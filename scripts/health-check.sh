#!/bin/bash

# AERY Health Check Script
# Comprehensive health check for all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
GLOBE="🌐"
DATABASE="🗄️"
FIRE="🔥"

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    AERY Health Check                        ║"
    echo "║              Comprehensive Service Status                   ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_section() {
    echo -e "${CYAN}${1}${NC} $2"
    echo "────────────────────────────────────────────────────────────────"
}

print_success() {
    echo -e "${GREEN}${CHECK}${NC} $1"
}

print_error() {
    echo -e "${RED}${CROSS}${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}${WARNING}${NC} $1"
}

print_info() {
    echo -e "${BLUE}${INFO}${NC} $1"
}

check_docker() {
    print_section "${GEAR}" "Docker Environment"
    
    # Check if Docker is running
    if docker info &> /dev/null; then
        print_success "Docker is running"
    else
        print_error "Docker is not running"
        return 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        print_success "Docker Compose is available"
    else
        print_error "Docker Compose is not available"
        return 1
    fi
    
    echo ""
}

check_containers() {
    print_section "${FIRE}" "Container Status"
    
    # Get container status
    containers=$(docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}" 2>/dev/null || echo "")
    
    if [ -z "$containers" ]; then
        print_warning "No containers found. Run 'make dev' to start services."
        echo ""
        return 1
    fi
    
    echo "$containers"
    echo ""
    
    # Check individual containers
    services=("gateway" "workers" "postgres" "redis")
    
    for service in "${services[@]}"; do
        if docker-compose ps "$service" | grep -q "Up"; then
            print_success "$service container is running"
        else
            print_error "$service container is not running"
        fi
    done
    
    echo ""
}

check_services() {
    print_section "${GLOBE}" "Service Health"
    
    # Check Gateway
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Gateway (http://localhost:8000) is healthy"
    else
        print_error "Gateway (http://localhost:8000) is not responding"
    fi
    
    # Check Gateway API
    api_response=$(curl -s http://localhost:8000/health 2>/dev/null || echo "")
    if echo "$api_response" | grep -q "healthy"; then
        print_success "Gateway API is responding correctly"
    else
        print_warning "Gateway API response is unexpected"
    fi
    
    echo ""
}

check_database() {
    print_section "${DATABASE}" "Database Health"
    
    # Check PostgreSQL
    if docker-compose exec -T postgres pg_isready -U aery > /dev/null 2>&1; then
        print_success "PostgreSQL is ready"
    else
        print_error "PostgreSQL is not ready"
    fi
    
    # Check database connection
    if docker-compose exec -T postgres psql -U aery -d aery -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "Database connection is working"
    else
        print_error "Database connection failed"
    fi
    
    # Check Redis
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is responding"
    else
        print_error "Redis is not responding"
    fi
    
    echo ""
}

check_ports() {
    print_section "${GLOBE}" "Port Availability"
    
    ports=("8000:Gateway" "5432:PostgreSQL" "6379:Redis" "8080:PgAdmin" "8081:Redis UI" "8025:MailHog")
    
    for port_info in "${ports[@]}"; do
        port=$(echo "$port_info" | cut -d: -f1)
        service=$(echo "$port_info" | cut -d: -f2)
        
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            print_success "Port $port ($service) is in use"
        elif lsof -i ":$port" > /dev/null 2>&1; then
            print_success "Port $port ($service) is in use"
        else
            print_warning "Port $port ($service) is not in use"
        fi
    done
    
    echo ""
}

check_logs() {
    print_section "${INFO}" "Recent Logs"
    
    echo "Recent Gateway logs:"
    docker-compose logs --tail=5 gateway 2>/dev/null || echo "No gateway logs available"
    
    echo ""
    echo "Recent Worker logs:"
    docker-compose logs --tail=5 workers 2>/dev/null || echo "No worker logs available"
    
    echo ""
}

check_disk_space() {
    print_section "${DATABASE}" "System Resources"
    
    # Check disk space
    disk_usage=$(df -h . | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        print_success "Disk space is adequate ($disk_usage% used)"
    elif [ "$disk_usage" -lt 90 ]; then
        print_warning "Disk space is getting low ($disk_usage% used)"
    else
        print_error "Disk space is critically low ($disk_usage% used)"
    fi
    
    # Check Docker disk usage
    docker_size=$(docker system df --format "table {{.Type}}\t{{.TotalCount}}\t{{.Size}}" 2>/dev/null || echo "")
    if [ -n "$docker_size" ]; then
        echo ""
        echo "Docker disk usage:"
        echo "$docker_size"
    fi
    
    echo ""
}

run_api_test() {
    print_section "${FIRE}" "API Functionality Test"
    
    # Test health endpoint
    health_response=$(curl -s http://localhost:8000/health 2>/dev/null || echo "")
    if [ -n "$health_response" ]; then
        print_success "Health endpoint is responding"
        echo "Response: $health_response"
    else
        print_error "Health endpoint is not responding"
    fi
    
    echo ""
}

show_service_urls() {
    print_section "${GLOBE}" "Service URLs"
    
    echo -e "${GREEN}🔗 Gateway:${NC}       http://localhost:8000"
    echo -e "${GREEN}🗄️  PgAdmin:${NC}       http://localhost:8080"
    echo -e "${GREEN}🔴 Redis UI:${NC}      http://localhost:8081"
    echo -e "${GREEN}📧 MailHog:${NC}       http://localhost:8025"
    
    echo ""
    print_info "Default PgAdmin credentials: <EMAIL> / admin"
    print_info "Default database: aery / aery / aery_dev_password"
    
    echo ""
}

show_summary() {
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                        Summary                               ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    
    if [ "$ERRORS" -eq 0 ]; then
        print_success "All systems are healthy!"
        echo ""
        echo -e "${YELLOW}Quick Commands:${NC}"
        echo "  make dev-logs          # View logs"
        echo "  make test              # Run tests"
        echo "  make stop              # Stop services"
    else
        print_error "Found $ERRORS issues that need attention"
        echo ""
        echo -e "${YELLOW}Troubleshooting:${NC}"
        echo "  make stop && make dev  # Restart services"
        echo "  make clean             # Clean and rebuild"
        echo "  make dev-logs          # Check logs"
    fi
    
    echo ""
}

main() {
    ERRORS=0
    
    print_header
    
    check_docker || ((ERRORS++))
    check_containers || ((ERRORS++))
    check_services || ((ERRORS++))
    check_database || ((ERRORS++))
    check_ports
    check_disk_space
    run_api_test || ((ERRORS++))
    show_service_urls
    show_summary
}

# Run main function
main "$@"
