#!/bin/bash

# AERY - Script de instalación y configuración
# Este script configura el entorno completo de AERY

set -e  # Salir en caso de error

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funciones de utilidad
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar si el comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Verificar requisitos del sistema
check_requirements() {
    log_info "Verificando requisitos del sistema..."
    
    # Verificar Docker
    if ! command_exists docker; then
        log_error "Docker no está instalado. Por favor instala Docker primero."
        exit 1
    fi
    
    # Verificar Docker Compose
    if ! command_exists docker-compose && ! docker compose version >/dev/null 2>&1; then
        log_error "Docker Compose no está instalado. Por favor instala Docker Compose primero."
        exit 1
    fi
    
    # Verificar Deno
    if ! command_exists deno; then
        log_warning "Deno no está instalado. Instalando Deno..."
        curl -fsSL https://deno.land/x/install/install.sh | sh
        export PATH="$HOME/.deno/bin:$PATH"
        echo 'export PATH="$HOME/.deno/bin:$PATH"' >> ~/.bashrc
        echo 'export PATH="$HOME/.deno/bin:$PATH"' >> ~/.zshrc
    fi
    
    # Verificar Python
    if ! command_exists python3; then
        log_error "Python 3 no está instalado. Por favor instala Python 3.11 o superior."
        exit 1
    fi
    
    # Verificar versión de Python
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [[ $(echo "$python_version < 3.11" | bc -l) -eq 1 ]]; then
        log_error "Se requiere Python 3.11 o superior. Versión actual: $python_version"
        exit 1
    fi
    
    log_success "Todos los requisitos están satisfechos"
}

# Configurar variables de entorno
setup_environment() {
    log_info "Configurando variables de entorno..."
    
    if [ ! -f .env ]; then
        log_info "Copiando archivo de configuración de ejemplo..."
        cp .env.example .env
        
        # Generar secretos aleatorios
        jwt_secret=$(openssl rand -hex 32)
        postgres_password=$(openssl rand -hex 16)
        
        # Actualizar .env con valores generados
        sed -i.bak "s/your-super-secure-jwt-secret-change-this-in-production/$jwt_secret/g" .env
        sed -i.bak "s/aery_secure_password/$postgres_password/g" .env
        
        # Limpiar archivo de backup
        rm .env.bak
        
        log_warning "Archivo .env creado. Por favor configura OPENROUTER_API_KEY antes de continuar."
        log_warning "Puedes obtener una API key en: https://openrouter.ai/"
    else
        log_info "Archivo .env ya existe"
    fi
}

# Crear directorios necesarios
setup_directories() {
    log_info "Creando directorios necesarios..."
    
    directories=(
        "logs"
        "artifacts"
        "artifacts/screenshots"
        "artifacts/downloads"
        "temp"
        "config/ssl"
        "config/grafana/provisioning/dashboards"
        "config/grafana/provisioning/datasources"
        "config/grafana/dashboards"
        "data/redis"
        "data/postgres"
        "data/prometheus"
        "data/grafana"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "Creado directorio: $dir"
    done
    
    log_success "Directorios creados exitosamente"
}

# Configurar Grafana
setup_grafana() {
    log_info "Configurando Grafana..."
    
    # Crear datasource de Prometheus
    cat > config/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF

    # Crear configuración de dashboards
    cat > config/grafana/provisioning/dashboards/dashboards.yml << EOF
apiVersion: 1

providers:
  - name: 'AERY Dashboards'
    orgId: 1
    folder: 'AERY'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    log_success "Configuración de Grafana completada"
}

# Instalar dependencias de Python
install_python_deps() {
    log_info "Instalando dependencias de Python..."
    
    cd workers
    
    # Crear entorno virtual si no existe
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    # Activar entorno virtual
    source venv/bin/activate
    
    # Actualizar pip
    pip install --upgrade pip
    
    # Instalar dependencias
    pip install -r requirements.txt
    
    # Instalar Playwright browsers
    playwright install chromium
    
    cd ..
    
    log_success "Dependencias de Python instaladas"
}

# Configurar Deno
setup_deno() {
    log_info "Configurando Deno..."
    
    cd gateway
    
    # Cache de dependencias
    deno cache main.ts
    deno cache dev.ts
    
    cd ..
    
    log_success "Configuración de Deno completada"
}

# Construir imágenes Docker
build_docker_images() {
    log_info "Construyendo imágenes Docker..."
    
    # Construir imagen del gateway
    log_info "Construyendo imagen del gateway..."
    docker build -t aery-gateway ./gateway
    
    # Construir imagen de workers
    log_info "Construyendo imagen de workers..."
    docker build -t aery-workers ./workers
    
    log_success "Imágenes Docker construidas exitosamente"
}

# Inicializar base de datos
init_database() {
    log_info "Inicializando base de datos..."
    
    # Iniciar solo PostgreSQL para inicialización
    docker-compose up -d postgres
    
    # Esperar a que PostgreSQL esté listo
    log_info "Esperando a que PostgreSQL esté listo..."
    sleep 10
    
    # Verificar conexión
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T postgres pg_isready -U aery_user -d aery >/dev/null 2>&1; then
            log_success "PostgreSQL está listo"
            break
        fi
        
        log_info "Intento $attempt/$max_attempts - Esperando PostgreSQL..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "PostgreSQL no está respondiendo después de $max_attempts intentos"
        exit 1
    fi
    
    log_success "Base de datos inicializada"
}

# Verificar servicios
verify_services() {
    log_info "Verificando servicios..."
    
    # Iniciar todos los servicios
    docker-compose up -d
    
    # Esperar un momento para que los servicios se inicien
    sleep 15
    
    # Verificar servicios
    services=("redis" "postgres" "gateway")
    
    for service in "${services[@]}"; do
        if docker-compose ps | grep -q "$service.*Up"; then
            log_success "Servicio $service está ejecutándose"
        else
            log_error "Servicio $service no está ejecutándose"
        fi
    done
    
    # Verificar conectividad
    log_info "Verificando conectividad..."
    
    # Verificar gateway
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "Gateway responde correctamente"
    else
        log_warning "Gateway no responde en http://localhost:8000/health"
    fi
    
    # Verificar Grafana
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        log_success "Grafana responde correctamente"
    else
        log_warning "Grafana no responde en http://localhost:3000"
    fi
}

# Mostrar información final
show_final_info() {
    log_success "¡Instalación de AERY completada!"
    echo
    echo "=== INFORMACIÓN DE ACCESO ==="
    echo "Gateway API: http://localhost:8000"
    echo "Grafana: http://localhost:3000 (admin/admin)"
    echo "Prometheus: http://localhost:9090"
    echo "Redis Insight: http://localhost:8001"
    echo "Portainer: http://localhost:9000"
    echo
    echo "=== COMANDOS ÚTILES ==="
    echo "Iniciar servicios: docker-compose up -d"
    echo "Detener servicios: docker-compose down"
    echo "Ver logs: docker-compose logs -f [servicio]"
    echo "Reiniciar servicio: docker-compose restart [servicio]"
    echo
    echo "=== CONFIGURACIÓN PENDIENTE ==="
    echo "1. Configura OPENROUTER_API_KEY en el archivo .env"
    echo "2. Reinicia los servicios: docker-compose restart"
    echo "3. Revisa la documentación en docs/"
    echo
    log_warning "No olvides configurar tu API key de OpenRouter antes de usar el sistema"
}

# Función principal
main() {
    echo "=== AERY - Instalación y Configuración ==="
    echo
    
    # Verificar si estamos en el directorio correcto
    if [ ! -f "docker-compose.yml" ]; then
        log_error "Este script debe ejecutarse desde el directorio raíz del proyecto AERY"
        exit 1
    fi
    
    # Ejecutar pasos de instalación
    check_requirements
    setup_environment
    setup_directories
    setup_grafana
    
    # Preguntar si instalar dependencias localmente
    read -p "¿Deseas instalar dependencias localmente para desarrollo? (y/N): " install_local
    if [[ $install_local =~ ^[Yy]$ ]]; then
        install_python_deps
        setup_deno
    fi
    
    # Construir e inicializar
    build_docker_images
    init_database
    verify_services
    
    # Mostrar información final
    show_final_info
}

# Ejecutar función principal
main "$@"