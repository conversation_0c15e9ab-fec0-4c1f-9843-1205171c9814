#!/bin/bash

# Script de inicialización completa de la base de datos de modelos
# Ejecuta todos los scripts necesarios para configurar el sistema de modelos dinámicos

set -e

echo "🚀 Inicializando sistema de modelos dinámicos..."

# Variables de configuración
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-aery_dev}
DB_USER=${DB_USER:-postgres}
DB_PASSWORD=${DB_PASSWORD:-password}

# Función para ejecutar SQL
execute_sql() {
    local file=$1
    echo "📄 Ejecutando: $file"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -f "$file"
}

# Función para verificar conexión
check_connection() {
    echo "🔍 Verificando conexión a PostgreSQL..."
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER -c "SELECT version();" > /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ Conexión establecida correctamente"
    else
        echo "❌ Error de conexión a PostgreSQL"
        exit 1
    fi
}

# Función para verificar Redis
check_redis() {
    echo "🔍 Verificando conexión a Redis..."
    if command -v redis-cli &> /dev/null; then
        redis-cli ping > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "✅ Redis disponible"
        else
            echo "⚠️  Redis no disponible, el cache no funcionará"
        fi
    else
        echo "⚠️  redis-cli no encontrado"
    fi
}

# Verificar dependencias
echo "🔧 Verificando dependencias..."
check_connection
check_redis

# Ejecutar scripts en orden
echo "📊 Creando esquema de modelos..."

# 1. Crear tablas básicas
execute_sql "/Users/<USER>/Proyectos/AERY/shared/database/models-schema.sql"

# 2. Insertar datos iniciales de proveedores
echo "🏢 Insertando proveedores..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
-- Insertar proveedores de modelos
INSERT INTO model_providers (name, display_name, api_base_url, api_key_env_var, enabled, rate_limit_per_minute, cost_per_1k_tokens, supported_features) 
VALUES 
('openai', 'OpenAI', 'https://api.openai.com/v1', 'OPENAI_API_KEY', true, 3500, 0.002, '["chat", "completion", "embedding", "vision"]'),
('anthropic', 'Anthropic', 'https://api.anthropic.com', 'ANTHROPIC_API_KEY', true, 4000, 0.008, '["chat", "completion"]'),
('openrouter', 'OpenRouter', 'https://openrouter.ai/api/v1', 'OPENROUTER_API_KEY', true, 200, 0.001, '["chat", "completion"]'),
('cohere', 'Cohere', 'https://api.cohere.ai/v1', 'COHERE_API_KEY', true, 1000, 0.001, '["chat", "completion", "embedding"]')
ON CONFLICT (name) DO UPDATE SET
display_name = EXCLUDED.display_name,
api_base_url = EXCLUDED.api_base_url,
updated_at = NOW();
EOF

# 3. Insertar categorías
echo "📂 Insertando categorías..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
INSERT INTO model_categories (name, display_name, description, enabled) 
VALUES 
('general', 'General Purpose', 'Modelos de propósito general para tareas variadas', true),
('code', 'Code Generation', 'Modelos especializados en generación de código', true),
('analysis', 'Data Analysis', 'Modelos optimizados para análisis de datos', true),
('creative', 'Creative Writing', 'Modelos para escritura creativa y contenido', true),
('research', 'Research & RAG', 'Modelos para investigación y sistemas RAG', true),
('fast', 'Fast Response', 'Modelos optimizados para respuestas rápidas', true)
ON CONFLICT (name) DO UPDATE SET
display_name = EXCLUDED.display_name,
description = EXCLUDED.description,
updated_at = NOW();
EOF

# 4. Insertar modelos
echo "🤖 Insertando modelos..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
-- Modelos OpenAI
INSERT INTO ai_models (name, display_name, provider_id, category_id, model_type, context_length, max_output_tokens, cost_per_1k_input, cost_per_1k_output, enabled, capabilities)
SELECT 
    'gpt-4o',
    'GPT-4o',
    p.id,
    c.id,
    'chat',
    128000,
    4096,
    0.005,
    0.015,
    true,
    '["text", "vision", "function_calling"]'
FROM model_providers p, model_categories c
WHERE p.name = 'openai' AND c.name = 'general';

INSERT INTO ai_models (name, display_name, provider_id, category_id, model_type, context_length, max_output_tokens, cost_per_1k_input, cost_per_1k_output, enabled, capabilities)
SELECT 
    'gpt-4o-mini',
    'GPT-4o Mini',
    p.id,
    c.id,
    'chat',
    128000,
    16384,
    0.00015,
    0.0006,
    true,
    '["text", "vision", "function_calling"]'
FROM model_providers p, model_categories c
WHERE p.name = 'openai' AND c.name = 'fast';

-- Modelos Anthropic
INSERT INTO ai_models (name, display_name, provider_id, category_id, model_type, context_length, max_output_tokens, cost_per_1k_input, cost_per_1k_output, enabled, capabilities)
SELECT 
    'claude-3-5-sonnet-20241022',
    'Claude 3.5 Sonnet',
    p.id,
    c.id,
    'chat',
    200000,
    8192,
    0.003,
    0.015,
    true,
    '["text", "vision", "function_calling"]'
FROM model_providers p, model_categories c
WHERE p.name = 'anthropic' AND c.name = 'general';

INSERT INTO ai_models (name, display_name, provider_id, category_id, model_type, context_length, max_output_tokens, cost_per_1k_input, cost_per_1k_output, enabled, capabilities)
SELECT 
    'claude-3-haiku-20240307',
    'Claude 3 Haiku',
    p.id,
    c.id,
    'chat',
    200000,
    4096,
    0.00025,
    0.00125,
    true,
    '["text", "vision"]'
FROM model_providers p, model_categories c
WHERE p.name = 'anthropic' AND c.name = 'fast';

-- Insertar solo si no existen (usar ON CONFLICT)
EOF

# 5. Insertar configuraciones de agentes
echo "🤖 Configurando agentes..."
execute_sql "/Users/<USER>/Proyectos/AERY/shared/database/agent-configs-seed.sql"

# 6. Configurar cache Redis
echo "📋 Configurando cache Redis..."
if command -v redis-cli &> /dev/null && redis-cli ping > /dev/null 2>&1; then
    redis-cli << 'EOF'
# Configurar TTL por defecto para cache de modelos
SET model_cache_ttl 3600
SET provider_cache_ttl 7200  
SET agent_config_cache_ttl 1800

# Limpiar cache existente
FLUSHDB
EOF
    echo "✅ Cache Redis configurado"
else
    echo "⚠️  Redis no disponible, saltando configuración de cache"
fi

# 7. Crear funciones de utilidad
echo "⚙️  Creando funciones de utilidad..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
-- Función para obtener el modelo por defecto de un proveedor
CREATE OR REPLACE FUNCTION get_default_model(provider_name TEXT)
RETURNS TABLE(model_id UUID, model_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT m.id, m.name
    FROM ai_models m
    JOIN model_providers p ON m.provider_id = p.id
    WHERE p.name = provider_name 
    AND m.enabled = true
    ORDER BY m.created_at ASC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Función para obtener estadísticas de uso de modelos
CREATE OR REPLACE FUNCTION get_model_usage_stats()
RETURNS TABLE(
    model_name TEXT,
    provider_name TEXT,
    total_requests BIGINT,
    total_tokens BIGINT,
    avg_response_time NUMERIC,
    last_used TIMESTAMP
) AS $$
BEGIN
    -- Esta función se implementará cuando tengamos tabla de logs
    RETURN QUERY
    SELECT 
        m.name::TEXT as model_name,
        p.display_name::TEXT as provider_name,
        0::BIGINT as total_requests,
        0::BIGINT as total_tokens,
        0::NUMERIC as avg_response_time,
        NOW() as last_used
    FROM ai_models m
    JOIN model_providers p ON m.provider_id = p.id
    WHERE m.enabled = true;
END;
$$ LANGUAGE plpgsql;

-- Función para validar configuración de agente
CREATE OR REPLACE FUNCTION validate_agent_config(agent_type_param TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    config_exists BOOLEAN;
    primary_model_valid BOOLEAN;
    fallback_model_valid BOOLEAN;
BEGIN
    -- Verificar que existe la configuración
    SELECT EXISTS(
        SELECT 1 FROM agent_configurations 
        WHERE agent_type = agent_type_param AND enabled = true
    ) INTO config_exists;
    
    IF NOT config_exists THEN
        RETURN FALSE;
    END IF;
    
    -- Verificar que el modelo primario existe y está habilitado
    SELECT EXISTS(
        SELECT 1 FROM agent_configurations ac
        JOIN ai_models m ON ac.primary_model_id = m.id
        WHERE ac.agent_type = agent_type_param 
        AND m.enabled = true
    ) INTO primary_model_valid;
    
    -- Verificar modelo fallback si existe
    SELECT COALESCE((
        SELECT m.enabled
        FROM agent_configurations ac
        JOIN ai_models m ON ac.fallback_model_id = m.id
        WHERE ac.agent_type = agent_type_param
    ), true) INTO fallback_model_valid;
    
    RETURN primary_model_valid AND fallback_model_valid;
END;
$$ LANGUAGE plpgsql;
EOF

# 8. Insertar datos de prueba
echo "🧪 Insertando datos de prueba..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
-- Simular algunos logs de uso
INSERT INTO model_usage_logs (model_id, agent_type, tokens_used, response_time_ms, success, created_at)
SELECT 
    m.id,
    ac.agent_type,
    FLOOR(RANDOM() * 2000 + 100)::INTEGER,
    FLOOR(RANDOM() * 3000 + 500)::INTEGER,
    RANDOM() > 0.1,
    NOW() - (RANDOM() * INTERVAL '30 days')
FROM ai_models m
CROSS JOIN agent_configurations ac
WHERE m.enabled = true
AND RANDOM() > 0.7  -- Solo algunos registros
LIMIT 50;
EOF

# 9. Verificar instalación
echo "✅ Verificando instalación..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -U $DB_USER << 'EOF'
-- Resumen de la instalación
SELECT 'Proveedores instalados' as tipo, COUNT(*)::TEXT as cantidad FROM model_providers WHERE enabled = true
UNION ALL
SELECT 'Categorías instaladas', COUNT(*)::TEXT FROM model_categories WHERE enabled = true  
UNION ALL
SELECT 'Modelos instalados', COUNT(*)::TEXT FROM ai_models WHERE enabled = true
UNION ALL
SELECT 'Agentes configurados', COUNT(*)::TEXT FROM agent_configurations WHERE enabled = true;

-- Verificar configuraciones de agentes
SELECT 
    ac.display_name,
    m.name as primary_model,
    fm.name as fallback_model,
    CASE WHEN validate_agent_config(ac.agent_type) THEN '✅' ELSE '❌' END as valid
FROM agent_configurations ac
LEFT JOIN ai_models m ON ac.primary_model_id = m.id
LEFT JOIN ai_models fm ON ac.fallback_model_id = fm.id
WHERE ac.enabled = true
ORDER BY ac.display_name;
EOF

echo ""
echo "🎉 ¡Inicialización completada exitosamente!"
echo ""
echo "📋 Resumen:"
echo "  - Sistema de modelos dinámicos configurado"
echo "  - Proveedores: OpenAI, Anthropic, OpenRouter, Cohere"
echo "  - Modelos de alta calidad y económicos disponibles"
echo "  - Agentes del sistema configurados con modelos apropiados"
echo "  - Cache Redis configurado (si está disponible)"
echo "  - Funciones de utilidad creadas"
echo ""
echo "🚀 El sistema está listo para usar!"
echo ""
echo "📚 Próximos pasos:"
echo "  1. Configurar las API keys en las variables de entorno"
echo "  2. Reiniciar el servidor backend para cargar la nueva configuración"
echo "  3. Verificar la UI de modelos en /models"
echo "  4. Configurar asignaciones específicas de modelos por agente"
echo ""
