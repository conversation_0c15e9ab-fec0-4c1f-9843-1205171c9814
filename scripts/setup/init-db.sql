-- Script de inicialización de la base de datos AERY
-- PostgreSQL Database Setup

-- =============================================================================
-- CONFIGURACIÓN INICIAL
-- =============================================================================

-- Crear extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Configurar timezone
SET timezone = 'UTC';

-- =============================================================================
-- ESQUEMAS
-- =============================================================================

-- Esquema principal para AERY
CREATE SCHEMA IF NOT EXISTS aery;

-- Esquema para métricas y logs
CREATE SCHEMA IF NOT EXISTS metrics;

-- Esquema para auditoría
CREATE SCHEMA IF NOT EXISTS audit;

-- =============================================================================
-- TIPOS PERSONALIZADOS
-- =============================================================================

-- Tipo para estado de tareas
CREATE TYPE aery.task_status AS ENUM (
    'pending',
    'processing',
    'completed',
    'failed',
    'cancelled',
    'timeout'
);

-- Tipo para planes de usuario
CREATE TYPE aery.user_plan AS ENUM (
    'free',
    'basic',
    'pro',
    'enterprise'
);

-- Tipo para roles de usuario
CREATE TYPE aery.user_role AS ENUM (
    'user',
    'admin',
    'developer',
    'system'
);

-- Tipo para tipos de métricas
CREATE TYPE metrics.metric_type AS ENUM (
    'counter',
    'gauge',
    'histogram',
    'summary'
);

-- =============================================================================
-- TABLAS PRINCIPALES
-- =============================================================================

-- Tabla de usuarios
CREATE TABLE aery.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    plan aery.user_plan DEFAULT 'free',
    role aery.user_role DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'
);

-- Tabla de API keys
CREATE TABLE aery.api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES aery.users(id) ON DELETE CASCADE,
    key_hash VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Tabla de tareas
CREATE TABLE aery.tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES aery.users(id) ON DELETE CASCADE,
    api_key_id UUID REFERENCES aery.api_keys(id) ON DELETE SET NULL,
    instruction TEXT NOT NULL,
    instruction_hash VARCHAR(64) NOT NULL,
    status aery.task_status DEFAULT 'pending',
    priority INTEGER DEFAULT 5,
    worker_id VARCHAR(100),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    timeout_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    metadata JSONB DEFAULT '{}'
);

-- Tabla de resultados de tareas
CREATE TABLE aery.task_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID NOT NULL REFERENCES aery.tasks(id) ON DELETE CASCADE,
    success BOOLEAN NOT NULL,
    result JSONB,
    error_message TEXT,
    error_code VARCHAR(50),
    execution_time_ms INTEGER,
    screenshots JSONB DEFAULT '[]',
    artifacts JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de pre-scripts optimizados
CREATE TABLE aery.prescripts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instruction_hash VARCHAR(64) UNIQUE NOT NULL,
    script JSONB NOT NULL,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_success_at TIMESTAMP WITH TIME ZONE,
    last_failure_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- =============================================================================
-- TABLAS DE MÉTRICAS
-- =============================================================================

-- Tabla de métricas del sistema
CREATE TABLE metrics.system_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_type metrics.metric_type NOT NULL,
    value NUMERIC NOT NULL,
    labels JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(100) NOT NULL
);

-- Tabla de métricas de workers
CREATE TABLE metrics.worker_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    worker_id VARCHAR(100) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    value NUMERIC NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Tabla de métricas de tareas
CREATE TABLE metrics.task_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES aery.tasks(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    value NUMERIC NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- =============================================================================
-- TABLAS DE AUDITORÍA
-- =============================================================================

-- Tabla de logs de auditoría
CREATE TABLE audit.audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES aery.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- ÍNDICES
-- =============================================================================

-- Índices para usuarios
CREATE INDEX idx_users_email ON aery.users(email);
CREATE INDEX idx_users_username ON aery.users(username);
CREATE INDEX idx_users_plan ON aery.users(plan);
CREATE INDEX idx_users_created_at ON aery.users(created_at);

-- Índices para API keys
CREATE INDEX idx_api_keys_user_id ON aery.api_keys(user_id);
CREATE INDEX idx_api_keys_key_hash ON aery.api_keys(key_hash);
CREATE INDEX idx_api_keys_is_active ON aery.api_keys(is_active);
CREATE INDEX idx_api_keys_expires_at ON aery.api_keys(expires_at);

-- Índices para tareas
CREATE INDEX idx_tasks_user_id ON aery.tasks(user_id);
CREATE INDEX idx_tasks_status ON aery.tasks(status);
CREATE INDEX idx_tasks_created_at ON aery.tasks(created_at);
CREATE INDEX idx_tasks_instruction_hash ON aery.tasks(instruction_hash);
CREATE INDEX idx_tasks_worker_id ON aery.tasks(worker_id);
CREATE INDEX idx_tasks_priority ON aery.tasks(priority);

-- Índices para resultados
CREATE INDEX idx_task_results_task_id ON aery.task_results(task_id);
CREATE INDEX idx_task_results_success ON aery.task_results(success);
CREATE INDEX idx_task_results_created_at ON aery.task_results(created_at);

-- Índices para pre-scripts
CREATE INDEX idx_prescripts_instruction_hash ON aery.prescripts(instruction_hash);
CREATE INDEX idx_prescripts_success_count ON aery.prescripts(success_count);
CREATE INDEX idx_prescripts_is_active ON aery.prescripts(is_active);

-- Índices para métricas
CREATE INDEX idx_system_metrics_timestamp ON metrics.system_metrics(timestamp);
CREATE INDEX idx_system_metrics_name ON metrics.system_metrics(metric_name);
CREATE INDEX idx_worker_metrics_worker_id ON metrics.worker_metrics(worker_id);
CREATE INDEX idx_worker_metrics_timestamp ON metrics.worker_metrics(timestamp);
CREATE INDEX idx_task_metrics_task_id ON metrics.task_metrics(task_id);
CREATE INDEX idx_task_metrics_timestamp ON metrics.task_metrics(timestamp);

-- Índices para auditoría
CREATE INDEX idx_audit_logs_user_id ON audit.audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit.audit_logs(action);
CREATE INDEX idx_audit_logs_timestamp ON audit.audit_logs(timestamp);

-- =============================================================================
-- FUNCIONES Y TRIGGERS
-- =============================================================================

-- Función para actualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON aery.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON aery.tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prescripts_updated_at BEFORE UPDATE ON aery.prescripts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Función para limpiar métricas antiguas
CREATE OR REPLACE FUNCTION cleanup_old_metrics()
RETURNS void AS $$
BEGIN
    -- Eliminar métricas del sistema más antiguas de 30 días
    DELETE FROM metrics.system_metrics 
    WHERE timestamp < NOW() - INTERVAL '30 days';
    
    -- Eliminar métricas de workers más antiguas de 7 días
    DELETE FROM metrics.worker_metrics 
    WHERE timestamp < NOW() - INTERVAL '7 days';
    
    -- Eliminar métricas de tareas más antiguas de 7 días
    DELETE FROM metrics.task_metrics 
    WHERE timestamp < NOW() - INTERVAL '7 days';
    
    -- Eliminar logs de auditoría más antiguos de 90 días
    DELETE FROM audit.audit_logs 
    WHERE timestamp < NOW() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- DATOS INICIALES
-- =============================================================================

-- Usuario administrador por defecto
INSERT INTO aery.users (email, username, password_hash, plan, role, is_active, email_verified)
VALUES (
    '<EMAIL>',
    'admin',
    crypt('admin123', gen_salt('bf')),
    'enterprise',
    'admin',
    true,
    true
) ON CONFLICT (email) DO NOTHING;

-- Usuario de sistema para workers
INSERT INTO aery.users (email, username, password_hash, plan, role, is_active, email_verified)
VALUES (
    '<EMAIL>',
    'system',
    crypt('system123', gen_salt('bf')),
    'enterprise',
    'system',
    true,
    true
) ON CONFLICT (email) DO NOTHING;

-- =============================================================================
-- PERMISOS
-- =============================================================================

-- Otorgar permisos al usuario de la aplicación
GRANT USAGE ON SCHEMA aery TO aery_user;
GRANT USAGE ON SCHEMA metrics TO aery_user;
GRANT USAGE ON SCHEMA audit TO aery_user;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA aery TO aery_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA metrics TO aery_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA audit TO aery_user;

GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA aery TO aery_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA metrics TO aery_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA audit TO aery_user;

-- Permisos para funciones
GRANT EXECUTE ON FUNCTION update_updated_at_column() TO aery_user;
GRANT EXECUTE ON FUNCTION cleanup_old_metrics() TO aery_user;

-- =============================================================================
-- CONFIGURACIÓN FINAL
-- =============================================================================

-- Configurar autovacuum para tablas de alta frecuencia
ALTER TABLE metrics.system_metrics SET (autovacuum_vacuum_scale_factor = 0.1);
ALTER TABLE metrics.worker_metrics SET (autovacuum_vacuum_scale_factor = 0.1);
ALTER TABLE metrics.task_metrics SET (autovacuum_vacuum_scale_factor = 0.1);

-- Configurar estadísticas extendidas
CREATE STATISTICS IF NOT EXISTS tasks_status_user_stats ON status, user_id FROM aery.tasks;
CREATE STATISTICS IF NOT EXISTS metrics_name_timestamp_stats ON metric_name, timestamp FROM metrics.system_metrics;

COMMIT;