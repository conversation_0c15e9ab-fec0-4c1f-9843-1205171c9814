#!/usr/bin/env python3
"""
Script de migración para setup inicial de modelos dinámicos con PostgreSQL y Redis
"""

import asyncio
import asyncpg
import redis
import json
import os
from datetime import datetime
from loguru import logger
import sys


async def setup_database():
    """Configura la base de datos con el nuevo esquema de modelos"""
    
    try:
        # Conectar a PostgreSQL
        database_url = os.getenv(
            'DATABASE_URL', 
            'postgresql://aery_user:aery_password@localhost:5432/aery_db'
        )
        
        conn = await asyncpg.connect(database_url)
        logger.info("✅ Conectado a PostgreSQL")
        
        # Leer y ejecutar el esquema de modelos
        schema_path = 'shared/database/models_schema.sql'
        if os.path.exists(schema_path):
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_sql = f.read()
            
            await conn.execute(schema_sql)
            logger.info("✅ Esquema de modelos creado")
        else:
            logger.warning("⚠️ Archivo de esquema no encontrado, saltando...")
        
        # Leer y ejecutar los datos iniciales
        seed_path = 'shared/database/models_seed.sql'
        if os.path.exists(seed_path):
            with open(seed_path, 'r', encoding='utf-8') as f:
                seed_sql = f.read()
            
            await conn.execute(seed_sql)
            logger.info("✅ Datos iniciales de modelos insertados")
        else:
            logger.warning("⚠️ Archivo de datos iniciales no encontrado, saltando...")
        
        await conn.close()
        logger.info("✅ Setup de base de datos completado")
        
    except Exception as e:
        logger.error(f"❌ Error en setup de base de datos: {e}")
        return False
    
    return True


def setup_redis():
    """Configura Redis para cache de modelos"""
    
    try:
        # Conectar a Redis
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
        r = redis.from_url(redis_url, decode_responses=True)
        
        # Test de conexión
        r.ping()
        logger.info("✅ Conectado a Redis")
        
        # Limpiar cache anterior de modelos si existe
        pattern = "aery:models:*"
        keys = r.keys(pattern)
        if keys:
            r.delete(*keys)
            logger.info(f"🧹 Limpiado cache anterior: {len(keys)} claves")
        
        # Configurar TTL por defecto
        r.setex("aery:models:cache_ttl", 300, "300")
        logger.info("✅ Setup de Redis completado")
        
        r.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Error en setup de Redis: {e}")
        return False


def migrate_legacy_config():
    """Migra configuración legacy de archivos JSON a base de datos"""
    
    try:
        # Buscar archivo de configuración legacy
        legacy_config_paths = [
            'server/workers/config/model_config.json',
            'server/workers/src/config/model_config.json'
        ]
        
        legacy_config = None
        for path in legacy_config_paths:
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    legacy_config = json.load(f)
                logger.info(f"📁 Encontrada configuración legacy en: {path}")
                break
        
        if not legacy_config:
            logger.info("ℹ️ No se encontró configuración legacy, saltando migración")
            return True
        
        # TODO: Implementar migración de modelos legacy a base de datos
        # Esta función se puede expandir más tarde para migrar automáticamente
        # los modelos del archivo JSON a la base de datos
        
        logger.info("✅ Migración de configuración legacy completada")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error en migración legacy: {e}")
        return False


def create_backup():
    """Crea backup de configuración actual"""
    
    try:
        backup_dir = "backups"
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"{backup_dir}/models_backup_{timestamp}.json"
        
        # Buscar configuración actual para backup
        config_paths = [
            'server/workers/config/model_config.json',
            'server/workers/src/config/model_config.json'
        ]
        
        for path in config_paths:
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as src:
                    config = json.load(src)
                
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    json.dump(config, dst, indent=2, ensure_ascii=False)
                
                logger.info(f"💾 Backup creado: {backup_file}")
                return True
        
        logger.info("ℹ️ No se encontró configuración para backup")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creando backup: {e}")
        return False


async def verify_setup():
    """Verifica que el setup se completó correctamente"""
    
    try:
        # Verificar base de datos
        database_url = os.getenv(
            'DATABASE_URL', 
            'postgresql://aery_user:aery_password@localhost:5432/aery_db'
        )
        
        conn = await asyncpg.connect(database_url)
        
        # Verificar tablas
        tables_query = """
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('model_providers', 'model_categories', 'ai_models', 'agent_configurations')
        """
        tables = await conn.fetchval("SELECT COUNT(*) FROM (" + tables_query + ") t")
        
        if tables >= 4:
            logger.info("✅ Tablas de modelos verificadas")
        else:
            logger.error("❌ Faltan tablas de modelos")
            return False
        
        # Verificar datos iniciales
        providers_count = await conn.fetchval("SELECT COUNT(*) FROM model_providers")
        models_count = await conn.fetchval("SELECT COUNT(*) FROM ai_models")
        agents_count = await conn.fetchval("SELECT COUNT(*) FROM agent_configurations")
        
        logger.info(f"📊 Datos verificados: {providers_count} proveedores, {models_count} modelos, {agents_count} agentes")
        
        await conn.close()
        
        # Verificar Redis
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
        r = redis.from_url(redis_url, decode_responses=True)
        r.ping()
        r.close()
        logger.info("✅ Redis verificado")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error en verificación: {e}")
        return False


async def main():
    """Función principal de migración"""
    
    logger.info("🚀 Iniciando setup de modelos dinámicos con PostgreSQL y Redis")
    
    # 1. Crear backup
    logger.info("1️⃣ Creando backup de configuración actual...")
    if not create_backup():
        logger.error("❌ Fallo en backup")
        sys.exit(1)
    
    # 2. Setup de base de datos
    logger.info("2️⃣ Configurando base de datos...")
    if not await setup_database():
        logger.error("❌ Fallo en setup de base de datos")
        sys.exit(1)
    
    # 3. Setup de Redis
    logger.info("3️⃣ Configurando Redis...")
    if not setup_redis():
        logger.error("❌ Fallo en setup de Redis")
        sys.exit(1)
    
    # 4. Migrar configuración legacy
    logger.info("4️⃣ Migrando configuración legacy...")
    if not migrate_legacy_config():
        logger.error("❌ Fallo en migración legacy")
        sys.exit(1)
    
    # 5. Verificar setup
    logger.info("5️⃣ Verificando setup...")
    if not await verify_setup():
        logger.error("❌ Fallo en verificación")
        sys.exit(1)
    
    logger.info("🎉 Setup de modelos dinámicos completado exitosamente!")
    
    # Mostrar siguiente pasos
    print("\n" + "="*60)
    print("✨ SETUP COMPLETADO - PRÓXIMOS PASOS:")
    print("="*60)
    print("1. Reiniciar el servidor de workers para usar la nueva API")
    print("2. Actualizar el frontend para usar el nuevo store de modelos")
    print("3. Verificar que los endpoints /api/models funcionen correctamente")
    print("4. Configurar las variables de entorno necesarias:")
    print("   - DATABASE_URL (PostgreSQL)")
    print("   - REDIS_URL (Redis)")
    print("5. Los backups están en la carpeta 'backups/'")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
