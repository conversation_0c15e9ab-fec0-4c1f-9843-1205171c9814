# 🚀 AERY - API de Automatización de Navegador con IA

## 1. Visión General del Producto

AERY es una API de automatización de navegador impulsada por inteligencia artificial que permite ejecutar comandos en lenguaje natural para automatizar tareas web. El sistema combina la potencia de múltiples agentes IA con optimización de rendimiento mediante pre-scripts generados automáticamente.

El producto resuelve la complejidad de crear y mantener scripts de automatización web, permitiendo a los usuarios describir sus tareas en lenguaje natural mientras el sistema aprende y optimiza las ejecuciones posteriores para lograr un 90% de reducción en costos y tiempo.

Objetivo: Democratizar la automatización web mediante IA, reduciendo la barrera técnica y optimizando costos operacionales.

## 2. Características Principales

### 2.1 Roles de Usuario

| Rol              | Método de Registro        | Permisos Principales                                                |
| ---------------- | ------------------------- | ------------------------------------------------------------------- |
| Usuario Básico   | API Key                   | Puede ejecutar automatizaciones básicas, acceso a pre-scripts       |
| Usuario Avanzado | API Key                   | Acceso completo a agentes IA, métricas avanzadas, self-healing      |
| Administrador    | Configuración del sistema | Gestión de workers, monitoreo del sistema, configuración de agentes |

### 2.2 Módulos de Funcionalidad

Nuestros requisitos de la API consisten en las siguientes páginas principales:

1. **Gateway API**: endpoint principal para recibir requests, validación de entrada, enrutamiento de tareas.
2. **Panel de Control**: monitoreo de ejecuciones, métricas de rendimiento, gestión de pre-scripts.
3. **Configuración de Agentes**: configuración de agentes IA, parámetros de PocketFlow, proveedores LLM.
4. **Gestión de Colas**: visualización de colas Redis, estado de workers, distribución de cargas.
5. **Resultados y Artefactos**: visualización de screenshots, logs de ejecución, reportes de errores.
6. **Sistema de Aprendizaje**: gestión de pre-scripts, métricas de optimización, configuración de self-healing.

### 2.3 Detalles de Páginas

| Nombre de Página         | Nombre del Módulo           | Descripción de Funcionalidad                                                                                           |
| ------------------------ | --------------------------- | ---------------------------------------------------------------------------------------------------------------------- |
| Gateway API              | Endpoint Principal          | Recibir requests POST con instrucciones en lenguaje natural, validar formato y autenticación, generar task ID único    |
| Gateway API              | Procesador de Tareas        | Calcular hash de instrucción, verificar existencia de pre-script, decidir estrategia de ejecución (rápida vs completa) |
| Gateway API              | Enrutador de Colas          | Publicar tareas en Redis pub/sub, distribuir carga entre workers disponibles, manejar prioridades                      |
| Panel de Control         | Dashboard Principal         | Mostrar métricas en tiempo real, estado de workers, estadísticas de ejecución, gráficos de rendimiento                 |
| Panel de Control         | Monitor de Ejecuciones      | Listar ejecuciones activas y completadas, filtrar por estado/fecha, mostrar tiempo de ejecución y costos               |
| Panel de Control         | Gestión de Pre-scripts      | Visualizar pre-scripts generados, editar manualmente, eliminar scripts obsoletos, estadísticas de uso                  |
| Configuración de Agentes | Setup PocketFlow            | Configurar agentes especializados (Explorer, Tester, Validator, Analyzer), parámetros de coordinación                  |
| Configuración de Agentes | Proveedores LLM             | Configurar OpenRouter API keys, seleccionar modelos, configurar límites de tokens y costos                             |
| Configuración de Agentes | Estrategias de Ejecución    | Definir flujos secuenciales/paralelos, configurar timeouts, establecer criterios de confianza                          |
| Gestión de Colas         | Monitor Redis               | Visualizar colas activas, tamaño de cola, workers conectados, latencia de mensajes                                     |
| Gestión de Colas         | Configuración Workers       | Registrar/desregistrar workers Python, configurar capacidad, asignar tipos de tareas                                   |
| Gestión de Colas         | Balanceador de Carga        | Distribuir tareas según capacidad de workers, manejar failover, reintento automático                                   |
| Resultados y Artefactos  | Visualizador de Screenshots | Mostrar capturas de pantalla generadas, comparar antes/después, anotar elementos detectados                            |
| Resultados y Artefactos  | Logs de Ejecución           | Mostrar logs detallados de Playwright, errores de agentes IA, tiempos de respuesta                                     |
| Resultados y Artefactos  | Reportes de Errores         | Categorizar errores, sugerir soluciones, activar self-healing automático                                               |
| Sistema de Aprendizaje   | Generador de Pre-scripts    | Extraer acciones exitosas, crear scripts optimizados, validar compatibilidad                                           |
| Sistema de Aprendizaje   | Motor de Self-healing       | Detectar fallos de pre-scripts, analizar causas, aplicar correcciones con IA                                           |
| Sistema de Aprendizaje   | Métricas de Optimización    | Calcular ahorros de costo/tiempo, medir precisión de agentes, tendencias de mejora                                     |

## 3. Proceso Principal

### Flujo de Usuario Básico

1. El usuario envía una instrucción en lenguaje natural via POST al Gateway API
2. El sistema calcula el hash de la instrucción y verifica si existe un pre-script
3. Si existe pre-script: ejecuta directamente con Playwright (ruta rápida)
4. Si no existe: activa el sistema de agentes IA para procesamiento completo
5. Los agentes analizan la instrucción y generan una secuencia de acciones
6. Playwright ejecuta las acciones en el navegador
7. El sistema genera un pre-script para futuras ejecuciones
8. Se retorna el resultado con artefactos (screenshots, datos extraídos)

### Flujo de Self-healing

1. Un pre-script falla durante la ejecución
2. El sistema detecta el error y activa el modo self-healing
3. Los agentes IA analizan la causa del fallo (selector cambiado, estructura de página, etc.)
4. Se genera una corrección automática
5. Se reintenta la ejecución con la corrección aplicada
6. Si es exitoso, se actualiza el pre-script; si falla, se activa procesamiento completo

```mermaid
graph TD
    A[Usuario envía instrucción] --> B[Gateway API recibe request]
    B --> C{¿Pre-script existe?}
    C -->|Sí| D[Ejecución rápida con Playwright]
    C -->|No| E[Activar agentes IA]
    E --> F[Procesar con PocketFlow]
    F --> G[Generar acciones]
    G --> H[Ejecutar con Playwright]
    H --> I[Generar pre-script]
    D --> J{¿Ejecución exitosa?}
    J -->|Sí| K[Retornar resultado]
    J -->|No| L[Activar self-healing]
    L --> M[Analizar error con IA]
    M --> N[Aplicar corrección]
    N --> O[Reintentar ejecución]
    I --> K
    O --> K
```

## 4. Diseño de Interfaz de Usuario

### 4.1 Estilo de Diseño

* **Colores primarios**: Azul oscuro (#1a365d) para headers, Verde (#38a169) para estados exitosos

* **Colores secundarios**: Gris claro (#f7fafc) para backgrounds, Naranja (#ed8936) para alertas

* **Estilo de botones**: Redondeados con sombra sutil, efectos hover suaves

* **Fuente**: Inter, tamaños 14px para texto normal, 18px para títulos, 12px para metadatos

* **Estilo de layout**: Diseño de tarjetas con espaciado generoso, navegación lateral fija

* **Iconos**: Lucide icons para consistencia, emojis para estados y categorías

### 4.2 Resumen de Diseño de Páginas

| Nombre de Página         | Nombre del Módulo           | Elementos de UI                                                                                                                      |
| ------------------------ | --------------------------- | ------------------------------------------------------------------------------------------------------------------------------------ |
| Gateway API              | Endpoint Principal          | Formulario de prueba con textarea para instrucciones, botón "Ejecutar", indicador de carga, respuesta JSON formateada                |
| Panel de Control         | Dashboard Principal         | Grid de métricas con iconos, gráficos de línea para tendencias, tabla de ejecuciones recientes, indicadores de estado en tiempo real |
| Panel de Control         | Monitor de Ejecuciones      | Tabla paginada con filtros, badges de estado colorados, modal de detalles, botones de acción (reintentar, ver logs)                  |
| Configuración de Agentes | Setup PocketFlow            | Formulario de configuración con tabs, sliders para parámetros, preview de configuración, botón guardar con confirmación              |
| Gestión de Colas         | Monitor Redis               | Dashboard en tiempo real con WebSocket, gráficos de barras para colas, lista de workers con estado, métricas de latencia             |
| Resultados y Artefactos  | Visualizador de Screenshots | Galería de imágenes con zoom, comparador lado a lado, anotaciones interactivas, timeline de ejecución                                |
| Sistema de Aprendizaje   | Métricas de Optimización    | Gráficos de ahorro de costos, tablas de pre-scripts, indicadores de mejora, exportar reportes PDF                                    |

### 4.3 Responsividad

La aplicación está diseñada desktop-first con adaptación móvil completa. Incluye navegación colapsible en móviles, tablas responsivas con scroll horizontal, y optimización táctil para botones y controles interactivos.

## 5. Arquitectura Técnica

### 5.1 Stack Tecnológico

* **Gateway y Orquestador**: Deno con Fresh framework

* **Agentes IA**: Python con PocketFlow framework

* **Automatización**: Playwright para Node.js/Python

* **Colas**: Redis pub/sub para distribución de tareas

* **Proveedor LLM**: OpenRouter API

* **Base de datos**: PostgreSQL para persistencia, Redis para cache

* **Monitoreo**: Prometheus + Grafana

### 5.2 Componentes del Sistema

1. **Deno Gateway**: API REST que recibe requests, valida entrada, gestiona autenticación
2. **Redis Queue Manager**: Sistema pub/sub para distribuir tareas entre workers
3. **Python Workers**: Procesos que ejecutan agentes IA y automatización Playwright
4. **PocketFlow Agents**: Agentes especializados (Explorer, Tester, Validator, Analyzer)
5. **Pre-script Engine**: Sistema de cache y optimización de ejecuciones
6. **Self-healing System**: Motor de corrección automática con IA

### 5.3 Flujo de Datos

```mermaid
sequenceDiagram
    participant U as Usuario
    participant G as Deno Gateway
    participant R as Redis Queue
    participant W as Python Worker
    participant P as PocketFlow
    participant B as Playwright
    
    U->>G: POST /api/execute
    G->>G: Validar y generar task ID
    G->>R: Publicar tarea en cola
    R->>W: Distribuir a worker disponible
    W->>P: Activar agentes IA
    P->>P: Procesar instrucción
    P->>W: Retornar acciones
    W->>B: Ejecutar automatización
    B->>W: Resultado + artefactos
    W->>G: Enviar resultado
    G->>U: Respuesta HTTP
```

## 6. Criterios de Aceptación

### 6.1 Funcionalidad Core

* ✅ API acepta instrucciones en lenguaje natural

* ✅ Sistema genera pre-scripts automáticamente

* ✅ Ejecuciones posteriores son 10x más rápidas

* ✅ Self-healing funciona en 90% de casos

* ✅ Integración completa con PocketFlow

### 6.2 Rendimiento

* ✅ Primera ejecución: < 60 segundos

* ✅ Ejecuciones con pre-script: < 5 segundos

* ✅ Soporte para 100+ ejecuciones concurrentes

* ✅ Disponibilidad 99.9%

### 6.3 Calidad de Código

* ✅ Cobertura de tests > 80%

* ✅ Documentación API completa

* ✅ Principios SOLID aplicados

* ✅ Código mantenible y extensible

## 7. Casos de Uso Ejemplo

### Caso 1: E-commerce Testing

```json
{
  "instruction": "Ve a amazon.com, busca 'auriculares bluetooth', filtra por precio $50-150, añade el mejor valorado al carrito",
  "expected_result": "Producto añadido al carrito con screenshot de confirmación"
}
```

### Caso 2: Form Automation

```json
{
  "instruction": "Completa el formulario de contacto en ejemplo.com con nombre 'Juan Pérez', email '<EMAIL>' y mensaje 'Consulta sobre servicios'",
  "expected_result": "Formulario enviado exitosamente"
}
```

### Caso 3: Data Extraction

```json
{
  "instruction": "Extrae los precios de los primeros 10 productos de la categoría laptops en mercadolibre.com",
  "expected_result": "Array JSON
```

