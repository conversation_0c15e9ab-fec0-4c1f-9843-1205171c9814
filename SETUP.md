# AERY - Guía de Configuración e Instalación

¡Bienvenido a AERY! Esta guía te ayudará a configurar y ejecutar el sistema completo de automatización de navegador con IA.

## 🚀 Inicio Rápido

### 1. Prerrequisitos

Asegúrate de tener instalado:

- **Docker** (v20.10+) y **Docker Compose** (v2.0+)
- **Deno** (v1.40+) - Se instalará automáticamente si no está presente
- **Python** (v3.11+)
- **Git**
- **curl** (para verificaciones de salud)

### 2. Instalación Automática

```bash
# Clonar el repositorio
git clone <repository-url>
cd AERY

# Ejecutar script de instalación
./scripts/setup/install.sh
```

El script de instalación:
- ✅ Verifica prerrequisitos
- ✅ Configura variables de entorno
- ✅ Crea directorios necesarios
- ✅ Instala dependencias
- ✅ Construye imágenes Docker
- ✅ Inicializa la base de datos
- ✅ Verifica que todos los servicios funcionen

### 3. Configuración Manual (Alternativa)

Si prefieres configurar manualmente:

```bash
# 1. Copiar configuración de ejemplo
cp .env.example .env

# 2. Editar variables de entorno
nano .env  # Configura especialmente OPENROUTER_API_KEY

# 3. Crear directorios
mkdir -p logs artifacts temp config/ssl

# 4. Construir e iniciar servicios
docker-compose up --build -d

# 5. Verificar salud del sistema
curl http://localhost:8000/health
```

## 🔧 Configuración Detallada

### Variables de Entorno Críticas

Edita el archivo `.env` y configura:

```bash
# OBLIGATORIO: API Key de OpenRouter
OPENROUTER_API_KEY=your-openrouter-api-key-here

# RECOMENDADO: Cambiar secretos por defecto
JWT_SECRET=your-super-secure-jwt-secret
POSTGRES_PASSWORD=your-secure-password

# OPCIONAL: Configuración de modelos IA
AERY_INSTRUCTION_ANALYZER_MODEL=anthropic/claude-3-haiku
AERY_ACTION_PLANNER_MODEL=openai/gpt-4-turbo
```

### Obtener API Key de OpenRouter

1. Ve a [OpenRouter](https://openrouter.ai/)
2. Crea una cuenta
3. Genera una API key
4. Añade créditos a tu cuenta
5. Copia la API key al archivo `.env`

## 🐳 Servicios Docker

AERY incluye los siguientes servicios:

| Servicio | Puerto | Descripción |
|----------|--------|-------------|
| **Gateway** | 8000 | API principal y frontend |
| **Workers** | - | Procesadores de automatización (3 instancias) |
| **Redis** | 6379 | Cache y colas de tareas |
| **PostgreSQL** | 5432 | Base de datos principal |
| **Nginx** | 80, 443 | Proxy reverso y load balancer |
| **Prometheus** | 9090 | Recolección de métricas |
| **Grafana** | 3000 | Dashboards y visualización |
| **Redis Insight** | 8001 | Interfaz web para Redis |
| **Portainer** | 9000 | Gestión de contenedores |

### Comandos Útiles

```bash
# Iniciar todos los servicios
docker-compose up -d

# Ver logs en tiempo real
docker-compose logs -f

# Ver logs de un servicio específico
docker-compose logs -f gateway
docker-compose logs -f worker-1

# Reiniciar un servicio
docker-compose restart gateway

# Detener todos los servicios
docker-compose down

# Detener y eliminar volúmenes (CUIDADO: elimina datos)
docker-compose down -v

# Ver estado de servicios
docker-compose ps

# Ejecutar comando en contenedor
docker-compose exec gateway deno task health
docker-compose exec postgres psql -U aery_user -d aery
```

## 🧪 Verificación del Sistema

### Health Checks

```bash
# Verificar API principal
curl http://localhost:8000/health

# Verificar métricas
curl http://localhost:8000/metrics

# Verificar Grafana
curl http://localhost:3000

# Verificar Prometheus
curl http://localhost:9090
```

### Prueba de API

```bash
# Crear usuario de prueba (opcional)
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "testpass123"
  }'

# Ejecutar tarea de prueba
curl -X POST http://localhost:8000/api/v1/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "instruction": "Navega a google.com y toma una captura de pantalla",
    "options": {
      "screenshot": true,
      "timeout": 30000
    }
  }'
```

## 📊 Monitoreo y Dashboards

### Acceso a Interfaces Web

- **API Gateway**: http://localhost:8000
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Redis Insight**: http://localhost:8001
- **Portainer**: http://localhost:9000

### Dashboards Predefinidos

Grafana incluye dashboards para:
- 📈 **Sistema General**: Overview del estado del sistema
- 🔧 **Workers**: Métricas de procesamiento
- 🌐 **API**: Métricas de la API
- 💾 **Base de Datos**: Performance de PostgreSQL
- 🚀 **Redis**: Métricas de cache y colas

## 🔍 Troubleshooting

### Problemas Comunes

#### 1. Error "OPENROUTER_API_KEY not configured"
```bash
# Solución: Configurar API key en .env
echo "OPENROUTER_API_KEY=your-key-here" >> .env
docker-compose restart
```

#### 2. Puerto 8000 ya en uso
```bash
# Verificar qué proceso usa el puerto
lsof -i :8000

# Cambiar puerto en docker-compose.yml
# ports:
#   - "8001:8000"  # Cambiar 8000 por 8001
```

#### 3. Workers no procesan tareas
```bash
# Verificar logs de workers
docker-compose logs worker-1

# Verificar conexión a Redis
docker-compose exec worker-1 python -c "import redis; r=redis.from_url('redis://redis:6379'); print(r.ping())"

# Reiniciar workers
docker-compose restart worker-1 worker-2 worker-3
```

#### 4. Base de datos no conecta
```bash
# Verificar estado de PostgreSQL
docker-compose exec postgres pg_isready -U aery_user

# Ver logs de PostgreSQL
docker-compose logs postgres

# Reiniciar PostgreSQL
docker-compose restart postgres
```

#### 5. Navegador no se inicia en Workers
```bash
# Verificar instalación de Playwright
docker-compose exec worker-1 playwright install --dry-run

# Reinstalar navegadores
docker-compose exec worker-1 playwright install chromium

# Verificar permisos
docker-compose exec worker-1 ls -la /app
```

### Logs y Debugging

```bash
# Ver todos los logs
docker-compose logs

# Logs con timestamps
docker-compose logs -t

# Seguir logs en tiempo real
docker-compose logs -f --tail=100

# Logs de un servicio específico
docker-compose logs -f gateway

# Logs con filtro
docker-compose logs | grep ERROR
```

### Limpieza y Reset

```bash
# Limpiar contenedores parados
docker container prune

# Limpiar imágenes no usadas
docker image prune

# Reset completo (CUIDADO: elimina todos los datos)
docker-compose down -v
docker system prune -a
./scripts/setup/install.sh
```

## 🚀 Desarrollo

### Desarrollo Local

```bash
# Desarrollo del Gateway (Deno)
cd gateway
deno task dev  # Hot reload en puerto 8000

# Desarrollo de Workers (Python)
cd workers
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python main.py
```

### Testing

```bash
# Tests del Gateway
cd gateway
deno task test
deno task test:coverage

# Tests de Workers
cd workers
python -m pytest
python -m pytest --cov=.

# Tests de integración
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

### Contribuir

1. Fork el repositorio
2. Crea una rama para tu feature: `git checkout -b feature/nueva-funcionalidad`
3. Commit tus cambios: `git commit -am 'Añadir nueva funcionalidad'`
4. Push a la rama: `git push origin feature/nueva-funcionalidad`
5. Crea un Pull Request

## 📚 Documentación Adicional

- [📖 **API Documentation**](docs/api/README.md) - Documentación completa de la API
- [🏗️ **Architecture**](docs/architecture/README.md) - Arquitectura del sistema
- [🔧 **Configuration**](docs/configuration.md) - Configuración avanzada
- [🚀 **Deployment**](docs/deployment.md) - Guía de deployment
- [🔒 **Security**](docs/security.md) - Consideraciones de seguridad

## 🆘 Soporte

¿Necesitas ayuda?

- 📧 **Email**: <EMAIL>
- 💬 **Discord**: https://discord.gg/aery
- 🐛 **Issues**: https://github.com/aery/aery/issues
- 📖 **Docs**: https://docs.aery.ai

## 📄 Licencia

Este proyecto está licenciado bajo la Licencia MIT. Ver el archivo [LICENSE](LICENSE) para más detalles.

---

¡Gracias por usar AERY! 🎉