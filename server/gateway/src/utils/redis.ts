import { connect, Redis } from "redis";

export class RedisService {
  private static instance: Redis | null = null;
  private static connecting = false;

  /**
   * Obtiene la instancia singleton de Redis
   */
  static async getInstance(): Promise<Redis> {
    if (this.instance) {
      return this.instance;
    }

    if (this.connecting) {
      // Esperar a que termine la conexión en curso
      while (this.connecting) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
      return this.instance!;
    }

    this.connecting = true;

    try {
      const redisUrl = Deno.env.get("REDIS_URL") || "redis://localhost:6379";

      console.log("Conectando a Redis:", redisUrl.replace(/\/\/.*@/, "//***@"));

      this.instance = await connect({
        hostname: this.parseRedisUrl(redisUrl).hostname,
        port: this.parseRedisUrl(redisUrl).port,
        password: this.parseRedisUrl(redisUrl).password,
        db: this.parseRedisUrl(redisUrl).db,
      });

      console.log("✅ Conectado a Redis exitosamente");

      // Configurar event listeners
      // this.setupEventListeners();

      return this.instance;
    } catch (error) {
      console.error("❌ Error conectando a Redis:", error);
      this.instance = null;
      throw error;
    } finally {
      this.connecting = false;
    }
  }

  /**
   * Parsea la URL de Redis
   */
  private static parseRedisUrl(url: string) {
    const parsed = new URL(url);
    return {
      hostname: parsed.hostname || "localhost",
      port: parseInt(parsed.port) || 6379,
      password: parsed.password || undefined,
      db: parseInt(parsed.pathname.slice(1)) || 0,
    };
  }

  /**
   * Configura event listeners para la conexión
   */
  private static setupEventListeners() {
    if (!this.instance) return;

    // Manejar reconexión automática
    this.instance.on("error", (error) => {
      console.error("Redis connection error:", error);
      this.instance = null;
    });

    this.instance.on("connect", () => {
      console.log("Redis reconnected");
    });
  }

  /**
   * Cierra la conexión a Redis
   */
  static async close(): Promise<void> {
    if (this.instance) {
      try {
        await this.instance.quit();
        console.log("✅ Conexión a Redis cerrada");
      } catch (error) {
        console.error("Error cerrando conexión a Redis:", error);
      } finally {
        this.instance = null;
      }
    }
  }

  /**
   * Verifica si Redis está conectado
   */
  static async isConnected(): Promise<boolean> {
    try {
      if (!this.instance) {
        return false;
      }
      await this.instance.ping();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Obtiene estadísticas de Redis
   */
  static async getStats(): Promise<{
    connected: boolean;
    memory: string;
    clients: number;
    operations: number;
  }> {
    try {
      const redis = await this.getInstance();
      const info = await redis.info();

      // Parsear información básica
      const lines = info.split("\r\n");
      const stats: Record<string, string> = {};

      for (const line of lines) {
        if (line.includes(":")) {
          const [key, value] = line.split(":");
          stats[key] = value;
        }
      }

      return {
        connected: true,
        memory: stats.used_memory_human || "N/A",
        clients: parseInt(stats.connected_clients) || 0,
        operations: parseInt(stats.total_commands_processed) || 0,
      };
    } catch (error) {
      console.error("Error obteniendo stats de Redis:", error);
      return {
        connected: false,
        memory: "N/A",
        clients: 0,
        operations: 0,
      };
    }
  }

  /**
   * Limpia keys expiradas manualmente (útil para desarrollo)
   */
  static async cleanup(pattern = "*"): Promise<number> {
    try {
      const redis = await this.getInstance();
      const keys = await redis.keys(pattern);

      let deleted = 0;
      for (const key of keys) {
        const ttl = await redis.ttl(key);
        if (ttl === -1) { // Sin TTL
          continue;
        }
        if (ttl === -2) { // Expirada
          await redis.del(key);
          deleted++;
        }
      }

      console.log(`🧹 Limpieza completada: ${deleted} keys eliminadas`);
      return deleted;
    } catch (error) {
      console.error("Error en cleanup de Redis:", error);
      return 0;
    }
  }

  /**
   * Obtiene métricas de las colas
   */
  static async getQueueMetrics(): Promise<{
    fastExecution: number;
    fullProcessing: number;
    failed: number;
    total: number;
  }> {
    try {
      const redis = await this.getInstance();

      const [fastExecution, fullProcessing, failed] = await Promise.all([
        redis.llen("queue:fast_execution"),
        redis.llen("queue:full_processing"),
        redis.llen("queue:failed"),
      ]);

      return {
        fastExecution,
        fullProcessing,
        failed,
        total: fastExecution + fullProcessing + failed,
      };
    } catch (error) {
      console.error("Error obteniendo métricas de colas:", error);
      return {
        fastExecution: 0,
        fullProcessing: 0,
        failed: 0,
        total: 0,
      };
    }
  }
}

// Cleanup automático al cerrar la aplicación
if (typeof Deno !== "undefined") {
  Deno.addSignalListener("SIGINT", async () => {
    console.log("\n🛑 Cerrando aplicación...");
    await RedisService.close();
    Deno.exit(0);
  });

  Deno.addSignalListener("SIGTERM", async () => {
    console.log("\n🛑 Terminando aplicación...");
    await RedisService.close();
    Deno.exit(0);
  });
}
