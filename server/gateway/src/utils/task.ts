import { RedisService } from "./redis.ts";
import { v4 } from "uuid";

export interface Task {
  taskId: string;
  instruction: string;
  instructionHash: string;
  options: {
    timeout?: number;
    headless?: boolean;
    viewport?: {
      width: number;
      height: number;
    };
    waitForSelector?: string;
    priority?: "low" | "normal" | "high";
  };
  metadata?: {
    userId?: string;
    sessionId?: string;
    tags?: string[];
  };
  strategy: "optimized_prescript" | "full_llm_processing";
  preScript?: Record<string, unknown>;
  status: "queued" | "processing" | "completed" | "failed";
  progress?: number;
  createdAt: string;
  updatedAt?: string;
  userId: string;
  priority: "low" | "normal" | "high";
  workerId?: string;
  executionStartedAt?: string;
  executionCompletedAt?: string;
}

export interface TaskResult {
  taskId: string;
  success: boolean;
  data?: Record<string, unknown>;
  artifacts?: {
    screenshots?: string[];
    logs?: string[];
    extractedData?: Record<string, unknown>;
    videoRecording?: string;
  };
  error?: string;
  executionTime: number;
  strategy: string;
  costSaving?: number;
  preScriptGenerated?: boolean;
  selfHealingApplied?: boolean;
}

export class TaskService {
  /**
   * Crea una nueva tarea
   */
  static async createTask(
    instruction: string,
    options: Task["options"],
    metadata: Task["metadata"],
    userId: string,
  ): Promise<Task> {
    const taskId = v4.generate();
    const instructionHash = await this.generateInstructionHash(instruction);

    // Verificar si existe pre-script
    const redis = await RedisService.getInstance();
    const preScript = await redis.get(`prescript:${instructionHash}`);

    const task: Task = {
      taskId,
      instruction,
      instructionHash,
      options,
      metadata,
      strategy: preScript ? "optimized_prescript" : "full_llm_processing",
      preScript: preScript ? JSON.parse(preScript) : null,
      status: "queued",
      createdAt: new Date().toISOString(),
      userId,
      priority: options.priority || "normal",
    };

    return task;
  }

  /**
   * Guarda una tarea en Redis
   */
  static async saveTask(task: Task): Promise<void> {
    const redis = await RedisService.getInstance();
    await redis.setex(`task:${task.taskId}`, 3600, JSON.stringify(task));

    // Agregar a índices para búsqueda
    await redis.sadd(`user:${task.userId}:tasks`, task.taskId);
    await redis.zadd(
      "tasks:by_created",
      new Date(task.createdAt).getTime(),
      task.taskId,
    );
  }

  /**
   * Obtiene una tarea por ID
   */
  static async getTask(taskId: string): Promise<Task | null> {
    try {
      const redis = await RedisService.getInstance();
      const taskData = await redis.get(`task:${taskId}`);

      if (!taskData) {
        return null;
      }

      return JSON.parse(taskData) as Task;
    } catch (error) {
      console.error(`Error obteniendo tarea ${taskId}:`, error);
      return null;
    }
  }

  /**
   * Actualiza el estado de una tarea
   */
  static async updateTaskStatus(
    taskId: string,
    status: Task["status"],
    progress?: number,
    workerId?: string,
  ): Promise<boolean> {
    try {
      const task = await this.getTask(taskId);
      if (!task) {
        return false;
      }

      task.status = status;
      task.updatedAt = new Date().toISOString();

      if (progress !== undefined) {
        task.progress = progress;
      }

      if (workerId) {
        task.workerId = workerId;
      }

      if (status === "processing" && !task.executionStartedAt) {
        task.executionStartedAt = new Date().toISOString();
      }

      if (
        (status === "completed" || status === "failed") &&
        !task.executionCompletedAt
      ) {
        task.executionCompletedAt = new Date().toISOString();
      }

      await this.saveTask(task);

      // Notificar cambio de estado via pub/sub
      const redis = await RedisService.getInstance();
      await redis.publish(
        "task_status_updates",
        JSON.stringify({
          taskId,
          status,
          progress,
          timestamp: new Date().toISOString(),
        }),
      );

      return true;
    } catch (error) {
      console.error(`Error actualizando tarea ${taskId}:`, error);
      return false;
    }
  }

  /**
   * Guarda el resultado de una tarea
   */
  static async saveTaskResult(result: TaskResult): Promise<void> {
    try {
      const redis = await RedisService.getInstance();

      // Guardar resultado
      await redis.setex(
        `result:${result.taskId}`,
        86400, // 24 horas
        JSON.stringify(result),
      );

      // Actualizar estado de la tarea
      await this.updateTaskStatus(
        result.taskId,
        result.success ? "completed" : "failed",
      );

      // Si fue exitoso y no había pre-script, generar uno
      if (result.success && result.strategy === "full_llm_processing") {
        await this.generatePreScript(result.taskId);
      }
    } catch (error) {
      console.error(
        `Error guardando resultado de tarea ${result.taskId}:`,
        error,
      );
    }
  }

  /**
   * Obtiene el resultado de una tarea
   */
  static async getTaskResult(taskId: string): Promise<TaskResult | null> {
    try {
      const redis = await RedisService.getInstance();
      const resultData = await redis.get(`result:${taskId}`);

      if (!resultData) {
        return null;
      }

      return JSON.parse(resultData) as TaskResult;
    } catch (error) {
      console.error(`Error obteniendo resultado de tarea ${taskId}:`, error);
      return null;
    }
  }

  /**
   * Obtiene tareas de un usuario
   */
  static async getUserTasks(
    userId: string,
    limit = 50,
    offset = 0,
  ): Promise<Task[]> {
    try {
      const redis = await RedisService.getInstance();
      const taskIds = await redis.smembers(`user:${userId}:tasks`);

      const tasks: Task[] = [];
      const batch = taskIds.slice(offset, offset + limit);

      for (const taskId of batch) {
        const task = await this.getTask(taskId);
        if (task) {
          tasks.push(task);
        }
      }

      // Ordenar por fecha de creación (más recientes primero)
      return tasks.sort((a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    } catch (error) {
      console.error(`Error obteniendo tareas del usuario ${userId}:`, error);
      return [];
    }
  }

  /**
   * Genera hash de instrucción para identificar pre-scripts
   */
  private static async generateInstructionHash(
    instruction: string,
  ): Promise<string> {
    const normalized = instruction.toLowerCase().trim();
    const encoder = new TextEncoder();
    const data = encoder.encode(normalized);
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
  }

  /**
   * Genera un pre-script basado en una ejecución exitosa
   */
  private static async generatePreScript(taskId: string): Promise<void> {
    try {
      const task = await this.getTask(taskId);
      const result = await this.getTaskResult(taskId);

      if (!task || !result || !result.success) {
        return;
      }

      // Extraer acciones del resultado para crear pre-script
      const preScript = {
        instructionHash: task.instructionHash,
        actions: result.data?.actions || [],
        selectors: result.data?.selectors || {},
        timing: result.data?.timing || {},
        successRate: 1.0,
        createdAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        usageCount: 0,
        avgExecutionTime: result.executionTime,
      };

      const redis = await RedisService.getInstance();
      await redis.setex(
        `prescript:${task.instructionHash}`,
        86400 * 30, // 30 días
        JSON.stringify(preScript),
      );

      console.log(
        `✅ Pre-script generado para instrucción: ${
          task.instruction.substring(0, 50)
        }...`,
      );
    } catch (error) {
      console.error(`Error generando pre-script para tarea ${taskId}:`, error);
    }
  }

  /**
   * Obtiene estadísticas de tareas
   */
  static async getTaskStats(userId?: string): Promise<{
    total: number;
    completed: number;
    failed: number;
    processing: number;
    queued: number;
    avgExecutionTime: number;
    costSavings: number;
  }> {
    try {
      const redis = await RedisService.getInstance();

      // Si es para un usuario específico
      if (userId) {
        const taskIds = await redis.smembers(`user:${userId}:tasks`);
        const tasks = await Promise.all(
          taskIds.map((id) => this.getTask(id)),
        );

        const validTasks = tasks.filter((t) => t !== null) as Task[];

        return this.calculateStats(validTasks);
      }

      // Estadísticas globales (solo para admins)
      const allTaskIds = await redis.zrange("tasks:by_created", 0, -1);
      const tasks = await Promise.all(
        allTaskIds.map((id) => this.getTask(id)),
      );

      const validTasks = tasks.filter((t) => t !== null) as Task[];

      return this.calculateStats(validTasks);
    } catch (error) {
      console.error("Error obteniendo estadísticas de tareas:", error);
      return {
        total: 0,
        completed: 0,
        failed: 0,
        processing: 0,
        queued: 0,
        avgExecutionTime: 0,
        costSavings: 0,
      };
    }
  }

  /**
   * Calcula estadísticas de un conjunto de tareas
   */
  private static calculateStats(tasks: Task[]): {
    total: number;
    completed: number;
    failed: number;
    processing: number;
    queued: number;
    avgExecutionTime: number;
    costSavings: number;
  } {
    const stats = {
      total: tasks.length,
      completed: 0,
      failed: 0,
      processing: 0,
      queued: 0,
      avgExecutionTime: 0,
      costSavings: 0,
    };

    let totalExecutionTime = 0;
    let completedTasks = 0;
    let optimizedTasks = 0;

    for (const task of tasks) {
      switch (task.status) {
        case "completed":
          stats.completed++;
          completedTasks++;
          if (task.strategy === "optimized_prescript") {
            optimizedTasks++;
          }
          break;
        case "failed":
          stats.failed++;
          break;
        case "processing":
          stats.processing++;
          break;
        case "queued":
          stats.queued++;
          break;
      }

      if (task.executionStartedAt && task.executionCompletedAt) {
        const executionTime = new Date(task.executionCompletedAt).getTime() -
          new Date(task.executionStartedAt).getTime();
        totalExecutionTime += executionTime;
      }
    }

    if (completedTasks > 0) {
      stats.avgExecutionTime = totalExecutionTime / completedTasks;
      stats.costSavings = (optimizedTasks / completedTasks) * 90; // 90% ahorro con pre-scripts
    }

    return stats;
  }
}
