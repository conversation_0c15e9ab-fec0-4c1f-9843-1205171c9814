// CORS Middleware for AERY API
import { load } from "https://deno.land/std@0.208.0/dotenv/mod.ts";

// Load environment variables
try {
  await load({ export: false, allowEmptyValues: true, envPath: ".env" });
} catch {
  // .env file not found, continue with existing env vars
}

export class CorsMiddleware {
  /**
   * Verifica si CORS está habilitado mediante variable de entorno
   */
  private static isCorsEnabled(): boolean {
    const corsEnabled = Deno.env.get("CORS_ENABLED");
    console.log(`🔍 DEBUG: CORS_ENABLED env var = '${corsEnabled}'`);
    return corsEnabled !== "false";
  }
  private static getAllowedOrigins(): string[] {
    const origins = Deno.env.get("CORS_ALLOWED_ORIGINS") || "*";
    if (origins === "*") {
      return ["*"];
    }
    return origins.split(",").map(origin => origin.trim());
  }

  private static getAllowedMethods(): string[] {
    const methods = Deno.env.get("CORS_ALLOWED_METHODS") || "GET,POST,PUT,DELETE,OPTIONS,PATCH";
    return methods.split(",").map(method => method.trim());
  }

  private static getAllowedHeaders(): string[] {
    const headers = Deno.env.get("CORS_ALLOWED_HEADERS") || "Content-Type,Authorization,X-API-Key,X-Requested-With,Accept,Origin,Cache-Control,Pragma";
    return headers.split(",").map(header => header.trim());
  }

  private static getAllowCredentials(): boolean {
    return Deno.env.get("CORS_ALLOW_CREDENTIALS") === "true";
  }

  private static getMaxAge(): string {
    return Deno.env.get("CORS_MAX_AGE") || "86400";
  }

  /**
   * Aplica headers CORS a una respuesta
   */
  static applyCorsHeaders(request: Request, response: Response): Response {
    const origin = request.headers.get("Origin");
    const headers = new Headers(response.headers);
    const allowedOrigins = this.getAllowedOrigins();

    // Si se permite cualquier origen (*)
    if (allowedOrigins.includes("*")) {
      headers.set("Access-Control-Allow-Origin", "*");
    } else if (origin && allowedOrigins.includes(origin)) {
      // Verificar si el origin está permitido
      headers.set("Access-Control-Allow-Origin", origin);
    } else if (!origin) {
      // Para requests sin origin (como Postman)
      headers.set("Access-Control-Allow-Origin", "*");
    } else {
      // Para origins no permitidos, usar el primer origin permitido como fallback
      headers.set("Access-Control-Allow-Origin", allowedOrigins[0] || "*");
    }

    headers.set("Access-Control-Allow-Methods", this.getAllowedMethods().join(", "));
    headers.set("Access-Control-Allow-Headers", this.getAllowedHeaders().join(", "));
    headers.set("Access-Control-Allow-Credentials", this.getAllowCredentials().toString());
    headers.set("Access-Control-Max-Age", this.getMaxAge());

    // Crear nueva respuesta con headers CORS
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers
    });
  }

  /**
   * Maneja requests OPTIONS (preflight)
   */
  static handlePreflight(request: Request): Response {
    const origin = request.headers.get("Origin");
    const headers = new Headers();
    const allowedOrigins = this.getAllowedOrigins();

    // Si se permite cualquier origen (*)
    if (allowedOrigins.includes("*")) {
      headers.set("Access-Control-Allow-Origin", "*");
    } else if (origin && allowedOrigins.includes(origin)) {
      headers.set("Access-Control-Allow-Origin", origin);
    } else if (!origin) {
      headers.set("Access-Control-Allow-Origin", "*");
    } else {
      // Para origins no permitidos, usar el primer origin permitido como fallback
      headers.set("Access-Control-Allow-Origin", allowedOrigins[0] || "*");
    }

    headers.set("Access-Control-Allow-Methods", this.getAllowedMethods().join(", "));
    headers.set("Access-Control-Allow-Headers", this.getAllowedHeaders().join(", "));
    headers.set("Access-Control-Allow-Credentials", this.getAllowCredentials().toString());
    headers.set("Access-Control-Max-Age", this.getMaxAge());

    return new Response(null, {
      status: 204,
      headers
    });
  }

  /**
   * Middleware wrapper condicional que solo aplica CORS si está habilitado
   */
  static wrapConditional(handlers: any) {
    // Si CORS está deshabilitado, aún necesitamos manejar OPTIONS pero sin headers CORS
    if (!this.isCorsEnabled()) {
      console.log("🚫 CORS deshabilitado - omitiendo headers CORS");
      const wrappedHandlers: any = {};
      
      // Agregar handler para OPTIONS con headers mínimos para evitar errores CORS
       wrappedHandlers.OPTIONS = async (req: Request, ctx: any): Promise<Response> => {
         const origin = req.headers.get("Origin");
         const headers = new Headers();
         
         // Permitir el origen específico para evitar errores del navegador
         if (origin) {
           headers.set("Access-Control-Allow-Origin", origin);
         }
         headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
         headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-API-Key");
         
         return new Response(null, { status: 200, headers });
       };
      
      for (const [method, handler] of Object.entries(handlers)) {
         if (typeof handler === 'function') {
           wrappedHandlers[method] = async (req: Request, ctx: any): Promise<Response> => {
             const response = await (handler as Function)(req, ctx);
             
             // Agregar headers CORS mínimos a la respuesta
             const origin = req.headers.get("Origin");
             if (origin) {
               const headers = new Headers(response.headers);
               headers.set("Access-Control-Allow-Origin", origin);
               headers.set("Access-Control-Allow-Credentials", "true");
               
               return new Response(response.body, {
                 status: response.status,
                 statusText: response.statusText,
                 headers
               });
             }
             
             return response;
           };
         } else {
           wrappedHandlers[method] = handler;
         }
       }
      
      return wrappedHandlers;
    }
    
    // Si CORS está habilitado, usar el wrapper normal
    console.log("✅ CORS habilitado - aplicando headers CORS");
    return this.wrap(handlers);
  }

  /**
   * Middleware wrapper para rutas Fresh (Handlers object)
   */
  static wrap(handlers: any) {
    const wrappedHandlers: any = {};

    // Wrap each HTTP method handler
    for (const [method, handler] of Object.entries(handlers)) {
      if (typeof handler === 'function') {
        wrappedHandlers[method] = async (req: Request, ctx: any): Promise<Response> => {
          // Manejar preflight OPTIONS
          if (req.method === "OPTIONS") {
            return this.handlePreflight(req);
          }

          try {
            // Ejecutar el handler original
            const response = await (handler as Function)(req, ctx);

            // Aplicar headers CORS
            return this.applyCorsHeaders(req, response);
          } catch (error) {
            console.error("Error in CORS middleware:", error);

            // Crear respuesta de error con CORS
            const errorResponse = new Response(
              JSON.stringify({
                error: "Internal Server Error",
                message: "An error occurred processing your request",
                timestamp: new Date().toISOString()
              }),
              {
                status: 500,
                headers: {
                  "Content-Type": "application/json"
                }
              }
            );

            return this.applyCorsHeaders(req, errorResponse);
          }
        };
      }
    }

    // Always handle OPTIONS for preflight
    if (!wrappedHandlers.OPTIONS) {
      wrappedHandlers.OPTIONS = (req: Request, ctx: any): Response => {
        return this.handlePreflight(req);
      };
    }

    return wrappedHandlers;
  }
}
