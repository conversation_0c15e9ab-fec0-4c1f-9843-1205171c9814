import { createHash } from "crypto";
import { RedisService } from "../utils/redis.ts";

export interface AuthResult {
  valid: boolean;
  userId?: string;
  role?: "user" | "admin";
  plan?: "basic" | "premium";
  rateLimit?: {
    remaining: number;
    resetTime: number;
  };
}

export class AuthMiddleware {
  /**
   * Valida una API key desde el header Authorization
   */
  static async validateApiKey(req: Request): Promise<AuthResult> {
    try {
      const authHeader = req.headers.get("Authorization");

      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return { valid: false };
      }

      const apiKey = authHeader.substring(7); // Remover "Bearer "

      if (!apiKey || apiKey.length < 32) {
        return { valid: false };
      }

      // Hash de la API key para buscar en Redis
      const keyHash = createHash("sha256").update(apiKey).digest("hex");

      const redis = await RedisService.getInstance();
      const keyData = await redis.get(`apikey:${keyHash}`);

      if (!keyData) {
        return { valid: false };
      }

      const keyInfo = JSON.parse(keyData);

      // Verificar si la key está activa
      if (!keyInfo.active) {
        return { valid: false };
      }

      // Verificar expiración
      if (keyInfo.expiresAt && new Date(keyInfo.expiresAt) < new Date()) {
        return { valid: false };
      }

      // Verificar rate limiting
      const rateLimitResult = await this.checkRateLimit(
        keyInfo.userId,
        keyInfo.plan,
      );

      if (!rateLimitResult.allowed) {
        return {
          valid: false,
          rateLimit: {
            remaining: 0,
            resetTime: rateLimitResult.resetTime,
          },
        };
      }

      // Actualizar último uso
      await redis.setex(
        `apikey:${keyHash}`,
        keyInfo.ttl || 86400,
        JSON.stringify({
          ...keyInfo,
          lastUsed: new Date().toISOString(),
          usageCount: (keyInfo.usageCount || 0) + 1,
        }),
      );

      return {
        valid: true,
        userId: keyInfo.userId,
        role: keyInfo.role || "user",
        plan: keyInfo.plan || "basic",
        rateLimit: {
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime,
        },
      };
    } catch (error) {
      console.error("Error validando API key:", error);
      return { valid: false };
    }
  }

  /**
   * Verifica los límites de rate limiting
   */
  private static async checkRateLimit(
    userId: string,
    plan: string,
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    try {
      const redis = await RedisService.getInstance();

      // Límites por plan
      const limits = {
        basic: { requests: 100, window: 3600 }, // 100 req/hora
        premium: { requests: 1000, window: 3600 }, // 1000 req/hora
      };

      const limit = limits[plan as keyof typeof limits] || limits.basic;
      const now = Math.floor(Date.now() / 1000);
      const window = Math.floor(now / limit.window);
      const key = `ratelimit:${userId}:${window}`;

      // Incrementar contador
      const current = await redis.incr(key);

      // Establecer TTL en la primera request de la ventana
      if (current === 1) {
        await redis.expire(key, limit.window);
      }

      const resetTime = (window + 1) * limit.window;
      const remaining = Math.max(0, limit.requests - current);

      return {
        allowed: current <= limit.requests,
        remaining,
        resetTime,
      };
    } catch (error) {
      console.error("Error verificando rate limit:", error);
      // En caso de error, permitir la request
      return {
        allowed: true,
        remaining: 100,
        resetTime: Math.floor(Date.now() / 1000) + 3600,
      };
    }
  }

  /**
   * Genera una nueva API key
   */
  static async generateApiKey(
    userId: string,
    plan: "basic" | "premium" = "basic",
    role: "user" | "admin" = "user",
    expiresInDays?: number,
  ): Promise<string> {
    try {
      // Generar API key aleatoria
      const apiKey = crypto.randomUUID().replace(/-/g, "") +
        crypto.randomUUID().replace(/-/g, "").substring(0, 16);

      const keyHash = createHash("sha256").update(apiKey).digest("hex");

      const keyInfo = {
        userId,
        plan,
        role,
        active: true,
        createdAt: new Date().toISOString(),
        expiresAt: expiresInDays
          ? new Date(Date.now() + expiresInDays * 24 * 60 * 60 * 1000)
            .toISOString()
          : null,
        usageCount: 0,
        ttl: 86400 * (expiresInDays || 365), // TTL en segundos
      };

      const redis = await RedisService.getInstance();
      await redis.setex(
        `apikey:${keyHash}`,
        keyInfo.ttl,
        JSON.stringify(keyInfo),
      );

      // Guardar referencia del usuario a sus keys
      await redis.sadd(`user:${userId}:apikeys`, keyHash);

      return apiKey;
    } catch (error) {
      console.error("Error generando API key:", error);
      throw new Error("No se pudo generar la API key");
    }
  }

  /**
   * Revoca una API key
   */
  static async revokeApiKey(apiKey: string): Promise<boolean> {
    try {
      const keyHash = createHash("sha256").update(apiKey).digest("hex");
      const redis = await RedisService.getInstance();

      const keyData = await redis.get(`apikey:${keyHash}`);
      if (!keyData) {
        return false;
      }

      const keyInfo = JSON.parse(keyData);

      // Marcar como inactiva
      await redis.setex(
        `apikey:${keyHash}`,
        keyInfo.ttl || 86400,
        JSON.stringify({
          ...keyInfo,
          active: false,
          revokedAt: new Date().toISOString(),
        }),
      );

      return true;
    } catch (error) {
      console.error("Error revocando API key:", error);
      return false;
    }
  }
}
