// Model Management Service
import { config } from "../lib/config.ts";
import type { 
  ModelConfig, 
  CreateModelRequest, 
  UpdateModelRequest,
  ModelUsageStats,
  ModelProviderConfig,
  ModelSelectionConfig,
  ModelTestRequest,
  ModelTestResponse
} from "./types.ts";

export class ModelManager {
  private models: Map<string, ModelConfig> = new Map();
  private providerConfigs: Map<string, ModelProviderConfig> = new Map();
  private agentModelSelections: Map<string, ModelSelectionConfig> = new Map();
  private usageStats: Map<string, ModelUsageStats> = new Map();

  constructor() {
    this.initializeDefaultModels();
    this.initializeProviderConfigs();
    this.initializeAgentSelections();
  }

  private initializeDefaultModels() {
    const defaultModels: ModelConfig[] = [
      // OpenAI Models
      {
        id: "openai/gpt-4o",
        name: "GPT-4o",
        provider: "openai",
        category: "chat",
        pricing: { input: 2.50, output: 10.00 }, // per 1M tokens
        context_length: 128000,
        features: [
          { name: "function_calling", supported: true },
          { name: "json_mode", supported: true },
          { name: "vision", supported: true },
          { name: "code_interpreter", supported: true }
        ],
        enabled: true,
        default_params: {
          temperature: 0.1,
          max_tokens: 2000,
          top_p: 0.9
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: "openai/gpt-4o-mini",
        name: "GPT-4o Mini",
        provider: "openai",
        category: "chat",
        pricing: { input: 0.15, output: 0.60 },
        context_length: 128000,
        features: [
          { name: "function_calling", supported: true },
          { name: "json_mode", supported: true },
          { name: "vision", supported: true }
        ],
        enabled: true,
        default_params: {
          temperature: 0.1,
          max_tokens: 1500,
          top_p: 0.9
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: "openai/text-embedding-3-small",
        name: "Text Embedding 3 Small",
        provider: "openai",
        category: "embedding",
        pricing: { input: 0.02, output: 0 },
        context_length: 8192,
        features: [
          { name: "batch_processing", supported: true },
          { name: "custom_dimensions", supported: true }
        ],
        enabled: true,
        default_params: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      // Anthropic Models
      {
        id: "anthropic/claude-3.5-sonnet",
        name: "Claude 3.5 Sonnet",
        provider: "anthropic",
        category: "chat",
        pricing: { input: 3.00, output: 15.00 },
        context_length: 200000,
        features: [
          { name: "function_calling", supported: true },
          { name: "json_mode", supported: true },
          { name: "vision", supported: true },
          { name: "artifacts", supported: true }
        ],
        enabled: true,
        default_params: {
          temperature: 0.1,
          max_tokens: 2000
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      // Cohere Models
      {
        id: "cohere/embed-v4.0",
        name: "Embed v4.0",
        provider: "cohere",
        category: "embedding",
        pricing: { input: 0.10, output: 0 },
        context_length: 512,
        features: [
          { name: "multilingual", supported: true },
          { name: "batch_processing", supported: true }
        ],
        enabled: true,
        default_params: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    defaultModels.forEach(model => {
      this.models.set(model.id, model);
      
      // Initialize usage stats
      this.usageStats.set(model.id, {
        model_id: model.id,
        total_requests: 0,
        total_tokens_input: 0,
        total_tokens_output: 0,
        total_cost: 0,
        last_used: "",
        avg_response_time: 0,
        success_rate: 100
      });
    });
  }

  private initializeProviderConfigs() {
    const providers: ModelProviderConfig[] = [
      {
        provider: "openai",
        api_key: config.ai.openaiApiKey || "",
        base_url: "https://api.openai.com/v1",
        enabled: Boolean(config.ai.openaiApiKey),
        models: Array.from(this.models.values()).filter(m => m.provider === "openai")
      },
      {
        provider: "anthropic",
        api_key: config.ai.anthropicApiKey || "",
        base_url: "https://api.anthropic.com/v1",
        enabled: Boolean(config.ai.anthropicApiKey),
        models: Array.from(this.models.values()).filter(m => m.provider === "anthropic")
      },
      {
        provider: "openrouter",
        api_key: config.openrouter.apiKey || "",
        base_url: config.openrouter.baseUrl,
        enabled: Boolean(config.openrouter.apiKey),
        models: [] // OpenRouter models are dynamic
      },
      {
        provider: "cohere",
        api_key: config.ai.cohereApiKey || "",
        base_url: "https://api.cohere.ai/v1",
        enabled: Boolean(config.ai.cohereApiKey),
        models: Array.from(this.models.values()).filter(m => m.provider === "cohere")
      }
    ];

    providers.forEach(provider => {
      this.providerConfigs.set(provider.provider, provider);
    });
  }

  private initializeAgentSelections() {
    const defaultSelections: ModelSelectionConfig[] = [
      {
        agent_type: "instruction_analyzer",
        model_id: "openai/gpt-4o-mini",
        fallback_model_id: "anthropic/claude-3.5-sonnet",
        enabled: true
      },
      {
        agent_type: "action_planner",
        model_id: "openai/gpt-4o",
        fallback_model_id: "anthropic/claude-3.5-sonnet",
        enabled: true
      },
      {
        agent_type: "element_selector",
        model_id: "openai/gpt-4o-mini",
        fallback_model_id: "openai/gpt-4o",
        enabled: true
      },
      {
        agent_type: "validator",
        model_id: "openai/gpt-4o-mini",
        fallback_model_id: "anthropic/claude-3.5-sonnet",
        enabled: true
      },
      {
        agent_type: "self_healer",
        model_id: "anthropic/claude-3.5-sonnet",
        fallback_model_id: "openai/gpt-4o",
        enabled: true
      },
      {
        agent_type: "rag_embeddings",
        model_id: "openai/text-embedding-3-small",
        fallback_model_id: "cohere/embed-v4.0",
        enabled: true
      },
      {
        agent_type: "rag_chat",
        model_id: "openai/gpt-4o-mini",
        fallback_model_id: "anthropic/claude-3.5-sonnet",
        enabled: true
      }
    ];

    defaultSelections.forEach(selection => {
      this.agentModelSelections.set(selection.agent_type, selection);
    });
  }

  // Model CRUD Operations
  listModels(provider?: string, category?: string, enabled?: boolean): ModelConfig[] {
    let models = Array.from(this.models.values());
    
    if (provider) {
      models = models.filter(m => m.provider === provider);
    }
    
    if (category) {
      models = models.filter(m => m.category === category);
    }
    
    if (enabled !== undefined) {
      models = models.filter(m => m.enabled === enabled);
    }
    
    return models.sort((a, b) => a.name.localeCompare(b.name));
  }

  getModel(id: string): ModelConfig | undefined {
    return this.models.get(id);
  }

  createModel(request: CreateModelRequest): ModelConfig {
    const id = `${request.provider}/${request.name.toLowerCase().replace(/\s+/g, '-')}`;
    
    if (this.models.has(id)) {
      throw new Error(`Model with ID '${id}' already exists`);
    }

    const model: ModelConfig = {
      id,
      name: request.name,
      provider: request.provider,
      category: request.category,
      pricing: request.pricing,
      context_length: request.context_length,
      features: request.features || [],
      enabled: request.enabled ?? true,
      default_params: request.default_params || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    this.models.set(id, model);
    
    // Initialize usage stats
    this.usageStats.set(id, {
      model_id: id,
      total_requests: 0,
      total_tokens_input: 0,
      total_tokens_output: 0,
      total_cost: 0,
      last_used: "",
      avg_response_time: 0,
      success_rate: 100
    });

    return model;
  }

  updateModel(id: string, request: UpdateModelRequest): ModelConfig {
    const model = this.models.get(id);
    if (!model) {
      throw new Error(`Model with ID '${id}' not found`);
    }

    const updatedModel: ModelConfig = {
      ...model,
      ...request,
      updated_at: new Date().toISOString()
    };

    this.models.set(id, updatedModel);
    return updatedModel;
  }

  deleteModel(id: string): boolean {
    const deleted = this.models.delete(id);
    if (deleted) {
      this.usageStats.delete(id);
      
      // Remove from agent selections
      for (const [agentType, selection] of this.agentModelSelections.entries()) {
        if (selection.model_id === id || selection.fallback_model_id === id) {
          this.agentModelSelections.delete(agentType);
        }
      }
    }
    return deleted;
  }

  // Provider Management
  getProviders(): ModelProviderConfig[] {
    return Array.from(this.providerConfigs.values());
  }

  updateProviderConfig(provider: string, apiKey: string, enabled: boolean): boolean {
    const config = this.providerConfigs.get(provider);
    if (!config) return false;

    config.api_key = apiKey;
    config.enabled = enabled;
    
    return true;
  }

  // Agent Model Selection
  getAgentModelSelections(): ModelSelectionConfig[] {
    return Array.from(this.agentModelSelections.values());
  }

  setAgentModel(agentType: string, modelId: string, fallbackModelId?: string): boolean {
    if (!this.models.has(modelId)) {
      throw new Error(`Model '${modelId}' not found`);
    }

    if (fallbackModelId && !this.models.has(fallbackModelId)) {
      throw new Error(`Fallback model '${fallbackModelId}' not found`);
    }

    this.agentModelSelections.set(agentType, {
      agent_type: agentType,
      model_id: modelId,
      fallback_model_id: fallbackModelId,
      enabled: true
    });

    return true;
  }

  getModelForAgent(agentType: string): string | null {
    const selection = this.agentModelSelections.get(agentType);
    if (!selection || !selection.enabled) return null;

    const model = this.models.get(selection.model_id);
    if (model && model.enabled) {
      return selection.model_id;
    }

    // Try fallback
    if (selection.fallback_model_id) {
      const fallbackModel = this.models.get(selection.fallback_model_id);
      if (fallbackModel && fallbackModel.enabled) {
        return selection.fallback_model_id;
      }
    }

    return null;
  }

  // Usage Statistics
  getUsageStats(modelId?: string): ModelUsageStats[] {
    if (modelId) {
      const stats = this.usageStats.get(modelId);
      return stats ? [stats] : [];
    }
    
    return Array.from(this.usageStats.values());
  }

  recordUsage(modelId: string, tokensInput: number, tokensOutput: number, responseTime: number, success: boolean) {
    const stats = this.usageStats.get(modelId);
    const model = this.models.get(modelId);
    
    if (!stats || !model) return;

    const cost = (tokensInput * model.pricing.input + tokensOutput * model.pricing.output) / 1000000; // per 1M tokens

    stats.total_requests++;
    stats.total_tokens_input += tokensInput;
    stats.total_tokens_output += tokensOutput;
    stats.total_cost += cost;
    stats.last_used = new Date().toISOString();
    stats.avg_response_time = (stats.avg_response_time * (stats.total_requests - 1) + responseTime) / stats.total_requests;
    
    // Calculate success rate
    const successCount = Math.round(stats.success_rate * (stats.total_requests - 1) / 100);
    const newSuccessCount = success ? successCount + 1 : successCount;
    stats.success_rate = (newSuccessCount / stats.total_requests) * 100;
  }

  // Model Testing
  async testModel(request: ModelTestRequest): Promise<ModelTestResponse> {
    const model = this.models.get(request.model_id);
    if (!model) {
      return {
        success: false,
        error: `Model '${request.model_id}' not found`
      };
    }

    const provider = this.providerConfigs.get(model.provider);
    if (!provider || !provider.enabled || !provider.api_key) {
      return {
        success: false,
        error: `Provider '${model.provider}' not configured or disabled`
      };
    }

    const startTime = Date.now();

    try {
      // This is a simplified test - in reality, you'd call the actual provider API
      const response = `Test response from ${model.name}: "${request.prompt}"`;
      const responseTime = Date.now() - startTime;
      const tokensUsed = Math.ceil(request.prompt.length / 4) + Math.ceil(response.length / 4); // Rough estimate
      const cost = (tokensUsed * model.pricing.input) / 1000000;

      // Record usage
      this.recordUsage(request.model_id, tokensUsed, tokensUsed, responseTime, true);

      return {
        success: true,
        response,
        tokens_used: tokensUsed,
        response_time: responseTime,
        cost
      };
    } catch (error) {
      this.recordUsage(request.model_id, 0, 0, Date.now() - startTime, false);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }

  // Utility Methods
  getAvailableProviders(): string[] {
    return Array.from(this.providerConfigs.values())
      .filter(p => p.enabled)
      .map(p => p.provider);
  }

  getCategories(): string[] {
    return [...new Set(Array.from(this.models.values()).map(m => m.category))];
  }

  getTotalCost(): number {
    return Array.from(this.usageStats.values())
      .reduce((total, stats) => total + stats.total_cost, 0);
  }

  getTotalRequests(): number {
    return Array.from(this.usageStats.values())
      .reduce((total, stats) => total + stats.total_requests, 0);
  }
}

// Global instance
export const modelManager = new ModelManager();