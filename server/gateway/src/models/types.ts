// Model Management Types
export interface ModelConfig {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'openrouter' | 'cohere';
  category: 'chat' | 'embedding' | 'completion';
  pricing: {
    input: number;  // tokens per dollar
    output: number; // tokens per dollar
  };
  context_length: number;
  features: ModelFeature[];
  enabled: boolean;
  default_params: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    top_k?: number;
  };
  created_at: string;
  updated_at: string;
}

export interface ModelFeature {
  name: string;
  supported: boolean;
  description?: string;
}

export interface CreateModelRequest {
  name: string;
  provider: 'openai' | 'anthropic' | 'openrouter' | 'cohere';
  category: 'chat' | 'embedding' | 'completion';
  pricing: {
    input: number;
    output: number;
  };
  context_length: number;
  features?: ModelFeature[];
  enabled?: boolean;
  default_params?: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    top_k?: number;
  };
}

export interface UpdateModelRequest {
  name?: string;
  pricing?: {
    input: number;
    output: number;
  };
  context_length?: number;
  features?: ModelFeature[];
  enabled?: boolean;
  default_params?: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    top_k?: number;
  };
}

export interface ModelUsageStats {
  model_id: string;
  total_requests: number;
  total_tokens_input: number;
  total_tokens_output: number;
  total_cost: number;
  last_used: string;
  avg_response_time: number;
  success_rate: number;
}

export interface ModelProviderConfig {
  provider: string;
  api_key: string;
  base_url?: string;
  enabled: boolean;
  models: ModelConfig[];
}

export interface ModelSelectionConfig {
  agent_type: string;
  model_id: string;
  fallback_model_id?: string;
  enabled: boolean;
}

export interface ModelsListResponse {
  success: boolean;
  models: ModelConfig[];
  total: number;
  providers: string[];
  categories: string[];
}

export interface ModelResponse {
  success: boolean;
  model: ModelConfig;
}

export interface ModelStatsResponse {
  success: boolean;
  stats: ModelUsageStats[];
  total_cost: number;
  total_requests: number;
}

export interface ModelTestRequest {
  model_id: string;
  prompt: string;
  params?: {
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
  };
}

export interface ModelTestResponse {
  success: boolean;
  response?: string;
  tokens_used?: number;
  response_time?: number;
  cost?: number;
  error?: string;
}