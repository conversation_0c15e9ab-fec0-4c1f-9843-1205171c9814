// RAG Types - Definiciones de tipos para el sistema RAG

// Interfaz base para el shared store
export interface SharedStore {
  [key: string]: any;
}

// Resultado de ejecución de nodos
export interface ExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  nextAction?: string;
}

// Configuración de proyecto RAG
export interface ProjectConfig {
  id: string;
  name: string;
  description?: string;
  vectorStoreConfig: VectorStoreConfig;
  llmConfig: LLMConfig;
  createdAt?: Date;
  updatedAt?: Date;
}

// Configuración de Vector Store
export interface VectorStoreConfig {
  apiKey: string;
  model?: string;
  dimensions?: number;
}

// Configuración de LLM
export interface LLMConfig {
  model: string;
  apiKey: string;
  temperature?: number;
  maxTokens?: number;
}

// Documento para el vector store
export interface Document {
  id: string;
  content: string;
  metadata: {
    projectId: string;
    type: 'story' | 'requirement' | 'test' | 'documentation';
    source?: string;
    timestamp: Date;
    [key: string]: any;
  };
}

// Resultado de búsqueda en vector store
export interface SearchResult {
  document: Document;
  score: number;
}

// User Story mejorada
export interface ImprovedStory {
  original: string;
  improved: string;
  acceptanceCriteria: string[];
  definitionOfDone: string[];
  estimatedComplexity: 'low' | 'medium' | 'high';
  suggestedTestCases: string[];
  context: string;
}

// Test Case generado
export interface GeneratedTestCase {
  title: string;
  description: string;
  type: 'happy_path' | 'negative' | 'boundary' | 'integration';
  steps: TestStep[];
  expectedResult: string;
  priority: 'high' | 'medium' | 'low';
}

// Paso de test case
export interface TestStep {
  step: number;
  action: string;
  data?: string;
  expectedBehavior?: string;
}

// Edge Cases identificados
export interface EdgeCase {
  scenario: string;
  description: string;
  riskLevel: 'high' | 'medium' | 'low';
  suggestedHandling: string;
  testApproach: string;
}

// Request para mejorar story
export interface ImproveStoryRequest {
  story: string;
  context?: string;
  requirements?: string[];
}

// Response para story mejorada
export interface ImproveStoryResponse {
  improvedStory: ImprovedStory;
  suggestions: string[];
  confidence: number;
}

// Request para generar test cases
export interface GenerateTestsRequest {
  story: string;
  acceptanceCriteria?: string[];
  testTypes?: ('happy_path' | 'negative' | 'boundary' | 'integration')[];
}

// Response para test cases generados
export interface GenerateTestsResponse {
  testCases: GeneratedTestCase[];
  edgeCases: EdgeCase[];
  coverage: {
    functionalCoverage: number;
    boundaryTestsIncluded: boolean;
    negativeTestsIncluded: boolean;
  };
}

// Request para crear proyecto
export interface CreateProjectRequest {
  name: string;
  description?: string;
  initialDocuments?: {
    content: string;
    type: 'story' | 'requirement' | 'test' | 'documentation';
    source?: string;
  }[];
}

// Response para proyecto creado
export interface CreateProjectResponse {
  projectId: string;
  message: string;
}

// Request para añadir conocimiento
export interface AddKnowledgeRequest {
  documents: {
    content: string;
    type: 'story' | 'requirement' | 'test' | 'documentation';
    source?: string;
    metadata?: Record<string, any>;
  }[];
}

// Response para conocimiento añadido
export interface AddKnowledgeResponse {
  documentsAdded: number;
  message: string;
}

// Interfaz para abstracciones (Dependency Inversion)
export interface IVectorStore {
  search(query: string, topK: number): Promise<SearchResult[]>;
  add(documents: Document[]): Promise<void>;
  delete(documentIds: string[]): Promise<void>;
  getStats(): Promise<{ totalDocuments: number; lastUpdated: Date }>;
  getAllDocuments(): Promise<Document[]>;
}

export interface ILLMProvider {
  generate(prompt: string, context: string[]): Promise<string>;
  generateStructured<T>(prompt: string, context: string[], schema?: any): Promise<T>;
}

// Métricas de RAG
export interface RAGMetrics {
  projectId: string; 
  queriesProcessed: number;
  averageResponseTime: number;
  contextRetrievalAccuracy: number;
  userSatisfactionScore?: number;
  timestamp: Date;
}

// Error específico de RAG
export class RAGError extends Error {
  constructor(
    message: string,
    public code: string,
    public projectId?: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'RAGError';
  }
}