// Context Analysis Node - Analiza el contexto de una user story
import { Node } from "../../core/node.ts";
import type { RAGEngine } from "../../services/rag-engine.ts";
import type { SharedStore } from "../../types/index.ts";

/**
 * Nodo que analiza el contexto de una user story
 * para identificar información relevante del proyecto
 */
export class ContextAnalysisNode extends Node {
  constructor(private ragEngine: RAGEngine) {
    super("context-analysis", 2);
  }

  prep(shared: SharedStore) {
    this.log("Preparing context analysis");
    
    return {
      story: shared.userStory || shared.story,
      projectId: shared.projectId,
      additionalContext: shared.additionalContext || shared.context
    };
  }

  async exec(data: any): Promise<any> {
    this.log(`Analyzing context for story: "${data.story}"`);
    
    if (!data.story) {
      throw new Error("User story is required for context analysis");
    }

    try {
      // Buscar contexto relevante en el knowledge base
      const contextQuery = `analyze context user story: ${data.story}`;
      
      const systemPrompt = `
Analiza el contexto de esta user story y proporciona información relevante del proyecto.

TAREAS:
1. Identificar el dominio y área funcional
2. Encontrar patrones similares en el proyecto
3. Identificar dependencias potenciales
4. Sugerir consideraciones técnicas
5. Evaluar la complejidad estimada

FORMATO DE RESPUESTA (JSON):
{
  "domain": "Área funcional (ej: autenticación, pagos, etc.)",
  "similarPatterns": ["patrón1", "patrón2"],
  "dependencies": ["dependencia1", "dependencia2"],
  "technicalConsiderations": ["consideración1", "consideración2"],
  "estimatedComplexity": "low|medium|high",
  "contextSummary": "Resumen del contexto relevante encontrado"
}`;

      const analysis = await this.ragEngine.retrieveAndGenerate(
        contextQuery,
        systemPrompt,
        5
      );

      this.log("Context analysis completed");
      
      return {
        contextAnalysis: analysis,
        query: contextQuery,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      this.log(`Context analysis failed: ${error.message}`, 'error');
      throw error;
    }
  }

  post(shared: SharedStore, prepRes: any, execRes: any): string {
    // Almacenar el análisis de contexto en el estado compartido
    shared.contextAnalysis = execRes.contextAnalysis;
    shared.contextQuery = execRes.query;
    shared.analysisTimestamp = execRes.timestamp;
    
    this.log("Context analysis stored in shared state");
    
    // Continuar al siguiente paso: mejora de la story
    return "improve";
  }
}