// Validation Node - Valida y refina la user story mejorada
import { Node } from "../../core/node.ts";
import type { RAGEngine } from "../../services/rag-engine.ts";
import type { SharedStore, ImprovedStory } from "../../types/index.ts";

/**
 * Nodo que valida la calidad de una user story mejorada
 * y sugiere refinamientos adicionales si es necesario
 */
export class ValidationNode extends Node {
  constructor(private ragEngine: RAGEngine) {
    super("validation", 2);
  }

  prep(shared: SharedStore) {
    this.log("Preparing story validation");
    
    return {
      improvedStory: shared.improvedStory,
      originalStory: shared.userStory || shared.story,
      contextAnalysis: shared.contextAnalysis,
      projectId: shared.projectId
    };
  }

  async exec(data: any): Promise<any> {
    this.log("Validating improved story");
    
    if (!data.improvedStory) {
      throw new Error("Improved story is required for validation");
    }

    try {
      const validation = await this.validateStoryQuality(data.improvedStory);
      
      // Si la validación indica problemas, intentar refinar
      if (validation.qualityScore < 0.7) {
        this.log("Story quality below threshold, attempting refinement", 'warn');
        
        const refinedStory = await this.refineStory(
          data.improvedStory,
          validation.issues,
          data.contextAnalysis
        );
        
        return {
          validatedStory: refinedStory,
          validation: validation,
          wasRefined: true,
          refinementReason: "Quality score below 0.7"
        };
      }
      
      this.log(`Story validation passed with score: ${validation.qualityScore}`);
      
      return {
        validatedStory: data.improvedStory,
        validation: validation,
        wasRefined: false
      };
      
    } catch (error) {
      this.log(`Story validation failed: ${error.message}`, 'error');
      
      // En caso de error, retornar la story sin validar pero marcada
      return {
        validatedStory: data.improvedStory,
        validation: {
          qualityScore: 0.5,
          issues: [`Validation error: ${error.message}`],
          strengths: [],
          suggestions: ["Manual review recommended due to validation error"]
        },
        wasRefined: false,
        validationError: error.message
      };
    }
  }

  post(shared: SharedStore, prepRes: any, execRes: any): string {
    // Almacenar el resultado de la validación
    shared.finalStory = execRes.validatedStory;
    shared.storyValidation = execRes.validation;
    shared.wasRefined = execRes.wasRefined;
    shared.validationTimestamp = new Date().toISOString();
    
    if (execRes.wasRefined) {
      this.log("Story was refined during validation");
    }
    
    this.log("Story validation completed and stored");
    
    // Finalizar el flujo
    return "complete";
  }

  /**
   * Validar la calidad de una user story
   */
  private async validateStoryQuality(story: ImprovedStory): Promise<{
    qualityScore: number;
    issues: string[];
    strengths: string[];
    suggestions: string[];
  }> {
    const systemPrompt = `
Evalúa la calidad de esta user story mejorada según criterios profesionales.

CRITERIOS DE EVALUACIÓN:
1. Formato estándar "Como [usuario], quiero [funcionalidad] para [beneficio]"
2. Claridad y especificidad
3. Criterios de aceptación bien definidos (formato Given-When-Then)
4. Definition of Done completa y realista
5. Complejidad estimada apropiada
6. Test cases sugeridos relevantes

FORMATO DE RESPUESTA (JSON):
{
  "qualityScore": 0.85,
  "issues": ["problema1", "problema2"],
  "strengths": ["fortaleza1", "fortaleza2"], 
  "suggestions": ["sugerencia1", "sugerencia2"]
}

User Story a evaluar:
- Original: "${story.original}"
- Mejorada: "${story.improved}"
- Criterios de Aceptación: ${story.acceptanceCriteria.join(', ')}
- Definition of Done: ${story.definitionOfDone.join(', ')}
- Complejidad: ${story.estimatedComplexity}`;

    try {
      const response = await this.ragEngine.retrieveAndGenerate(
        `validate user story quality: ${story.improved}`,
        systemPrompt,
        3
      );

      // Intentar parsear como JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback si no se puede parsear
      return this.createFallbackValidation(story);
      
    } catch (error) {
      this.log(`Quality validation parsing failed: ${error.message}`, 'warn');
      return this.createFallbackValidation(story);
    }
  }

  /**
   * Refinar una story basado en issues de validación
   */
  private async refineStory(
    story: ImprovedStory,
    issues: string[],
    contextAnalysis?: string
  ): Promise<ImprovedStory> {
    const refinementPrompt = `
Refina esta user story para resolver los siguientes problemas:

PROBLEMAS IDENTIFICADOS:
${issues.map(issue => `- ${issue}`).join('\n')}

STORY ACTUAL:
${JSON.stringify(story, null, 2)}

CONTEXTO ADICIONAL:
${contextAnalysis || 'No disponible'}

INSTRUCCIONES:
1. Mantén la esencia y propósito original
2. Resuelve específicamente cada problema identificado
3. Mejora la claridad y especificidad
4. Asegura criterios de aceptación claros

FORMATO DE RESPUESTA (JSON):
{
  "original": "story original",
  "improved": "story refinada",
  "acceptanceCriteria": ["criterio1", "criterio2"],
  "definitionOfDone": ["item1", "item2"],
  "estimatedComplexity": "low|medium|high",
  "suggestedTestCases": ["test1", "test2"],
  "context": "Explicación de los refinamientos realizados"
}`;

    try {
      const response = await this.ragEngine.retrieveAndGenerate(
        `refine user story: ${story.improved}`,
        refinementPrompt,
        3
      );

      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const refinedStory = JSON.parse(jsonMatch[0]);
        
        this.log("Story successfully refined");
        return refinedStory;
      }
      
    } catch (error) {
      this.log(`Story refinement failed: ${error.message}`, 'error');
    }

    // Si falla el refinamiento, aplicar mejoras básicas
    return this.applyBasicRefinements(story, issues);
  }

  /**
   * Crear validación de fallback
   */
  private createFallbackValidation(story: ImprovedStory): {
    qualityScore: number;
    issues: string[];
    strengths: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const strengths: string[] = [];
    
    // Validaciones básicas
    if (!story.improved.toLowerCase().includes('como ') || 
        !story.improved.toLowerCase().includes(' quiero ') ||
        !story.improved.toLowerCase().includes(' para ')) {
      issues.push("No sigue el formato estándar 'Como [usuario], quiero [funcionalidad] para [beneficio]'");
    } else {
      strengths.push("Sigue el formato estándar de user story");
    }

    if (story.acceptanceCriteria.length === 0) {
      issues.push("No tiene criterios de aceptación definidos");
    } else if (story.acceptanceCriteria.length < 2) {
      issues.push("Tiene muy pocos criterios de aceptación");
    } else {
      strengths.push("Incluye criterios de aceptación");
    }

    if (story.definitionOfDone.length === 0) {
      issues.push("No tiene Definition of Done");
    } else {
      strengths.push("Incluye Definition of Done");
    }

    const qualityScore = Math.max(0.3, (strengths.length * 0.3) - (issues.length * 0.2));

    return {
      qualityScore,
      issues,
      strengths,
      suggestions: issues.map(issue => `Resolver: ${issue}`)
    };
  }

  /**
   * Aplicar refinamientos básicos
   */
  private applyBasicRefinements(story: ImprovedStory, issues: string[]): ImprovedStory {
    let refinedStory = { ...story };

    // Si no tiene formato estándar, intentar aplicarlo
    if (issues.some(issue => issue.includes('formato estándar'))) {
      if (!refinedStory.improved.toLowerCase().includes('como ')) {
        refinedStory.improved = `Como usuario, quiero ${refinedStory.improved.toLowerCase()} para obtener el beneficio esperado.`;
      }
    }

    // Añadir contexto de refinamiento
    refinedStory.context = `${refinedStory.context}\n\nRefinamientos aplicados: ${issues.join(', ')}`;

    return refinedStory;
  }
}