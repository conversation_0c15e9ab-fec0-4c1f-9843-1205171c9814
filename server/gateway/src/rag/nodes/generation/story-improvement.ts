// Story Improvement Node - Mejora una user story usando contexto RAG
import { Node } from "../../core/node.ts";
import type { RAGEngine } from "../../services/rag-engine.ts";
import type { SharedStore, ImprovedStory } from "../../types/index.ts";

/**
 * Nodo que mejora una user story utilizando el contexto analizado
 * y patrones exitosos del proyecto
 */
export class StoryImprovementNode extends Node {
  constructor(private ragEngine: RAGEngine) {
    super("story-improvement", 3);
  }

  prep(shared: SharedStore) {
    this.log("Preparing story improvement");
    
    return {
      originalStory: shared.userStory || shared.story,
      contextAnalysis: shared.contextAnalysis,
      projectId: shared.projectId,
      additionalContext: shared.additionalContext
    };
  }

  async exec(data: any): Promise<ImprovedStory> {
    this.log(`Improving story: "${data.originalStory}"`);
    
    if (!data.originalStory) {
      throw new Error("Original user story is required");
    }

    try {
      // Usar el motor RAG para mejorar la story con contexto
      const improvedStory = await this.ragEngine.improveStory(
        data.originalStory,
        data.contextAnalysis || data.additionalContext
      );

      this.log("Story improvement completed");
      this.log(`Original: "${data.originalStory}"`);
      this.log(`Improved: "${improvedStory.improved}"`);
      
      return improvedStory;
      
    } catch (error) {
      this.log(`Story improvement failed: ${error.message}`, 'error');
      
      // Fallback: mejora básica sin contexto RAG
      const fallbackStory: ImprovedStory = {
        original: data.originalStory,
        improved: this.basicImprovement(data.originalStory),
        acceptanceCriteria: this.generateBasicCriteria(data.originalStory),
        definitionOfDone: [
          "Implementación completada",
          "Tests unitarios escritos y pasando",
          "Code review aprobado",
          "Documentación actualizada"
        ],
        estimatedComplexity: "medium" as const,
        suggestedTestCases: [
          "Test del flujo principal (happy path)",
          "Test de validaciones de entrada",
          "Test de casos límite"
        ],
        context: `Mejora realizada sin contexto RAG debido a error: ${error.message}`
      };
      
      this.log("Using fallback story improvement", 'warn');
      return fallbackStory;
    }
  }

  post(shared: SharedStore, prepRes: any, execRes: any): string {
    // Almacenar la story mejorada en el estado compartido
    shared.improvedStory = execRes;
    shared.improvementTimestamp = new Date().toISOString();
    
    this.log("Improved story stored in shared state");
    
    // Continuar al siguiente paso: validación
    return "validate";
  }

  /**
   * Mejora básica sin contexto RAG (fallback)
   */
  private basicImprovement(story: string): string {
    // Verificar si ya tiene el formato estándar
    const hasStandardFormat = /^como\s+.+\s+quiero\s+.+\s+para\s+.+/i.test(story.trim());
    
    if (hasStandardFormat) {
      return story.trim();
    }

    // Intentar extraer componentes básicos
    const lowerStory = story.toLowerCase();
    
    // Detectar usuario implícito
    let userType = "usuario";
    if (lowerStory.includes("admin")) userType = "administrador";
    else if (lowerStory.includes("cliente")) userType = "cliente";
    else if (lowerStory.includes("empleado")) userType = "empleado";
    
    // Detectar acción principal
    let action = story.trim();
    if (lowerStory.startsWith("necesito") || lowerStory.startsWith("requiero")) {
      action = story.replace(/^(necesito|requiero)\s+/i, "");
    }
    
    return `Como ${userType}, quiero ${action} para obtener el resultado esperado.`;
  }

  /**
   * Generar criterios de aceptación básicos
   */
  private generateBasicCriteria(story: string): string[] {
    return [
      `Dado que soy un usuario válido, cuando ejecuto la funcionalidad, entonces obtengo el resultado esperado`,
      `Dado que proporciono datos inválidos, cuando intento ejecutar la funcionalidad, entonces recibo un mensaje de error claro`,
      `Dado que la funcionalidad se ejecuta correctamente, cuando reviso el resultado, entonces cumple con los requisitos especificados`
    ];
  }
}