// PocketFlow Base Node Implementation
import type { SharedStore, ExecutionResult } from "../types/index.ts";
import type { INode } from "./types.ts";

/**
 * Clase base abstracta para todos los nodos de PocketFlow
 * Implementa el patrón Template Method
 */
export abstract class Node implements INode {
  constructor(
    public readonly id: string,
    public readonly retries: number = 3
  ) {}

  /**
   * Preparación de datos (Single Responsibility)
   * Puede ser sobrescrito por clases hijas
   */
  prep?(shared: SharedStore): any {
    return {};
  }

  /**
   * Ejecución principal - debe ser implementado por clases hijas
   */
  abstract exec(preparedData: any): Promise<any>;

  /**
   * Post-procesamiento (Open/Closed Principle)
   * Puede ser sobrescrito por clases hijas
   */
  post?(shared: SharedStore, prepRes: any, execRes: any): string {
    return "default";
  }

  /**
   * Template method pattern - define el flujo de ejecución
   */
  async execute(shared: SharedStore): Promise<ExecutionResult> {
    let attempt = 0;
    
    while (attempt < this.retries) {
      try {
        // Fase de preparación
        const prepData = this.prep?.(shared) || {};
        
        // Fase de ejecución
        const execResult = await this.exec(prepData);
        
        // Fase de post-procesamiento
        const nextAction = this.post?.(shared, prepData, execResult) || "default";
        
        return {
          success: true,
          data: execResult,
          nextAction
        };
      } catch (error) {
        attempt++;
        
        if (attempt >= this.retries) {
          return {
            success: false,
            error: `Node ${this.id} failed after ${this.retries} attempts: ${error.message}`
          };
        }
        
        // Esperar antes del siguiente intento (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
    
    return {
      success: false,
      error: `Node ${this.id} failed after ${this.retries} attempts`
    };
  }

  /**
   * Validar entrada - puede ser sobrescrito por clases hijas
   */
  protected validate(data: any): boolean {
    return true;
  }

  /**
   * Logging helper
   */
  protected log(message: string, level: 'info' | 'warn' | 'error' = 'info'): void {
    const timestamp = new Date().toISOString();
    console[level](`[${timestamp}] Node ${this.id}: ${message}`);
  }
}