// PocketFlow Core Types
import type { SharedStore, ExecutionResult } from "../types/index.ts";

// Interfaz base para nodos
export interface INode {
  readonly id: string;
  readonly retries: number;
  execute(shared: SharedStore): Promise<ExecutionResult>;
}

// Configuración de Flow
export interface FlowConfig {
  name: string;
  description?: string;
  timeout?: number;
  maxRetries?: number;
}

// Conexión entre nodos
export interface NodeConnection {
  fromNode: string;
  toNode: string;
  action: string;
}

// Estado de ejecución del flow
export interface FlowExecution {
  flowId: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed' | 'timeout';
  currentNodes: string[];
  visitedNodes: Set<string>;
  errors: string[];
  shared: SharedStore;
}

// Resultado del flow
export interface FlowResult {
  success: boolean;
  executionTime: number;
  nodesExecuted: string[];
  finalState: SharedStore;
  errors?: string[];
}