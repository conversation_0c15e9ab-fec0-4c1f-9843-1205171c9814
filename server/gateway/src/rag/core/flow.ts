// PocketFlow Engine Implementation
import type { SharedStore, ExecutionResult } from "../types/index.ts";
import type { INode, FlowConfig, NodeConnection, FlowExecution, FlowResult } from "./types.ts";

/**
 * Motor de flujos PocketFlow
 * Implementa el patrón de orquestación de nodos
 */
export class Flow {
  private nodes: Map<string, INode> = new Map();
  private connections: Map<string, string[]> = new Map();
  private config: FlowConfig;

  constructor(config?: FlowConfig) {
    this.config = {
      name: config?.name || 'unnamed-flow',
      description: config?.description,
      timeout: config?.timeout || 300000, // 5 minutos default
      maxRetries: config?.maxRetries || 3
    };
  }

  /**
   * Interface Segregation - métodos específicos para construcción
   */
  addNode(node: INode): Flow {
    this.nodes.set(node.id, node);
    this.log(`Node added: ${node.id}`);
    return this;
  }

  connect(fromNode: string, toNode: string, action: string = "default"): Flow {
    const key = `${fromNode}:${action}`;
    
    if (!this.connections.has(key)) {
      this.connections.set(key, []);
    }
    
    this.connections.get(key)!.push(toNode);
    this.log(`Connection added: ${fromNode} --[${action}]--> ${toNode}`);
    return this;
  }

  /**
   * Validar la estructura del flow antes de ejecutar
   */
  private validate(startNode: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validar que el nodo inicial existe
    if (!this.nodes.has(startNode)) {
      errors.push(`Start node '${startNode}' does not exist`);
    }

    // Validar que todas las conexiones apuntan a nodos existentes
    for (const [connection, targetNodes] of this.connections.entries()) {
      const [sourceNode] = connection.split(':');
      
      if (!this.nodes.has(sourceNode)) {
        errors.push(`Source node '${sourceNode}' in connection does not exist`);
      }

      for (const targetNode of targetNodes) {
        if (!this.nodes.has(targetNode)) {
          errors.push(`Target node '${targetNode}' in connection does not exist`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Ejecutar el flow completo
   */
  async run(startNode: string, shared: SharedStore = {}): Promise<FlowResult> {
    const startTime = new Date();
    
    // Validar estructura del flow
    const validation = this.validate(startNode);
    if (!validation.valid) {
      return {
        success: false,
        executionTime: 0,
        nodesExecuted: [],
        finalState: shared,
        errors: validation.errors
      };
    }

    const execution: FlowExecution = {
      flowId: `${this.config.name}-${Date.now()}`,
      startTime,
      status: 'running',
      currentNodes: [startNode],
      visitedNodes: new Set<string>(),
      errors: [],
      shared: { ...shared }
    };

    this.log(`Starting flow execution: ${execution.flowId}`);

    try {
      // Configurar timeout
      const timeoutPromise = new Promise<FlowResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Flow timeout after ${this.config.timeout}ms`));
        }, this.config.timeout);
      });

      const executionPromise = this.executeFlow(execution);
      
      const result = await Promise.race([executionPromise, timeoutPromise]);
      
      execution.endTime = new Date();
      execution.status = result.success ? 'completed' : 'failed';
      
      this.log(`Flow execution ${result.success ? 'completed' : 'failed'}: ${execution.flowId}`);
      
      return result;
      
    } catch (error) {
      execution.endTime = new Date();
      execution.status = error.message.includes('timeout') ? 'timeout' : 'failed';
      execution.errors.push(error.message);
      
      this.log(`Flow execution failed: ${error.message}`, 'error');
      
      return {
        success: false,
        executionTime: execution.endTime.getTime() - startTime.getTime(),
        nodesExecuted: Array.from(execution.visitedNodes),
        finalState: execution.shared,
        errors: execution.errors
      };
    }
  }

  /**
   * Lógica principal de ejecución del flow
   */
  private async executeFlow(execution: FlowExecution): Promise<FlowResult> {
    const maxIterations = this.nodes.size * 10; // Prevenir loops infinitos
    let iteration = 0;

    while (execution.currentNodes.length > 0 && iteration < maxIterations) {
      const nextNodes: string[] = [];

      // Ejecutar nodos en paralelo si es posible
      const nodePromises = execution.currentNodes.map(async (nodeId) => {
        if (execution.visitedNodes.has(nodeId)) {
          return null; // Skip already visited nodes
        }

        execution.visitedNodes.add(nodeId);

        const node = this.nodes.get(nodeId);
        if (!node) {
          throw new Error(`Node ${nodeId} not found`);
        }

        this.log(`Executing node: ${nodeId}`);
        const result = await node.execute(execution.shared);
        
        if (!result.success) {
          execution.errors.push(`Node ${nodeId}: ${result.error}`);
          throw new Error(`Node ${nodeId} failed: ${result.error}`);
        }

        // Buscar siguientes nodos basado en el resultado
        const connectionKey = `${nodeId}:${result.nextAction}`;
        const connections = this.connections.get(connectionKey) || [];
        
        this.log(`Node ${nodeId} completed with action: ${result.nextAction}, next nodes: [${connections.join(', ')}]`);
        
        return connections;
      });

      const results = await Promise.all(nodePromises);
      
      // Recopilar siguientes nodos
      for (const connections of results) {
        if (connections) {
          nextNodes.push(...connections);
        }
      }

      execution.currentNodes = [...new Set(nextNodes)]; // Remove duplicates
      iteration++;
    }

    if (iteration >= maxIterations) {
      throw new Error(`Flow exceeded maximum iterations (${maxIterations}), possible infinite loop`);
    }

    const endTime = new Date();
    
    return {
      success: true,
      executionTime: endTime.getTime() - execution.startTime.getTime(),
      nodesExecuted: Array.from(execution.visitedNodes),
      finalState: execution.shared,
      errors: execution.errors.length > 0 ? execution.errors : undefined
    };
  }

  /**
   * Obtener estadísticas del flow
   */
  getStats(): {
    nodeCount: number;
    connectionCount: number;
    config: FlowConfig;
  } {
    return {
      nodeCount: this.nodes.size,
      connectionCount: Array.from(this.connections.values()).reduce((sum, arr) => sum + arr.length, 0),
      config: this.config
    };
  }

  /**
   * Exportar definición del flow para debugging
   */
  export(): {
    nodes: string[];
    connections: NodeConnection[];
    config: FlowConfig;
  } {
    const connections: NodeConnection[] = [];
    
    for (const [key, targets] of this.connections.entries()) {
      const [fromNode, action] = key.split(':');
      for (const toNode of targets) {
        connections.push({ fromNode, toNode, action });
      }
    }

    return {
      nodes: Array.from(this.nodes.keys()),
      connections,
      config: this.config
    };
  }

  /**
   * Logging helper
   */
  private log(message: string, level: 'info' | 'warn' | 'error' = 'info'): void {
    const timestamp = new Date().toISOString();
    console[level](`[${timestamp}] Flow ${this.config.name}: ${message}`);
  }
}