// LLM Provider Implementation using OpenRouter
import type { ILLMProvider, LLMConfig } from "../types/index.ts";

/**
 * Proveedor de LLM usando OpenRouter (compatible con OpenAI API)
 */
export class OpenRouterProvider implements ILLMProvider {
  private apiKey: string;
  private model: string;
  private temperature: number;
  private maxTokens: number;
  private baseUrl: string;

  constructor(config: LLMConfig, baseUrl: string = "https://openrouter.ai/api/v1") {
    this.apiKey = config.apiKey;
    this.model = config.model;
    this.temperature = config.temperature || 0.7;
    this.maxTokens = config.maxTokens || 2000;
    this.baseUrl = baseUrl;
  }

  /**
   * Generar respuesta de texto
   */
  async generate(prompt: string, context: string[]): Promise<string> {
    const fullPrompt = this.buildPrompt(prompt, context);
    
    console.log(`Generating response with model: ${this.model}`);
    console.log(`Context chunks: ${context.length}`);
    
    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://aery.ai",
          "X-Title": "AERY RAG Service"
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: "system",
              content: "Eres un experto en desarrollo de software y mejora de user stories. Proporciona respuestas precisas, estructuradas y útiles."
            },
            {
              role: "user", 
              content: fullPrompt
            }
          ],
          temperature: this.temperature,
          max_tokens: this.maxTokens,
          stream: false
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`LLM API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error("Invalid response from LLM API");
      }

      const result = data.choices[0].message.content;
      console.log(`Generated response: ${result.length} characters`);
      
      return result;
      
    } catch (error) {
      console.error('LLM generation failed:', error);
      throw new Error(`LLM generation failed: ${error.message}`);
    }
  }

  /**
   * Generar respuesta estructurada (JSON)
   */
  async generateStructured<T>(
    prompt: string, 
    context: string[], 
    schema?: any
  ): Promise<T> {
    const structuredPrompt = `${prompt}

IMPORTANTE: Responde ÚNICAMENTE con un JSON válido que siga esta estructura:
${schema ? JSON.stringify(schema, null, 2) : '{}'}

No incluyas explicaciones adicionales, solo el JSON.`;

    const response = await this.generate(structuredPrompt, context);
    
    try {
      // Extraer JSON del response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : response;
      
      return JSON.parse(jsonStr) as T;
    } catch (error) {
      console.error('Failed to parse structured response:', response);
      throw new Error(`Failed to parse structured response: ${error.message}`);
    }
  }

  /**
   * Construir prompt completo con contexto
   */
  private buildPrompt(prompt: string, context: string[]): string {
    if (context.length === 0) {
      return prompt;
    }

    const contextText = context
      .map((chunk, idx) => `[Contexto ${idx + 1}]\n${chunk}`)
      .join('\n\n');

    return `Contexto relevante:
${contextText}

---

Pregunta/Solicitud:
${prompt}

Por favor, utiliza el contexto proporcionado para dar una respuesta precisa y útil.`;
  }

  /**
   * Estimar tokens de un texto (aproximación)
   */
  private estimateTokens(text: string): number {
    // Aproximación: 1 token ≈ 4 caracteres en inglés, 3 en español
    return Math.ceil(text.length / 3.5);
  }

  /**
   * Truncar contexto si excede límites de tokens
   */
  private truncateContext(context: string[], maxTokens: number = 6000): string[] {
    let totalTokens = 0;
    const truncatedContext: string[] = [];

    for (const chunk of context) {
      const chunkTokens = this.estimateTokens(chunk);
      
      if (totalTokens + chunkTokens > maxTokens) {
        break;
      }
      
      truncatedContext.push(chunk);
      totalTokens += chunkTokens;
    }

    if (truncatedContext.length < context.length) {
      console.warn(`Context truncated: ${context.length} -> ${truncatedContext.length} chunks`);
    }

    return truncatedContext;
  }
}

/**
 * Proveedor directo de OpenAI (alternativa)
 */
export class OpenAIProvider implements ILLMProvider {
  private apiKey: string;
  private model: string;
  private temperature: number;
  private maxTokens: number;

  constructor(config: LLMConfig) {
    this.apiKey = config.apiKey;
    this.model = config.model || 'gpt-4';
    this.temperature = config.temperature || 0.7;
    this.maxTokens = config.maxTokens || 2000;
  }

  async generate(prompt: string, context: string[]): Promise<string> {
    const fullPrompt = this.buildPrompt(prompt, context);
    
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        model: this.model,
        messages: [
          {
            role: "system",
            content: "Eres un experto en desarrollo de software y mejora de user stories."
          },
          {
            role: "user",
            content: fullPrompt
          }
        ],
        temperature: this.temperature,
        max_tokens: this.maxTokens
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async generateStructured<T>(prompt: string, context: string[], schema?: any): Promise<T> {
    const structuredPrompt = `${prompt}\n\nResponde con JSON válido siguiendo esta estructura:\n${JSON.stringify(schema, null, 2)}`;
    const response = await this.generate(structuredPrompt, context);
    
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? jsonMatch[0] : response;
      return JSON.parse(jsonStr) as T;
    } catch (error) {
      throw new Error(`Failed to parse structured response: ${error.message}`);
    }
  }

  private buildPrompt(prompt: string, context: string[]): string {
    if (context.length === 0) return prompt;

    const contextText = context.join('\n\n');
    return `Contexto:\n${contextText}\n\nPregunta: ${prompt}`;
  }
}