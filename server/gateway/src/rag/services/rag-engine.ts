// RAG Engine Implementation
import type { 
  IVectorStore, 
  ILLMProvider, 
  SearchResult,
  ImprovedStory,
  GeneratedTestCase,
  EdgeCase
} from "../types/index.ts";

/**
 * Motor RAG que combina retrieval y generation
 * Implementa el patrón Strategy para diferentes tipos de consultas
 */
export class RAGEngine {
  constructor(
    private vectorStore: IVectorStore,
    private llmProvider: ILLMProvider
  ) {}

  /**
   * Retrieval + Generation básico
   */
  async retrieveAndGenerate(
    query: string, 
    systemPrompt: string,
    topK: number = 5
  ): Promise<string> {
    console.log(`RAG query: "${query}" (top ${topK})`);
    
    try {
      // Fase de Retrieval
      const relevantDocs = await this.vectorStore.search(query, topK);
      const context = relevantDocs.map(doc => doc.document.content);
      
      console.log(`Retrieved ${context.length} relevant documents`);
      
      // Fase de Generation
      const fullPrompt = `${systemPrompt}\n\nConsulta: ${query}`;
      const response = await this.llmProvider.generate(fullPrompt, context);
      
      return response;
      
    } catch (error) {
      console.error('RAG operation failed:', error);
      throw new Error(`RAG operation failed: ${error.message}`);
    }
  }

  /**
   * Mejorar User Story específicamente
   */
  async improveStory(
    originalStory: string,
    additionalContext?: string
  ): Promise<ImprovedStory> {
    const query = `improve user story: ${originalStory}`;
    const contextQuery = additionalContext ? 
      `${originalStory} ${additionalContext}` : 
      originalStory;

    const relevantDocs = await this.vectorStore.search(contextQuery, 5);
    const context = relevantDocs.map(doc => doc.document.content);

    const systemPrompt = `
Eres un experto Product Owner y analista de software. Tu tarea es mejorar user stories siguiendo las mejores prácticas.

FORMATO REQUERIDO para la respuesta (JSON):
{
  "original": "story original",
  "improved": "Como [tipo de usuario], quiero [funcionalidad] para [beneficio/valor]",
  "acceptanceCriteria": [
    "Criterio 1 con formato Given-When-Then",
    "Criterio 2...",
    "..."
  ],
  "definitionOfDone": [
    "Elemento 1 de DoD",
    "Elemento 2 de DoD",
    "..."
  ],
  "estimatedComplexity": "low|medium|high",
  "suggestedTestCases": [
    "Test case 1",
    "Test case 2",
    "..."
  ],
  "context": "Contexto y justificación de las mejoras"
}

CRITERIOS DE MEJORA:
1. Formato estándar: "Como [usuario], quiero [funcionalidad] para [beneficio]"
2. Criterios de aceptación claros con formato Given-When-Then
3. Definition of Done técnica y funcional
4. Estimación de complejidad realista
5. Test cases sugeridos
6. Contexto que explique las mejoras realizadas

Utiliza el contexto proporcionado para entender patrones exitosos del proyecto.`;

    try {
      const response = await this.llmProvider.generateStructured<ImprovedStory>(
        systemPrompt,
        context,
        {
          original: "string",
          improved: "string", 
          acceptanceCriteria: ["string"],
          definitionOfDone: ["string"],
          estimatedComplexity: "low|medium|high",
          suggestedTestCases: ["string"],
          context: "string"
        }
      );

      // Asegurar que tenemos la story original
      response.original = originalStory;
      
      return response;
      
    } catch (error) {
      console.error('Story improvement failed:', error);
      
      // Fallback: respuesta básica sin contexto
      return {
        original: originalStory,
        improved: originalStory,
        acceptanceCriteria: ["Definir criterios de aceptación"],
        definitionOfDone: ["Completar implementación", "Pasar pruebas", "Code review"],
        estimatedComplexity: "medium" as const,
        suggestedTestCases: ["Test happy path", "Test casos límite"],
        context: "Error en mejora automática, requiere revisión manual"
      };
    }
  }

  /**
   * Generar Test Cases específicamente
   */
  async generateTestCases(
    userStory: string,
    acceptanceCriteria?: string[]
  ): Promise<{ testCases: GeneratedTestCase[]; edgeCases: EdgeCase[] }> {
    const query = `generate test cases for: ${userStory}`;
    const contextQuery = acceptanceCriteria ? 
      `${userStory} ${acceptanceCriteria.join(' ')}` : 
      userStory;

    const relevantDocs = await this.vectorStore.search(contextQuery, 5);
    const context = relevantDocs.map(doc => doc.document.content);

    const systemPrompt = `
Eres un experto QA Engineer y Test Architect. Genera test cases comprehensivos para la user story.

FORMATO REQUERIDO (JSON):
{
  "testCases": [
    {
      "title": "Título descriptivo del test",
      "description": "Descripción detallada",
      "type": "happy_path|negative|boundary|integration",
      "steps": [
        {
          "step": 1,
          "action": "Acción a realizar",
          "data": "Datos de entrada (opcional)",
          "expectedBehavior": "Comportamiento esperado"
        }
      ],
      "expectedResult": "Resultado final esperado",
      "priority": "high|medium|low"
    }
  ],
  "edgeCases": [
    {
      "scenario": "Descripción del edge case",
      "description": "Explicación detallada",
      "riskLevel": "high|medium|low",
      "suggestedHandling": "Cómo manejar este caso",
      "testApproach": "Enfoque de testing"
    }
  ]
}

TIPOS DE TEST CASES A INCLUIR:
1. Happy Path: Flujo normal exitoso
2. Negative Testing: Casos de error y validaciones
3. Boundary Testing: Valores límite y extremos
4. Integration Testing: Interacción con otros componentes

EDGE CASES A CONSIDERAR:
- Datos inválidos o malformados
- Límites de sistema (memoria, conexiones, etc.)
- Concurrencia y condiciones de carrera
- Fallos de red o servicios externos
- Estados inconsistentes`;

    try {
      const response = await this.llmProvider.generateStructured<{
        testCases: GeneratedTestCase[];
        edgeCases: EdgeCase[];
      }>(
        systemPrompt,
        context,
        {
          testCases: [{
            title: "string",
            description: "string",
            type: "happy_path|negative|boundary|integration",
            steps: [{
              step: "number",
              action: "string",
              data: "string",
              expectedBehavior: "string"
            }],
            expectedResult: "string",
            priority: "high|medium|low"
          }],
          edgeCases: [{
            scenario: "string",
            description: "string", 
            riskLevel: "high|medium|low",
            suggestedHandling: "string",
            testApproach: "string"
          }]
        }
      );

      return response;
      
    } catch (error) {
      console.error('Test case generation failed:', error);
      
      // Fallback básico
      return {
        testCases: [{
          title: "Test básico de funcionalidad",
          description: "Verificar que la funcionalidad funciona correctamente",
          type: "happy_path" as const,
          steps: [
            {
              step: 1,
              action: "Ejecutar funcionalidad principal",
              expectedBehavior: "La funcionalidad se ejecuta sin errores"
            }
          ],
          expectedResult: "Funcionalidad completada exitosamente",
          priority: "high" as const
        }],
        edgeCases: [{
          scenario: "Datos de entrada inválidos",
          description: "Qué sucede con datos incorrectos",
          riskLevel: "medium" as const,
          suggestedHandling: "Validar entrada y mostrar mensaje de error",
          testApproach: "Probar con diferentes tipos de datos inválidos"
        }]
      };
    }
  }

  /**
   * Análisis de contexto para mejorar retrieval
   */
  async analyzeContext(
    query: string,
    projectId: string
  ): Promise<{
    relevantTopics: string[];
    suggestedQueries: string[];
    confidence: number;
  }> {
    const relevantDocs = await this.vectorStore.search(query, 10);
    
    const systemPrompt = `
Analiza el contexto de esta consulta y los documentos relevantes encontrados.

FORMATO DE RESPUESTA (JSON):
{
  "relevantTopics": ["tópico1", "tópico2", "..."],
  "suggestedQueries": ["consulta sugerida 1", "consulta sugerida 2", "..."],
  "confidence": 0.85
}

Identifica:
1. Temas principales relacionados
2. Consultas alternativas que podrían ser útiles  
3. Nivel de confianza en la relevancia del contexto (0-1)`;

    try {
      const context = relevantDocs.map(doc => doc.document.content);
      
      const analysis = await this.llmProvider.generateStructured<{
        relevantTopics: string[];
        suggestedQueries: string[];
        confidence: number;
      }>(systemPrompt, context);

      return analysis;
      
    } catch (error) {
      console.error('Context analysis failed:', error);
      
      return {
        relevantTopics: ["general"],
        suggestedQueries: [query],
        confidence: 0.5
      };
    }
  }

  /**
   * Obtener estadísticas del motor RAG
   */
  async getStats(): Promise<{
    vectorStore: { totalDocuments: number; lastUpdated: Date };
    performance: {
      averageRetrievalTime: number;
      averageGenerationTime: number;
      successRate: number;
    };
  }> {
    const vectorStats = await this.vectorStore.getStats();
    
    return {
      vectorStore: vectorStats,
      performance: {
        averageRetrievalTime: 500, // ms - placeholder
        averageGenerationTime: 2000, // ms - placeholder  
        successRate: 0.95 // placeholder
      }
    };
  }
}