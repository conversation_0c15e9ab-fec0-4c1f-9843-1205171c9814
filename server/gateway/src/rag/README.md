# 🤖 AERY RAG Service

Servicio RAG (Retrieval-Augmented Generation) integrado en el gateway de AERY para mejora de user stories y generación de test cases usando PocketFlow y arquitectura modular.

## 📋 Características

- ✅ **Proyectos independientes**: Cada proyecto mantiene su propio knowledge base
- ✅ **PocketFlow integration**: Motor de flujos minimalista basado en 100 líneas
- ✅ **Mejora de User Stories**: Análisis contextual y mejora automática
- ✅ **Generación de Test Cases**: Test cases comprehensivos y edge cases
- ✅ **Vector Search**: Búsqueda semántica con OpenAI embeddings
- ✅ **Multi-LLM**: Soporte para OpenAI y OpenRouter
- ✅ **API REST**: Endpoints completos para integración
- ✅ **Validación robusta**: Validación de entrada y manejo de errores

## 🏗️ Arquitectura

```
src/rag/
├── core/                   # PocketFlow core
│   ├── flow.ts            # Motor de flujos
│   ├── node.ts            # Clase base para nodos
│   └── types.ts           # Tipos del core
├── services/              # Servicios principales
│   ├── rag-engine.ts      # Motor RAG principal
│   ├── project-manager.ts # Gestión de proyectos
│   ├── vector-store.ts    # Almacenamiento vectorial
│   └── llm-provider.ts    # Proveedores de LLM
├── flows/                 # Flujos de PocketFlow
│   ├── story-improvement.ts
│   └── test-generation.ts
├── nodes/                 # Nodos especializados
│   ├── retrieval/
│   │   └── context-analysis.ts
│   └── generation/
│       ├── story-improvement.ts
│       └── validation.ts
└── types/                 # Definiciones de tipos
    └── index.ts
```

## 🚀 API Endpoints

### Proyectos

#### `POST /api/rag/projects`
Crear un nuevo proyecto RAG.

```json
{
  "name": "E-commerce Platform",
  "description": "Proyecto de e-commerce con catálogo de productos",
  "initialDocuments": [
    {
      "content": "Como cliente, quiero agregar productos al carrito...",
      "type": "story",
      "source": "initial-setup"
    }
  ]
}
```

#### `GET /api/rag/projects`
Listar todos los proyectos.

#### `GET /api/rag/projects/:id/knowledge`
Obtener estadísticas del knowledge base del proyecto.

#### `POST /api/rag/projects/:id/knowledge`
Añadir documentos al knowledge base.

```json
{
  "documents": [
    {
      "content": "Contenido del documento...",
      "type": "story|requirement|test|documentation",
      "source": "manual",
      "metadata": {}
    }
  ]
}
```

#### `DELETE /api/rag/projects/:id/knowledge`
Eliminar proyecto completo.

### User Stories

#### `POST /api/rag/projects/:id/stories/improve`
Mejorar una user story.

```json
{
  "story": "Quiero buscar productos",
  "context": "E-commerce platform con catálogo"
}
```

**Respuesta:**
```json
{
  "success": true,
  "improvedStory": {
    "original": "Quiero buscar productos",
    "improved": "Como cliente, quiero buscar productos por nombre y categoría para encontrar fácilmente lo que necesito",
    "acceptanceCriteria": [
      "Dado que estoy en la página principal, cuando escribo en el campo de búsqueda, entonces veo sugerencias en tiempo real",
      "Dado que busco un producto, cuando selecciono una categoría, entonces los resultados se filtran apropiadamente"
    ],
    "definitionOfDone": [
      "Implementación de búsqueda completada",
      "Tests unitarios y de integración pasando",
      "UI/UX revisado y aprobado",
      "Documentación actualizada"
    ],
    "estimatedComplexity": "medium",
    "suggestedTestCases": [
      "Test de búsqueda por texto",
      "Test de filtrado por categoría",
      "Test de búsqueda vacía"
    ],
    "context": "Mejorada basada en patrones de e-commerce del proyecto"
  },
  "suggestions": ["Considerar implementar búsqueda por voz"],
  "confidence": 0.87
}
```

#### `POST /api/rag/projects/:id/stories/tests`
Generar test cases para una user story.

```json
{
  "story": "Como cliente, quiero filtrar productos por precio para encontrar opciones en mi presupuesto",
  "acceptanceCriteria": [
    "El usuario puede definir rango de precios",
    "Los filtros se aplican inmediatamente"
  ]
}
```

**Respuesta:**
```json
{
  "success": true,
  "testCases": [
    {
      "title": "Test de filtro de precio básico",
      "description": "Verificar que el filtro de precio funciona correctamente",
      "type": "happy_path",
      "steps": [
        {
          "step": 1,
          "action": "Navegar a la página de productos",
          "expectedBehavior": "La página se carga con lista de productos"
        },
        {
          "step": 2,
          "action": "Establecer rango de precio $10-$50",
          "expectedBehavior": "Solo se muestran productos en ese rango"
        }
      ],
      "expectedResult": "Productos filtrados correctamente por precio",
      "priority": "high"
    }
  ],
  "edgeCases": [
    {
      "scenario": "Rango de precio inválido",
      "description": "Precio mínimo mayor que precio máximo",
      "riskLevel": "medium",
      "suggestedHandling": "Mostrar mensaje de error y resetear filtros",
      "testApproach": "Probar diferentes combinaciones inválidas"
    }
  ],
  "coverage": {
    "functionalCoverage": 0.85,
    "boundaryTestsIncluded": true,
    "negativeTestsIncluded": true
  }
}
```

## 🔧 Configuración

### Variables de Entorno

```bash
# RAG Configuration
OPENAI_API_KEY=sk-...                    # Para embeddings
OPENROUTER_API_KEY=sk-or-v1-...         # Para LLM generation
EMBEDDING_MODEL=text-embedding-3-small   # Modelo de embeddings
RAG_TOP_K=5                              # Documentos relevantes por consulta
RAG_CHUNK_SIZE=1000                      # Tamaño de chunks de texto
RAG_CHUNK_OVERLAP=200                    # Solapamiento entre chunks
```

### Inicialización

El servicio se inicializa automáticamente con el gateway:

```typescript
// En main.ts del gateway, el ProjectManager ya está configurado
import { ProjectManager } from "./src/rag/services/project-manager.ts";

const projectManager = new ProjectManager();
```

## 🎯 Flujos de PocketFlow

### Story Improvement Flow

1. **Context Analysis**: Analiza el contexto en el knowledge base
2. **Story Improvement**: Mejora la story usando patrones del proyecto
3. **Validation**: Valida calidad y refina si es necesario

### Test Generation Flow

1. **Requirement Analysis**: Analiza requerimientos de testing
2. **Test Generation**: Genera test cases comprehensivos
3. **Test Validation**: Valida calidad y cobertura de tests

## 📊 Monitoreo y Métricas

### Métricas Disponibles

- Tiempo de ejecución de flujos
- Número de documentos en knowledge base
- Score de calidad de stories mejoradas
- Cobertura de test cases generados
- Tasa de éxito de operaciones RAG

### Logging

Todos los componentes incluyen logging detallado:

```typescript
// Ejemplo de logs del sistema
[2024-01-15T10:30:00.000Z] Flow story-improvement-flow: Starting flow execution: story-improvement-flow-1705316200000
[2024-01-15T10:30:01.000Z] Node context-analysis: Analyzing context for story: "Quiero buscar productos"
[2024-01-15T10:30:03.000Z] Node story-improvement: Story improvement completed
[2024-01-15T10:30:04.000Z] Flow story-improvement-flow: Flow execution completed: story-improvement-flow-1705316200000
```

## 🧪 Testing

### Script de Pruebas

```bash
# Ejecutar script de pruebas completo
node test-rag-implementation.js

# O probar manualmente con curl
curl -X POST http://localhost:8000/api/rag/projects \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Project", "description": "Proyecto de prueba"}'
```

### Tests Incluidos

- ✅ Creación y gestión de proyectos
- ✅ Adición de documentos al knowledge base
- ✅ Mejora de user stories
- ✅ Generación de test cases
- ✅ Validaciones de entrada
- ✅ Manejo de errores

## 🚦 Limitaciones y Consideraciones

### Límites Actuales

- **Documentos por request**: Máximo 50
- **Tamaño de documento**: Máximo 10,000 caracteres
- **Proyectos concurrentes**: Sin límite (limitado por memoria)
- **Timeout de flujos**: 2-3 minutos por operación

### Almacenamiento

- **Vector Store**: En memoria (se reinicia con el servidor)
- **Persistencia**: No implementada (futura mejora)
- **Cache**: No implementado (futura mejora)

### Escalabilidad

- Para producción, considerar:
  - Vector store persistente (Pinecone, Weaviate, etc.)
  - Cache de embeddings (Redis)
  - Queue para operaciones pesadas
  - Rate limiting por proyecto

## 🔄 Próximas Mejoras

### Roadmap

1. **Persistencia**: Vector store persistente
2. **Cache**: Cache de embeddings y resultados
3. **Batch Processing**: Procesamiento de múltiples stories
4. **Analytics**: Métricas avanzadas y dashboards
5. **Templates**: Templates personalizables por proyecto
6. **Integrations**: Jira, GitHub, Notion, etc.

### Extensibilidad

El sistema está diseñado para ser fácilmente extensible:

```typescript
// Añadir nuevo nodo
export class CustomAnalysisNode extends Node {
  constructor() {
    super("custom-analysis", 2);
  }
  
  async exec(data: any): Promise<any> {
    // Lógica personalizada
    return { customResult: "processed" };
  }
}

// Añadir al flujo
const flow = new Flow()
  .addNode(new CustomAnalysisNode())
  .connect("context-analysis", "custom-analysis", "custom");
```

## 📞 Soporte

Para problemas o preguntas:

1. Revisar logs del gateway: `console.log` detallado
2. Verificar configuración de API keys
3. Validar formato de requests según documentación
4. Consultar tipos TypeScript para estructura de datos

## 📄 Licencia

Parte del proyecto AERY - Sistema de automatización con IA.