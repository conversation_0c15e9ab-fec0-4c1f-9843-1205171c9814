// Test Generation Flow - Flujo para generar test cases y edge cases
import { Flow } from "../core/flow.ts";
import { Node } from "../core/node.ts";
import type { RAGEngine } from "../services/rag-engine.ts";
import type { SharedStore, GeneratedTestCase, EdgeCase } from "../types/index.ts";

/**
 * Nodo para análisis de requerimientos de testing
 */
class RequirementAnalysisNode extends Node {
  constructor(private ragEngine: RAGEngine) {
    super("requirement-analysis", 2);
  }

  prep(shared: SharedStore) {
    return {
      story: shared.userStory || shared.story,
      acceptanceCriteria: shared.acceptanceCriteria || [],
      projectId: shared.projectId
    };
  }

  async exec(data: any): Promise<any> {
    this.log(`Analyzing requirements for: "${data.story}"`);
    
    const systemPrompt = `
Analiza los requerimientos de testing para esta user story.

TAREAS:
1. Identificar flujos principales y alternativos
2. Determinar puntos de validación críticos
3. Identificar dependencias externas
4. Evaluar complejidad de testing
5. Determinar tipos de test necesarios

FORMATO DE RESPUESTA (JSON):
{
  "mainFlows": ["flujo1", "flujo2"],
  "validationPoints": ["punto1", "punto2"],
  "externalDependencies": ["dep1", "dep2"],
  "testingComplexity": "low|medium|high",
  "recommendedTestTypes": ["unit", "integration", "e2e", "performance"],
  "riskAreas": ["área1", "área2"]
}`;

    const analysis = await this.ragEngine.retrieveAndGenerate(
      `analyze testing requirements: ${data.story}`,
      systemPrompt,
      5
    );

    return {
      requirementAnalysis: analysis,
      timestamp: new Date().toISOString()
    };
  }

  post(shared: SharedStore, prepRes: any, execRes: any): string {
    shared.requirementAnalysis = execRes.requirementAnalysis;
    return "generate-tests";
  }
}

/**
 * Nodo para generación de test cases
 */
class TestGenerationNode extends Node {
  constructor(private ragEngine: RAGEngine) {
    super("test-generation", 3);
  }

  prep(shared: SharedStore) {
    return {
      story: shared.userStory || shared.story,
      acceptanceCriteria: shared.acceptanceCriteria || [],
      requirementAnalysis: shared.requirementAnalysis,
      projectId: shared.projectId
    };
  }

  async exec(data: any): Promise<{ testCases: GeneratedTestCase[]; edgeCases: EdgeCase[] }> {
    this.log("Generating comprehensive test cases");
    
    try {
      const result = await this.ragEngine.generateTestCases(
        data.story,
        data.acceptanceCriteria
      );

      this.log(`Generated ${result.testCases.length} test cases and ${result.edgeCases.length} edge cases`);
      
      return result;
      
    } catch (error) {
      this.log(`Test generation failed: ${error.message}`, 'error');
      
      // Fallback: generar tests básicos
      return this.generateFallbackTests(data.story);
    }
  }

  post(shared: SharedStore, prepRes: any, execRes: any): string {
    shared.generatedTests = execRes;
    return "validate-tests";
  }

  private generateFallbackTests(story: string): { testCases: GeneratedTestCase[]; edgeCases: EdgeCase[] } {
    return {
      testCases: [{
        title: "Test de funcionalidad principal",
        description: "Verificar que la funcionalidad principal trabaja correctamente",
        type: "happy_path" as const,
        steps: [
          {
            step: 1,
            action: "Navegar a la funcionalidad",
            expectedBehavior: "La página se carga correctamente"
          },
          {
            step: 2,
            action: "Ejecutar la acción principal",
            expectedBehavior: "La acción se completa exitosamente"
          }
        ],
        expectedResult: "Funcionalidad completada sin errores",
        priority: "high" as const
      }],
      edgeCases: [{
        scenario: "Datos de entrada inválidos",
        description: "Comportamiento con datos incorrectos o malformados",
        riskLevel: "medium" as const,
        suggestedHandling: "Validar entrada y mostrar mensajes de error apropiados",
        testApproach: "Probar con diferentes tipos de datos inválidos"
      }]
    };
  }
}

/**
 * Nodo para validación de test cases
 */
class TestValidationNode extends Node {
  constructor(private ragEngine: RAGEngine) {
    super("test-validation", 2);
  }

  prep(shared: SharedStore) {
    return {
      story: shared.userStory || shared.story,
      generatedTests: shared.generatedTests,
      requirementAnalysis: shared.requirementAnalysis
    };
  }

  async exec(data: any): Promise<any> {
    this.log("Validating generated test cases");
    
    const systemPrompt = `
Evalúa la calidad y completitud de estos test cases generados.

CRITERIOS DE EVALUACIÓN:
1. Cobertura de funcionalidades principales
2. Inclusión de casos negativos
3. Testing de casos límite
4. Claridad de los steps
5. Relevancia de los edge cases

TEST CASES:
${JSON.stringify(data.generatedTests.testCases, null, 2)}

EDGE CASES:
${JSON.stringify(data.generatedTests.edgeCases, null, 2)}

FORMATO DE RESPUESTA (JSON):
{
  "coverageScore": 0.85,
  "missingTestTypes": ["tipo1", "tipo2"],
  "suggestions": ["sugerencia1", "sugerencia2"],
  "qualityIssues": ["issue1", "issue2"],
  "strengths": ["fortaleza1", "fortaleza2"]
}`;

    try {
      const validation = await this.ragEngine.retrieveAndGenerate(
        `validate test cases quality: ${data.story}`,
        systemPrompt,
        3
      );

      return {
        testValidation: validation,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      this.log(`Test validation failed: ${error.message}`, 'error');
      
      return {
        testValidation: {
          coverageScore: 0.7,
          missingTestTypes: [],
          suggestions: ["Review tests manually due to validation error"],
          qualityIssues: [`Validation error: ${error.message}`],
          strengths: ["Tests generated successfully"]
        },
        timestamp: new Date().toISOString()
      };
    }
  }

  post(shared: SharedStore, prepRes: any, execRes: any): string {
    shared.testValidation = execRes.testValidation;
    shared.validationTimestamp = execRes.timestamp;
    return "complete";
  }
}

/**
 * Crear flujo de generación de test cases
 */
export function createTestGenerationFlow(ragEngine: RAGEngine): Flow {
  const flow = new Flow({
    name: 'test-generation-flow',
    description: 'Flujo completo para análisis de requerimientos y generación de test cases',
    timeout: 180000, // 3 minutos timeout
    maxRetries: 2
  });

  // Crear nodos
  const requirementAnalysisNode = new RequirementAnalysisNode(ragEngine);
  const testGenerationNode = new TestGenerationNode(ragEngine);
  const testValidationNode = new TestValidationNode(ragEngine);

  // Construir flujo
  return flow
    .addNode(requirementAnalysisNode)
    .addNode(testGenerationNode) 
    .addNode(testValidationNode)
    .connect("requirement-analysis", "test-generation", "generate-tests")
    .connect("test-generation", "test-validation", "validate-tests")
    .connect("requirement-analysis", "test-generation", "default") // fallback
    .connect("test-generation", "test-validation", "default"); // fallback
}

/**
 * Ejecutar flujo de generación de tests
 */
export async function executeTestGeneration(
  ragEngine: RAGEngine,
  userStory: string,
  projectId: string,
  acceptanceCriteria?: string[]
) {
  console.log(`🧪 Starting test generation flow for project: ${projectId}`);
  console.log(`📝 Story: "${userStory}"`);
  
  try {
    const flow = createTestGenerationFlow(ragEngine);
    
    const initialState = {
      userStory,
      story: userStory,
      projectId,
      acceptanceCriteria: acceptanceCriteria || [],
      startTime: new Date().toISOString()
    };

    const result = await flow.run("requirement-analysis", initialState);
    
    if (!result.success) {
      throw new Error(`Flow execution failed: ${result.errors?.join(', ')}`);
    }

    console.log(`✅ Test generation completed in ${result.executionTime}ms`);
    
    const generatedTests = result.finalState.generatedTests;
    const validation = result.finalState.testValidation;
    
    return {
      success: true,
      testCases: generatedTests?.testCases || [],
      edgeCases: generatedTests?.edgeCases || [],
      validation,
      executionStats: {
        executionTime: result.executionTime,
        nodesExecuted: result.nodesExecuted
      },
      metadata: {
        projectId,
        processedAt: new Date().toISOString(),
        flowVersion: '1.0.0'
      }
    };
    
  } catch (error) {
    console.error(`💥 Test generation flow error:`, error);
    
    return {
      success: false,
      error: error.message,
      testCases: [],
      edgeCases: [],
      metadata: {
        projectId,
        processedAt: new Date().toISOString(),
        errorType: 'flow_execution_error'
      }
    };
  }
}

/**
 * Validar entrada para generación de tests
 */
export function validateTestGenerationInput(
  userStory: string,
  projectId: string
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!userStory || userStory.trim().length === 0) {
    errors.push("User story is required for test generation");
  }

  if (!projectId || projectId.trim().length === 0) {
    errors.push("Project ID is required");
  }

  if (userStory && userStory.length > 3000) {
    errors.push("User story is too long for test generation (max 3000 characters)");
  }

  return {
    valid: errors.length === 0,
    errors
  };
}