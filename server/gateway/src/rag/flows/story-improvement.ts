// Story Improvement Flow - Flujo completo para mejorar user stories
import { Flow } from "../core/flow.ts";
import type { RAGEngine } from "../services/rag-engine.ts";

// Importar nodos
import { ContextAnalysisNode } from "../nodes/retrieval/context-analysis.ts";
import { StoryImprovementNode } from "../nodes/generation/story-improvement.ts";
import { ValidationNode } from "../nodes/generation/validation.ts";

/**
 * Crear un flujo completo para mejorar user stories
 * Implementa el patrón Builder para construcción fluida
 */
export function createStoryImprovementFlow(ragEngine: RAGEngine): Flow {
  const flow = new Flow({
    name: 'story-improvement-flow',
    description: 'Flujo completo para análisis, mejora y validación de user stories usando RAG',
    timeout: 120000, // 2 minutos timeout
    maxRetries: 2
  });

  // Crear nodos especializados
  const contextAnalysisNode = new ContextAnalysisNode(ragEngine);
  const storyImprovementNode = new StoryImprovementNode(ragEngine);
  const validationNode = new ValidationNode(ragEngine);

  // Construir el flujo
  return flow
    // Añadir nodos al flujo
    .addNode(contextAnalysisNode)
    .addNode(storyImprovementNode)
    .addNode(validationNode)
    
    // Definir conexiones del flujo
    .connect("context-analysis", "story-improvement", "improve")
    .connect("story-improvement", "validation", "validate")
    
    // Conexiones alternativas para manejo de errores
    .connect("context-analysis", "story-improvement", "default") // fallback si no hay "improve"
    .connect("story-improvement", "validation", "default"); // fallback si no hay "validate"
}

/**
 * Ejecutar el flujo de mejora de story con parámetros específicos
 */
export async function executeStoryImprovement(
  ragEngine: RAGEngine,
  userStory: string,
  projectId: string,
  additionalContext?: string
) {
  console.log(`🚀 Starting story improvement flow for project: ${projectId}`);
  console.log(`📝 Original story: "${userStory}"`);
  
  try {
    // Crear el flujo
    const flow = createStoryImprovementFlow(ragEngine);
    
    // Preparar estado inicial
    const initialState = {
      userStory,
      story: userStory, // alias para compatibilidad
      projectId,
      additionalContext,
      startTime: new Date().toISOString()
    };

    // Ejecutar el flujo
    const result = await flow.run("context-analysis", initialState);
    
    if (!result.success) {
      console.error(`❌ Story improvement flow failed:`, result.errors);
      throw new Error(`Flow execution failed: ${result.errors?.join(', ')}`);
    }

    console.log(`✅ Story improvement flow completed in ${result.executionTime}ms`);
    console.log(`📊 Nodes executed: ${result.nodesExecuted.join(' → ')}`);
    
    // Extraer resultados del estado final
    const finalStory = result.finalState.finalStory || result.finalState.improvedStory;
    const validation = result.finalState.storyValidation;
    const contextAnalysis = result.finalState.contextAnalysis;
    
    return {
      success: true,
      original: userStory,
      improved: finalStory,
      contextAnalysis,
      validation,
      executionStats: {
        executionTime: result.executionTime,
        nodesExecuted: result.nodesExecuted,
        wasRefined: result.finalState.wasRefined || false
      },
      metadata: {
        projectId,
        processedAt: new Date().toISOString(),
        flowVersion: '1.0.0'
      }
    };
    
  } catch (error) {
    console.error(`💥 Story improvement flow error:`, error);
    
    // Retornar estructura de error consistente
    return {
      success: false,
      error: error.message,
      original: userStory,
      metadata: {
        projectId,
        processedAt: new Date().toISOString(),
        errorType: 'flow_execution_error'
      }
    };
  }
}

/**
 * Validar parámetros de entrada para el flujo
 */
export function validateStoryImprovementInput(
  userStory: string,
  projectId: string
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!userStory || userStory.trim().length === 0) {
    errors.push("User story is required and cannot be empty");
  }

  if (!projectId || projectId.trim().length === 0) {
    errors.push("Project ID is required and cannot be empty");
  }

  if (userStory && userStory.length > 2000) {
    errors.push("User story is too long (max 2000 characters)");
  }

  if (userStory && userStory.trim().length < 10) {
    errors.push("User story is too short (min 10 characters)");
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Obtener estadísticas del flujo
 */
export function getStoryImprovementFlowStats(ragEngine: RAGEngine) {
  const flow = createStoryImprovementFlow(ragEngine);
  const stats = flow.getStats();
  
  return {
    ...stats,
    description: "Flujo de mejora de user stories con análisis de contexto, mejora y validación",
    estimatedExecutionTime: "30-60 segundos",
    steps: [
      {
        step: 1,
        name: "Análisis de Contexto",
        description: "Analiza el contexto de la story en el knowledge base del proyecto"
      },
      {
        step: 2,
        name: "Mejora de Story",
        description: "Mejora la story usando patrones y contexto del proyecto"
      },
      {
        step: 3,
        name: "Validación",
        description: "Valida la calidad de la story mejorada y refina si es necesario"
      }
    ]
  };
}