# Configuración de CORS en AERY Gateway

El gateway de AERY incluye soporte para habilitar/deshabilitar CORS de manera condicional mediante variables de entorno.

## ¿Cuándo deshabilitar CORS?

CORS (Cross-Origin Resource Sharing) es necesario cuando el API es consumida desde navegadores web que ejecutan JavaScript desde un dominio diferente. Sin embargo, cuando el gateway es consumido únicamente por otras APIs o servicios backend (uso interno), CORS no es necesario y puede ser deshabilitado para:

- Mejorar el rendimiento (menos headers HTTP)
- Simplificar la configuración
- Evitar problemas de configuración CORS en entornos internos

## Configuración

### Deshabilitar CORS (para uso interno)

Para deshabilitar CORS completamente, agrega la siguiente variable de entorno:

```bash
CORS_ENABLED=false
```

Esto deshabilitará:
- Headers CORS en todas las respuestas
- Man<PERSON>o de requests OPTIONS (preflight)
- Configuración CORS en Fresh framework

### Habilitar CORS (para uso con UI/navegadores)

Para habilitar CORS (comportamiento por defecto), puedes:

1. **Omitir la variable** (CORS habilitado por defecto)
2. **Establecer explícitamente:**
   ```bash
   CORS_ENABLED=true
   ```

### Configuración adicional de CORS

Cuando CORS está habilitado, puedes personalizar su comportamiento con estas variables:

```bash
# Orígenes permitidos (separados por coma)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Métodos HTTP permitidos
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH

# Headers permitidos
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-API-Key

# Permitir credenciales
CORS_ALLOW_CREDENTIALS=true

# Tiempo de cache para preflight (segundos)
CORS_MAX_AGE=86400
```

## Ejemplos de uso

### Desarrollo con UI (CORS habilitado)

```bash
# .env
CORS_ENABLED=true
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
```

### Producción interna (CORS deshabilitado)

```bash
# .env
CORS_ENABLED=false
```

### Producción con UI (CORS habilitado con dominios específicos)

```bash
# .env
CORS_ENABLED=true
CORS_ALLOWED_ORIGINS=https://app.midominio.com,https://admin.midominio.com
```

## Verificación

Al iniciar el gateway, verás en los logs el estado de CORS:

```
🔧 AERY Configuration:
   Environment: development
   Server: 0.0.0.0:8000
   CORS: ✅ Habilitado
   # o
   CORS: 🚫 Deshabilitado
```

## Implementación técnica

La funcionalidad se implementa en dos niveles:

1. **Fresh Framework**: Configuración CORS a nivel de servidor en `fresh.config.ts`
2. **Middleware personalizado**: `CorsMiddleware.wrapConditional()` en las rutas individuales

Ambos niveles verifican la variable `CORS_ENABLED` para determinar si aplicar CORS o no.