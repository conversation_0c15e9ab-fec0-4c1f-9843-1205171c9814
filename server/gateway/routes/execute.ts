// AERY Execute Task API
import { HandlerContext } from "$fresh/server.ts";
import { AuthService } from "../src/lib/auth.ts";
import { QueueService } from "../src/lib/queue.ts";
import { db } from "../src/lib/database.ts";
import { Utils, Validators } from "../src/lib/utils.ts";
import { CorsMiddleware } from "../src/middleware/cors.ts";
import type { QueueTask, TaskExecution } from "../../../../shared/types/index.ts";

/**
 * Execute task endpoint
 */
const executeHandler = async (
  req: Request,
  _ctx: HandlerContext,
): Promise<Response> => {
  if (req.method !== "POST") {
    return Utils.createErrorResponse(
      "Método no permitido",
      "METHOD_NOT_ALLOWED",
      405,
    );
  }

  try {
    // Autenticación
    const user = await authenticateRequest(req);
    if (!user) {
      return Utils.createErrorResponse(
        "No autorizado",
        "UNAUTHORIZED",
        401,
      );
    }

    const body = await req.json();

    // Validar entrada
    const validation = Utils.validateObject(body, ["instruction"]);
    if (!validation.valid) {
      return Utils.createErrorResponse(
        "Campos requeridos faltantes",
        "MISSING_FIELDS",
        400,
        { missing: validation.missing },
      );
    }

    const {
      instruction,
      url,
      prescriptId,
      priority = false,
      timeout = 300000, // 5 minutos por defecto
      screenshots = true,
      headless = true,
      viewport = { width: 1920, height: 1080 },
      userAgent,
      metadata = {},
    } = body;

    // Validar permisos
    if (
      priority && !AuthService.hasPermission(user, "execute_advanced_tasks")
    ) {
      return Utils.createErrorResponse(
        "Sin permisos para tareas prioritarias",
        "INSUFFICIENT_PERMISSIONS",
        403,
      );
    }

    // Validar URL si se proporciona
    if (url && !Utils.isValidUrl(url)) {
      return Utils.createErrorResponse(
        "URL inválida",
        "INVALID_URL",
        400,
      );
    }

    // Validar pre-script si se especifica
    let prescript = null;
    if (prescriptId) {
      if (!AuthService.hasPermission(user, "access_prescripts")) {
        return Utils.createErrorResponse(
          "Sin permisos para usar pre-scripts",
          "INSUFFICIENT_PERMISSIONS",
          403,
        );
      }

      prescript = await db.getPrescriptById(prescriptId);
      if (!prescript) {
        return Utils.createErrorResponse(
          "Pre-script no encontrado",
          "PRESCRIPT_NOT_FOUND",
          404,
        );
      }

      if (!prescript.isActive) {
        return Utils.createErrorResponse(
          "Pre-script no está activo",
          "PRESCRIPT_INACTIVE",
          400,
        );
      }
    }

    // Crear ejecución en base de datos
    const sanitizedInstruction = Utils.sanitizeInput(instruction);
    const instructionHash = await Utils.generateHash(sanitizedInstruction);

    // Generar un taskId temporal que será actualizado después
    const tempTaskId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const execution = await db.createTaskExecution(
      user.id,
      tempTaskId,
      sanitizedInstruction,
      instructionHash
    );

    // Crear tarea para la cola
    const queueTask: Omit<QueueTask, "id" | "createdAt" | "status"> = {
      userId: user.id,
      executionId: execution.id,
      instruction: sanitizedInstruction,
      url,
      prescriptId,
      priority,
      timeout,
      maxRetries: 3,
      config: {
        screenshots,
        headless,
        viewport,
        userAgent,
      },
      metadata,
    };

    // Encolar tarea
    const taskId = await QueueService.enqueueTask(queueTask, priority);

    // Actualizar ejecución con taskId
    await db.updateTaskExecution(execution.id, { taskId });

    // Respuesta exitosa
    return Utils.createSuccessResponse(
      {
        executionId: execution.id,
        taskId,
        status: "pending",
        estimatedTime: getEstimatedTime(instruction, prescript),
        queuePosition: await getQueuePosition(priority),
        prescript: prescript
          ? {
            id: prescript.id,
            name: prescript.name,
            description: prescript.description,
          }
          : null,
      },
      "Tarea encolada exitosamente",
      202,
    );
  } catch (error) {
    console.error("❌ Error ejecutando tarea:", error);

    return Utils.createErrorResponse(
      "Error interno del servidor",
      "INTERNAL_SERVER_ERROR",
      500,
    );
  }
};

/**
 * Autenticar solicitud
 */
async function authenticateRequest(req: Request) {
  // Intentar autenticación por API Key
  const apiKey = req.headers.get("X-API-Key") ||
    req.headers.get("Authorization")?.replace("Bearer ", "");

  if (apiKey) {
    const user = await AuthService.authenticateApiKey(apiKey);
    if (user) return user;
  }

  // Intentar autenticación por JWT
  const authHeader = req.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    const token = authHeader.substring(7);
    const user = await AuthService.authenticateJwt(token);
    if (user) return user;
  }

  return null;
}

/**
 * Estimar tiempo de ejecución
 */
function getEstimatedTime(instruction: string, prescript: any): number {
  let baseTime = 30000; // 30 segundos base

  // Ajustar según complejidad de la instrucción
  const words = instruction.split(" ").length;
  baseTime += words * 1000; // 1 segundo por palabra

  // Agregar tiempo del pre-script
  if (prescript && prescript.estimatedTime) {
    baseTime += prescript.estimatedTime;
  }

  return Math.min(baseTime, 300000); // Máximo 5 minutos
}

/**
 * Obtener posición en la cola
 */
async function getQueuePosition(priority: boolean): Promise<number> {
  try {
    const stats = await QueueService.getQueueStats();
    return priority ? stats.priority + 1 : stats.pending + stats.priority + 1;
  } catch (error) {
    return 1;
  }
}

export const handler = CorsMiddleware.wrapConditional({
  POST: executeHandler
});