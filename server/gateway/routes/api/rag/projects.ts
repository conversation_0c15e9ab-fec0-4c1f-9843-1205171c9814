// RAG Projects API Route - Gestión de proyectos RAG
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../src/middleware/cors.ts";
import { ProjectManager } from "../../../src/rag/services/project-manager.ts";
import type { 
  CreateProjectRequest, 
  CreateProjectResponse,
  ProjectConfig 
} from "../../../src/rag/types/index.ts";

// Instancia global del ProjectManager
const projectManager = new ProjectManager();

const projectsHandler: Handlers = {
  /**
   * GET /api/rag/projects - Listar todos los proyectos
   */
  async GET(req, ctx) {
    try {
      console.log("📋 GET /api/rag/projects - Listing all projects");
      
      const projects = projectManager.listProjects();
      const globalStats = projectManager.getGlobalStats();
      
      return new Response(
        JSON.stringify({
          success: true,
          projects,
          stats: globalStats,
          total: projects.length
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error listing projects:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to list projects",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * POST /api/rag/projects - Crear un nuevo proyecto RAG
   */
  async POST(req, ctx) {
    try {
      console.log("🏗️ POST /api/rag/projects - Creating new project");
      
      const body = await req.json() as CreateProjectRequest;
      
      // Validar entrada
      const validation = validateCreateProjectRequest(body);
      if (!validation.valid) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid request",
            details: validation.errors
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      // Generar ID si no se proporciona
      const projectId = body.name.toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');

      // Verificar si el proyecto ya existe
      if (projectManager.projectExists(projectId)) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Project already exists",
            message: `Project with ID '${projectId}' already exists`
          }),
          {
            status: 409,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      // Configurar el proyecto
      const projectConfig: ProjectConfig = {
        id: projectId,
        name: body.name,
        description: body.description,
        vectorStoreConfig: {
          apiKey: "", // Se usa la del config global
          model: "embed-english-v3.0" // Modelo compatible con Cohere
        },
        llmConfig: {
          model: "openai/gpt-4o-mini", // Modelo por defecto más económico
          apiKey: "" // Se usa la del config global
        }
      };

      // Crear el proyecto
      await projectManager.createProject(projectConfig);

      // Añadir documentos iniciales si se proporcionan
      if (body.initialDocuments && body.initialDocuments.length > 0) {
        await projectManager.addKnowledge(projectId, body.initialDocuments);
      }

      const response: CreateProjectResponse = {
        projectId,
        message: `Project '${body.name}' created successfully`
      };

      console.log(`✅ Project created: ${projectId}`);

      return new Response(
        JSON.stringify({
          success: true,
          ...response,
          project: projectManager.getProjectConfig(projectId)
        }),
        {
          status: 201,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error creating project:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to create project",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

/**
 * Validar request de creación de proyecto
 */
function validateCreateProjectRequest(body: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!body) {
    errors.push("Request body is required");
    return { valid: false, errors };
  }

  if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
    errors.push("Project name is required and must be a non-empty string");
  }

  if (body.name && body.name.length > 100) {
    errors.push("Project name is too long (max 100 characters)");
  }

  if (body.description && typeof body.description !== 'string') {
    errors.push("Project description must be a string");
  }

  if (body.description && body.description.length > 500) {
    errors.push("Project description is too long (max 500 characters)");
  }

  if (body.initialDocuments && !Array.isArray(body.initialDocuments)) {
    errors.push("Initial documents must be an array");
  }

  if (body.initialDocuments) {
    for (let i = 0; i < body.initialDocuments.length; i++) {
      const doc = body.initialDocuments[i];
      
      if (!doc.content || typeof doc.content !== 'string') {
        errors.push(`Initial document ${i + 1}: content is required and must be a string`);
      }
      
      if (!doc.type || !['story', 'requirement', 'test', 'documentation'].includes(doc.type)) {
        errors.push(`Initial document ${i + 1}: type must be one of: story, requirement, test, documentation`);
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Exportar handler con CORS
export const handler = CorsMiddleware.wrapConditional(projectsHandler);

// Exportar el projectManager para uso en otras rutas
export { projectManager };