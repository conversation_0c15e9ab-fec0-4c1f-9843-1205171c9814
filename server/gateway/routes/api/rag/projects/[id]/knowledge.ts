// RAG Knowledge API Route - Gestión del knowledge base por proyecto
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../../../src/middleware/cors.ts";
import { projectManager } from "../../projects.ts";
import type { 
  AddKnowledgeRequest, 
  AddKnowledgeResponse 
} from "../../../../../src/rag/types/index.ts";

const knowledgeHandler: Handlers = {
  /**
   * GET /api/rag/projects/:id/knowledge - Obtener estadísticas del knowledge base
   */
  async GET(req, ctx) {
    const projectId = ctx.params.id;
    
    try {
      console.log(`📊 GET /api/rag/projects/${projectId}/knowledge - Getting knowledge stats`);
      
      // Verificar que el proyecto existe
      if (!projectManager.projectExists(projectId)) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Project not found",
            message: `Project '${projectId}' does not exist`
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      // Obtener estadísticas del proyecto
      const stats = await projectManager.getProjectStats(projectId);
      
      // Obtener documentos del proyecto
      const documents = await projectManager.getProjectKnowledge(projectId);
      
      return new Response(
        JSON.stringify({
          success: true,
          projectId,
          knowledge: {
            totalDocuments: stats.vectorStore.totalDocuments,
            lastUpdated: stats.vectorStore.lastUpdated,
            documents: documents,
            projectInfo: {
              name: stats.project.name,
              description: stats.project.description,
              createdAt: stats.project.createdAt,
              updatedAt: stats.project.updatedAt
            }
          },
          ragEngine: stats.ragEngine
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error(`❌ Error getting knowledge stats for project ${projectId}:`, error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to get knowledge stats",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * POST /api/rag/projects/:id/knowledge - Añadir documentos al knowledge base
   */
  async POST(req, ctx) {
    const projectId = ctx.params.id;
    
    try {
      console.log(`📚 POST /api/rag/projects/${projectId}/knowledge - Adding knowledge`);
      
      // Verificar que el proyecto existe
      if (!projectManager.projectExists(projectId)) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Project not found",
            message: `Project '${projectId}' does not exist`
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      const body = await req.json() as AddKnowledgeRequest;
      
      // Validar entrada
      if (!body.documents || !Array.isArray(body.documents) || body.documents.length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid request",
            message: "Documents array is required and cannot be empty"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      // Añadir documentos al knowledge base
      await projectManager.addKnowledge(projectId, body.documents);
      
      const response: AddKnowledgeResponse = {
        documentsAdded: body.documents.length,
        message: `Successfully added ${body.documents.length} documents to knowledge base`
      };

      console.log(`✅ Added ${body.documents.length} documents to project ${projectId}`);

      // Obtener estadísticas actualizadas
      const updatedStats = await projectManager.getProjectStats(projectId);

      return new Response(
        JSON.stringify({
          success: true,
          ...response,
          updatedStats: {
            totalDocuments: updatedStats.vectorStore.totalDocuments,
            lastUpdated: updatedStats.vectorStore.lastUpdated
          }
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error(`❌ Error adding knowledge to project ${projectId}:`, error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to add knowledge",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

// Export handler with CORS
export const handler = CorsMiddleware.wrapConditional(knowledgeHandler);