// Dynamic RAG Stories Route - Handle improve/tests actions dynamically
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../../../src/middleware/cors.ts";
import { projectManager } from "../../projects.ts";
import { 
  executeStoryImprovement, 
  validateStoryImprovementInput 
} from "../../../../../src/rag/flows/story-improvement.ts";
import { 
  executeTestGeneration, 
  validateTestGenerationInput 
} from "../../../../../src/rag/flows/test-generation.ts";
import type { 
  ImproveStoryRequest, 
  ImproveStoryResponse,
  GenerateTestsRequest,
  GenerateTestsResponse 
} from "../../../../../src/rag/types/index.ts";

const dynamicStoriesHandler: Handlers = {
  /**
   * POST /api/rag/projects/:id/stories/:action
   * Dynamic routing for improve/tests actions
   */
  async POST(req, ctx) {
    const projectId = ctx.params.id;
    const action = ctx.params.action;

    console.log(`🎯 POST /api/rag/projects/${projectId}/stories/${action}`);

    // Verificar que el proyecto existe
    if (!projectManager.projectExists(projectId)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Project not found",
          message: `Project '${projectId}' does not exist`
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Rutear según la acción
    switch (action) {
      case 'improve':
        return await handleStoryImprovement(req, projectId);
      
      case 'tests':
        return await handleTestGeneration(req, projectId);
      
      default:
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid action",
            message: `Action '${action}' not supported. Available actions: improve, tests`,
            availableActions: ['improve', 'tests']
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
    }
  }
};

/**
 * Manejar mejora de user stories
 */
async function handleStoryImprovement(req: Request, projectId: string) {
  try {
    console.log(`📝 Improving story for project ${projectId}`);
    
    const body = await req.json() as ImproveStoryRequest;
    
    // Validar entrada
    const validation = validateStoryImprovementInput(body.story, projectId);
    if (!validation.valid) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Invalid request",
          details: validation.errors
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Obtener el motor RAG del proyecto
    const ragEngine = projectManager.getRAGEngine(projectId);
    
    // Ejecutar el flujo de mejora
    const result = await executeStoryImprovement(
      ragEngine,
      body.story,
      projectId,
      body.context
    );

    if (!result.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Story improvement failed",
          message: result.error,
          metadata: result.metadata
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    const response: ImproveStoryResponse = {
      improvedStory: result.improved,
      suggestions: result.validation?.suggestions || [],
      confidence: result.validation?.qualityScore || 0.8
    };

    console.log(`✅ Story improved for project ${projectId}`);

    return new Response(
      JSON.stringify({
        success: true,
        ...response,
        executionStats: result.executionStats,
        metadata: result.metadata
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
    
  } catch (error) {
    console.error(`❌ Error improving story for project ${projectId}:`, error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error.message
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}

/**
 * Manejar generación de test cases
 */
async function handleTestGeneration(req: Request, projectId: string) {
  try {
    console.log(`🧪 Generating tests for project ${projectId}`);
    
    const body = await req.json() as GenerateTestsRequest;
    
    // Validar entrada
    const validation = validateTestGenerationInput(body.story, projectId);
    if (!validation.valid) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Invalid request",
          details: validation.errors
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Obtener el motor RAG del proyecto
    const ragEngine = projectManager.getRAGEngine(projectId);
    
    // Ejecutar el flujo de generación de tests
    const result = await executeTestGeneration(
      ragEngine,
      body.story,
      projectId,
      body.acceptanceCriteria
    );

    if (!result.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Test generation failed",
          message: result.error,
          metadata: result.metadata
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Calcular métricas de cobertura
    const coverage = calculateTestCoverage(result.testCases, result.edgeCases);

    const response: GenerateTestsResponse = {
      testCases: result.testCases,
      edgeCases: result.edgeCases,
      coverage
    };

    console.log(`✅ Tests generated for project ${projectId}: ${result.testCases.length} tests, ${result.edgeCases.length} edge cases`);

    return new Response(
      JSON.stringify({
        success: true,
        ...response,
        validation: result.validation,
        executionStats: result.executionStats,
        metadata: result.metadata
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
    
  } catch (error) {
    console.error(`❌ Error generating tests for project ${projectId}:`, error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        message: error.message
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
}

/**
 * Calcular métricas de cobertura de tests
 */
function calculateTestCoverage(testCases: any[], edgeCases: any[]): {
  functionalCoverage: number;
  boundaryTestsIncluded: boolean;
  negativeTestsIncluded: boolean;
} {
  const hasHappyPath = testCases.some(test => test.type === 'happy_path');
  const hasNegativeTests = testCases.some(test => test.type === 'negative');
  const hasBoundaryTests = testCases.some(test => test.type === 'boundary');
  const hasIntegrationTests = testCases.some(test => test.type === 'integration');
  const hasEdgeCases = edgeCases.length > 0;

  // Calcular cobertura funcional básica
  let coverageScore = 0;
  if (hasHappyPath) coverageScore += 0.4; // 40% por happy path
  if (hasNegativeTests) coverageScore += 0.2; // 20% por negative tests
  if (hasBoundaryTests) coverageScore += 0.2; // 20% por boundary tests
  if (hasIntegrationTests) coverageScore += 0.1; // 10% por integration tests
  if (hasEdgeCases) coverageScore += 0.1; // 10% por edge cases

  return {
    functionalCoverage: Math.min(coverageScore, 1.0),
    boundaryTestsIncluded: hasBoundaryTests,
    negativeTestsIncluded: hasNegativeTests
  };
}

// Export handler with CORS
export const handler = CorsMiddleware.wrapConditional(dynamicStoriesHandler);