// Model Agent Configuration API Route
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../src/middleware/cors.ts";
import { modelManager } from "../../../src/models/model-manager.ts";

const agentsHandler: Handlers = {
  /**
   * GET /api/models/agents - Get agent model configurations
   */
  async GET(req, ctx) {
    try {
      console.log("🤖 GET /api/models/agents - Getting agent model selections");
      
      const selections = modelManager.getAgentModelSelections();

      return new Response(
        JSON.stringify({
          success: true,
          selections,
          total: selections.length
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error getting agent selections:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to get agent selections",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * PUT /api/models/agents - Update agent model configuration
   */
  async PUT(req, ctx) {
    try {
      console.log("🔧 PUT /api/models/agents - Updating agent model selections");
      
      const body = await req.json();
      
      // Validate request
      if (!body.agent_type || !body.model_id) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid request",
            message: "agent_type and model_id are required"
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      const success = modelManager.setAgentModel(
        body.agent_type,
        body.model_id,
        body.fallback_model_id
      );

      if (!success) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Failed to update agent model selection"
          }),
          {
            status: 500,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      console.log(`✅ Agent model updated: ${body.agent_type} -> ${body.model_id}`);

      return new Response(
        JSON.stringify({
          success: true,
          message: `Agent '${body.agent_type}' model updated to '${body.model_id}'`
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error updating agent selection:", error);
      
      const status = error.message.includes("not found") ? 404 : 500;
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to update agent selection",
          message: error.message
        }),
        {
          status,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

export const handler = CorsMiddleware.wrapConditional(agentsHandler);