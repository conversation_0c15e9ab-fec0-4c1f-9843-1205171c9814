// Model Statistics API Route
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../src/middleware/cors.ts";
import { modelManager } from "../../../src/models/model-manager.ts";
import type { ModelStatsResponse } from "../../../src/models/types.ts";

const statsHandler: Handlers = {
  /**
   * GET /api/models/stats - Get usage statistics for all models
   * Query params: model_id (optional)
   */
  async GET(req, ctx) {
    try {
      console.log("📊 GET /api/models/stats - Getting model statistics");
      
      const url = new URL(req.url);
      const modelId = url.searchParams.get("model_id") || undefined;

      const stats = modelManager.getUsageStats(modelId);
      const totalCost = modelManager.getTotalCost();
      const totalRequests = modelManager.getTotalRequests();

      const response: ModelStatsResponse = {
        success: true,
        stats,
        total_cost: totalCost,
        total_requests: totalRequests
      };

      return new Response(
        JSON.stringify(response),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error getting model statistics:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to get model statistics",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

export const handler = CorsMiddleware.wrapConditional(statsHandler);