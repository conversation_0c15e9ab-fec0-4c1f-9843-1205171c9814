// Models API Route - List and manage AI models
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../src/middleware/cors.ts";
import { modelManager } from "../../../src/models/model-manager.ts";
import type { 
  ModelsListResponse,
  CreateModelRequest,
  ModelResponse 
} from "../../../src/models/types.ts";

const modelsHandler: Handlers = {
  /**
   * GET /api/models - List all models
   * Query params: provider, category, enabled
   */
  async GET(req, ctx) {
    try {
      console.log("📋 GET /api/models - Listing all models");
      
      const url = new URL(req.url);
      const provider = url.searchParams.get("provider") || undefined;
      const category = url.searchParams.get("category") || undefined;
      const enabledParam = url.searchParams.get("enabled");
      const enabled = enabledParam ? enabledParam === "true" : undefined;

      const models = modelManager.listModels(provider, category, enabled);
      const providers = modelManager.getAvailableProviders();
      const categories = modelManager.getCategories();

      const response: ModelsListResponse = {
        success: true,
        models,
        total: models.length,
        providers,
        categories
      };

      return new Response(
        JSON.stringify(response),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error listing models:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to list models",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * POST /api/models - Create a new model
   */
  async POST(req, ctx) {
    try {
      console.log("🔧 POST /api/models - Creating new model");
      
      const body = await req.json() as CreateModelRequest;
      
      // Validate request
      const validation = validateCreateModelRequest(body);
      if (!validation.valid) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid request",
            details: validation.errors
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      const model = modelManager.createModel(body);

      const response: ModelResponse = {
        success: true,
        model
      };

      console.log(`✅ Model created: ${model.id}`);

      return new Response(
        JSON.stringify(response),
        {
          status: 201,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error creating model:", error);
      
      const status = error.message.includes("already exists") ? 409 : 500;
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to create model",
          message: error.message
        }),
        {
          status,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

/**
 * Validate create model request
 */
function validateCreateModelRequest(body: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!body) {
    errors.push("Request body is required");
    return { valid: false, errors };
  }

  if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
    errors.push("Model name is required and must be a non-empty string");
  }

  if (!body.provider || !['openai', 'anthropic', 'openrouter', 'cohere'].includes(body.provider)) {
    errors.push("Provider must be one of: openai, anthropic, openrouter, cohere");
  }

  if (!body.category || !['chat', 'embedding', 'completion'].includes(body.category)) {
    errors.push("Category must be one of: chat, embedding, completion");
  }

  if (!body.pricing || typeof body.pricing !== 'object') {
    errors.push("Pricing configuration is required");
  } else {
    if (typeof body.pricing.input !== 'number' || body.pricing.input < 0) {
      errors.push("Pricing input cost must be a non-negative number");
    }
    if (typeof body.pricing.output !== 'number' || body.pricing.output < 0) {
      errors.push("Pricing output cost must be a non-negative number");
    }
  }

  if (!body.context_length || typeof body.context_length !== 'number' || body.context_length <= 0) {
    errors.push("Context length must be a positive number");
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export const handler = CorsMiddleware.wrapConditional(modelsHandler);