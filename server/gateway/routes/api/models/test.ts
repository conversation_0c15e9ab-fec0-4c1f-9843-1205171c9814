// Model Testing API Route
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../src/middleware/cors.ts";
import { modelManager } from "../../../src/models/model-manager.ts";
import type { 
  ModelTestRequest,
  ModelTestResponse 
} from "../../../src/models/types.ts";

const testHandler: Handlers = {
  /**
   * POST /api/models/test - Test a model with a sample prompt
   */
  async POST(req, ctx) {
    try {
      console.log("🧪 POST /api/models/test - Testing model");
      
      const body = await req.json() as ModelTestRequest;
      
      // Validate request
      const validation = validateTestModelRequest(body);
      if (!validation.valid) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid request",
            details: validation.errors
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      const result = await modelManager.testModel(body);

      console.log(`${result.success ? '✅' : '❌'} Model test: ${body.model_id} - ${result.success ? 'Success' : result.error}`);

      return new Response(
        JSON.stringify(result),
        {
          status: result.success ? 200 : 400,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error testing model:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to test model",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

/**
 * Validate test model request
 */
function validateTestModelRequest(body: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!body) {
    errors.push("Request body is required");
    return { valid: false, errors };
  }

  if (!body.model_id || typeof body.model_id !== 'string') {
    errors.push("model_id is required and must be a string");
  }

  if (!body.prompt || typeof body.prompt !== 'string' || body.prompt.trim().length === 0) {
    errors.push("prompt is required and must be a non-empty string");
  }

  if (body.prompt && body.prompt.length > 4000) {
    errors.push("prompt is too long (max 4000 characters)");
  }

  if (body.params) {
    if (typeof body.params !== 'object') {
      errors.push("params must be an object");
    } else {
      if (body.params.temperature !== undefined && (typeof body.params.temperature !== 'number' || body.params.temperature < 0 || body.params.temperature > 2)) {
        errors.push("temperature must be a number between 0 and 2");
      }
      
      if (body.params.max_tokens !== undefined && (typeof body.params.max_tokens !== 'number' || body.params.max_tokens <= 0)) {
        errors.push("max_tokens must be a positive number");
      }
      
      if (body.params.top_p !== undefined && (typeof body.params.top_p !== 'number' || body.params.top_p < 0 || body.params.top_p > 1)) {
        errors.push("top_p must be a number between 0 and 1");
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export const handler = CorsMiddleware.wrapConditional(testHandler);