// Individual Model API Route - Get, update, delete specific model
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../src/middleware/cors.ts";
import { modelManager } from "../../../src/models/model-manager.ts";
import type { 
  ModelResponse,
  UpdateModelRequest 
} from "../../../src/models/types.ts";

const modelHandler: Handlers = {
  /**
   * GET /api/models/:id - Get specific model
   */
  async GET(req, ctx) {
    try {
      const modelId = decodeURIComponent(ctx.params.id);
      console.log(`📋 GET /api/models/${modelId} - Getting model details`);
      
      const model = modelManager.getModel(modelId);
      
      if (!model) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Model not found",
            message: `Model with ID '${modelId}' does not exist`
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      const response: ModelResponse = {
        success: true,
        model
      };

      return new Response(
        JSON.stringify(response),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error getting model:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to get model",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * PUT /api/models/:id - Update specific model
   */
  async PUT(req, ctx) {
    try {
      const modelId = decodeURIComponent(ctx.params.id);
      console.log(`🔧 PUT /api/models/${modelId} - Updating model`);
      
      const body = await req.json() as UpdateModelRequest;
      
      // Validate request
      const validation = validateUpdateModelRequest(body);
      if (!validation.valid) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid request",
            details: validation.errors
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      const model = modelManager.updateModel(modelId, body);

      const response: ModelResponse = {
        success: true,
        model
      };

      console.log(`✅ Model updated: ${modelId}`);

      return new Response(
        JSON.stringify(response),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error updating model:", error);
      
      const status = error.message.includes("not found") ? 404 : 500;
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to update model",
          message: error.message
        }),
        {
          status,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * DELETE /api/models/:id - Delete specific model
   */
  async DELETE(req, ctx) {
    try {
      const modelId = decodeURIComponent(ctx.params.id);
      console.log(`🗑️ DELETE /api/models/${modelId} - Deleting model`);
      
      const deleted = modelManager.deleteModel(modelId);
      
      if (!deleted) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Model not found",
            message: `Model with ID '${modelId}' does not exist`
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      console.log(`✅ Model deleted: ${modelId}`);

      return new Response(
        JSON.stringify({
          success: true,
          message: `Model '${modelId}' deleted successfully`
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error("❌ Error deleting model:", error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to delete model",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

/**
 * Validate update model request
 */
function validateUpdateModelRequest(body: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!body || Object.keys(body).length === 0) {
    errors.push("Request body with at least one field is required");
    return { valid: false, errors };
  }

  if (body.name !== undefined && (typeof body.name !== 'string' || body.name.trim().length === 0)) {
    errors.push("Model name must be a non-empty string");
  }

  if (body.pricing !== undefined) {
    if (typeof body.pricing !== 'object') {
      errors.push("Pricing must be an object");
    } else {
      if (body.pricing.input !== undefined && (typeof body.pricing.input !== 'number' || body.pricing.input < 0)) {
        errors.push("Pricing input cost must be a non-negative number");
      }
      if (body.pricing.output !== undefined && (typeof body.pricing.output !== 'number' || body.pricing.output < 0)) {
        errors.push("Pricing output cost must be a non-negative number");
      }
    }
  }

  if (body.context_length !== undefined && (typeof body.context_length !== 'number' || body.context_length <= 0)) {
    errors.push("Context length must be a positive number");
  }

  if (body.enabled !== undefined && typeof body.enabled !== 'boolean') {
    errors.push("Enabled must be a boolean");
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

export const handler = CorsMiddleware.wrapConditional(modelHandler);