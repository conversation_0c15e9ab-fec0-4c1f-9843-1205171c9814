// API Routes Index - Centralized API routing
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../src/middleware/cors.ts";

const apiHandler: Handlers = {
  async GET(req, ctx) {
    return new Response(
      JSON.stringify({
        message: "AERY API v1.0",
        timestamp: new Date().toISOString(),
        endpoints: {
          "GET /api/rag/projects": "List RAG projects",
          "POST /api/rag/projects": "Create RAG project",
          "GET /api/health": "Health check"
        }
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};

export const handler = CorsMiddleware.wrapConditional(apiHandler);