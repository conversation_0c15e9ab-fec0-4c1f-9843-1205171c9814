FROM denoland/deno:1.40.2

WORKDIR /app

# Copy dependency files first for better caching
COPY ./server/gateway/deno.json .
COPY ./server/gateway/fresh.config.ts .
COPY ./server/gateway/fresh.gen.ts .

# Copy shared types (needed for compilation)
COPY ./shared ./shared

# Copy source code from gateway
COPY ./server/gateway ./

# Cache dependencies
RUN deno cache main.ts

# Create artifacts directory
RUN mkdir -p /app/artifacts/screenshots

EXPOSE 8000

CMD ["deno", "run", "-A", "main.ts"]