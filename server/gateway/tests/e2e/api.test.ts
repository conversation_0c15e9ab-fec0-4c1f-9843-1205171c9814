/**
 * End-to-End API Tests for AERY Browser Automation API
 * 
 * These tests verify the complete API functionality including:
 * - Health checks
 * - Authentication (register/login)
 * - Task execution
 * - Pre-scripts management
 * - Metrics collection
 */

import { assertEquals, assertExists } from "https://deno.land/std@0.216.0/assert/mod.ts";

const API_BASE = "http://localhost:8000";

// Test configuration
const TEST_USER = {
  email: `test-${Date.now()}@example.com`,
  password: "test-password-123",
  plan: "basic"
};

let authToken = "";
let userId = "";

/**
 * Utility function to make HTTP requests
 */
async function makeRequest(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const url = `${API_BASE}${endpoint}`;
  const defaultHeaders = {
    "Content-Type": "application/json",
    ...(authToken && { Authorization: `Bearer ${authToken}` })
  };

  return await fetch(url, {
    headers: { ...defaultHeaders, ...(options.headers || {}) },
    ...options
  });
}

/**
 * Wait for a service to be ready
 */
async function waitForService(maxRetries = 30, delayMs = 1000): Promise<boolean> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(`${API_BASE}/health`);
      if (response.ok) {
        return true;
      }
    } catch (_error) {
      // Service not ready yet
    }
    await new Promise(resolve => setTimeout(resolve, delayMs));
  }
  return false;
}

// ================================
// Health Check Tests
// ================================

Deno.test("Health Check - Service should be healthy", async () => {
  console.log("🏥 Testing health check...");
  
  // Wait for service to be ready
  const isReady = await waitForService();
  assertEquals(isReady, true, "Service should be ready within timeout");

  const response = await makeRequest("/health");
  assertEquals(response.status, 200);
  
  const healthData = await response.json();
  assertExists(healthData.status);
  assertEquals(healthData.status, "healthy");
  
  console.log("✅ Health check passed");
});

// ================================
// Authentication Tests
// ================================

Deno.test("Auth - User Registration", async () => {
  console.log("👤 Testing user registration...");
  
  const response = await makeRequest("/auth/register", {
    method: "POST",
    body: JSON.stringify(TEST_USER)
  });

  assertEquals(response.status, 201, "Registration should succeed");
  
  const data = await response.json();
  assertExists(data.user);
  assertExists(data.user.id);
  assertExists(data.token);
  
  // Store for subsequent tests
  authToken = data.token;
  userId = data.user.id;
  
  assertEquals(data.user.email, TEST_USER.email);
  assertEquals(data.user.plan, TEST_USER.plan);
  
  console.log("✅ User registration passed");
});

Deno.test("Auth - User Login", async () => {
  console.log("🔐 Testing user login...");
  
  const response = await makeRequest("/auth/login", {
    method: "POST",
    body: JSON.stringify({
      email: TEST_USER.email,
      password: TEST_USER.password
    })
  });

  assertEquals(response.status, 200, "Login should succeed");
  
  const data = await response.json();
  assertExists(data.user);
  assertExists(data.token);
  assertEquals(data.user.email, TEST_USER.email);
  
  // Update auth token
  authToken = data.token;
  
  console.log("✅ User login passed");
});

Deno.test("Auth - Invalid credentials should fail", async () => {
  console.log("❌ Testing invalid login...");
  
  const response = await makeRequest("/auth/login", {
    method: "POST",
    body: JSON.stringify({
      email: TEST_USER.email,
      password: "wrong-password"
    })
  });

  assertEquals(response.status, 401, "Invalid login should fail");
  
  console.log("✅ Invalid login test passed");
});

// ================================
// Task Execution Tests
// ================================

Deno.test("Tasks - Execute simple task", async () => {
  console.log("🤖 Testing task execution...");
  
  const taskPayload = {
    instruction: "Navigate to httpbin.org and take a screenshot",
    url: "https://httpbin.org",
    options: {
      timeout: 30000,
      headless: true,
      viewport: { width: 1280, height: 720 }
    }
  };

  const response = await makeRequest("/execute", {
    method: "POST",
    body: JSON.stringify(taskPayload)
  });

  // Should accept the task (even if workers aren't running)
  assertEquals(response.status, 202, "Task should be accepted");
  
  const data = await response.json();
  assertExists(data.task_id);
  assertExists(data.status);
  assertEquals(data.status, "queued");
  
  console.log("✅ Task execution test passed");
});

Deno.test("Tasks - Execute task without authentication should fail", async () => {
  console.log("🔒 Testing unauthenticated task execution...");
  
  const tempToken = authToken;
  authToken = ""; // Remove auth token
  
  const response = await makeRequest("/execute", {
    method: "POST",
    body: JSON.stringify({
      instruction: "Test task",
      url: "https://example.com"
    })
  });

  assertEquals(response.status, 401, "Unauthenticated request should fail");
  
  authToken = tempToken; // Restore auth token
  
  console.log("✅ Unauthenticated task test passed");
});

Deno.test("Tasks - Invalid task payload should fail", async () => {
  console.log("⚠️  Testing invalid task payload...");
  
  const response = await makeRequest("/execute", {
    method: "POST",
    body: JSON.stringify({
      // Missing required fields
      instruction: ""
    })
  });

  assertEquals(response.status, 400, "Invalid payload should fail");
  
  console.log("✅ Invalid task payload test passed");
});

// ================================
// Pre-scripts Tests
// ================================

Deno.test("Pre-scripts - List pre-scripts", async () => {
  console.log("📝 Testing pre-scripts listing...");
  
  const response = await makeRequest("/prescripts");
  
  // Should return list even if empty
  assertEquals(response.status, 200);
  
  const data = await response.json();
  assertExists(data.prescripts);
  assertEquals(Array.isArray(data.prescripts), true);
  
  console.log("✅ Pre-scripts listing test passed");
});

Deno.test("Pre-scripts - Create new pre-script", async () => {
  console.log("➕ Testing pre-script creation...");
  
  const prescriptPayload = {
    name: "Test Login Script",
    description: "Automated login for testing",
    category: "authentication",
    actions: [
      {
        type: "navigate",
        url: "https://httpbin.org/forms/post"
      },
      {
        type: "fill",
        selector: "input[name='email']",
        value: "{{email}}"
      },
      {
        type: "fill",
        selector: "input[name='password']",
        value: "{{password}}"
      },
      {
        type: "click",
        selector: "input[type='submit']"
      }
    ],
    variables: [
      { name: "email", type: "string", required: true },
      { name: "password", type: "string", required: true }
    ]
  };

  const response = await makeRequest("/prescripts", {
    method: "POST",
    body: JSON.stringify(prescriptPayload)
  });

  assertEquals(response.status, 201, "Pre-script creation should succeed");
  
  const data = await response.json();
  assertExists(data.prescript);
  assertExists(data.prescript.id);
  assertEquals(data.prescript.name, prescriptPayload.name);
  assertEquals(data.prescript.category, prescriptPayload.category);
  
  console.log("✅ Pre-script creation test passed");
});

// ================================
// Metrics Tests
// ================================

Deno.test("Metrics - Get system metrics", async () => {
  console.log("📊 Testing metrics endpoint...");
  
  const response = await makeRequest("/metrics");
  assertEquals(response.status, 200);
  
  const data = await response.json();
  assertExists(data.metrics);
  
  console.log("✅ Metrics test passed");
});

// ================================
// Rate Limiting Tests
// ================================

Deno.test("Rate Limiting - Should respect rate limits", async () => {
  console.log("🚦 Testing rate limiting...");
  
  // Make multiple rapid requests to test rate limiting
  const promises: Promise<Response>[] = [];
  for (let i = 0; i < 5; i++) {
    promises.push(makeRequest("/health"));
  }
  
  const responses = await Promise.all(promises);
  
  // All health checks should succeed (rate limits usually more permissive for health)
  responses.forEach((response, index) => {
    assertEquals(response.status, 200, `Request ${index} should succeed`);
  });
  
  console.log("✅ Rate limiting test passed");
});

// ================================
// Error Handling Tests
// ================================

Deno.test("Error Handling - 404 for non-existent endpoints", async () => {
  console.log("🔍 Testing 404 handling...");
  
  const response = await makeRequest("/non-existent-endpoint");
  assertEquals(response.status, 404);
  
  console.log("✅ 404 handling test passed");
});

Deno.test("Error Handling - Invalid JSON should return 400", async () => {
  console.log("⚠️  Testing invalid JSON handling...");
  
  const response = await makeRequest("/auth/login", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: "invalid-json-content"
  });
  
  assertEquals(response.status, 400);
  
  console.log("✅ Invalid JSON handling test passed");
});

// ================================
// Cleanup
// ================================

Deno.test("Cleanup - Test environment cleanup", async () => {
  console.log("🧹 Running cleanup...");
  
  // Here you could add cleanup logic like:
  // - Delete test user
  // - Clean up test data
  // - Reset test state
  
  console.log("✅ Cleanup completed");
});
