/**
 * Simple API Health Check Test
 * This is a minimal test to verify the API is working
 */

import { assertEquals } from "testing";

Deno.test("API Health Check", async () => {
  try {
    const response = await fetch("http://localhost:8000/health");
    assertEquals(response.status, 200);
    
    const data = await response.json();
    assertEquals(data.status, "healthy");
    
    console.log("✅ API is healthy and responding");
  } catch (error) {
    console.error("❌ API health check failed:", error.message);
    throw error;
  }
});

Deno.test("API Endpoints Availability", async () => {
  const endpoints = [
    "/health",
    "/auth/register", // Should return method not allowed for GET
    "/auth/login",    // Should return method not allowed for GET  
    "/execute",       // Should return 401 for unauthenticated request
    "/prescripts",    // Should return 401 for unauthenticated request
    "/metrics"        // Should return 401 for unauthenticated request
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`http://localhost:8000${endpoint}`);
      
      // We expect different status codes for different endpoints
      const acceptableStatuses = [200, 401, 405]; // OK, Unauthorized, Method Not Allowed
      
      if (acceptableStatuses.includes(response.status)) {
        console.log(`✅ ${endpoint}: ${response.status} ${response.statusText}`);
      } else {
        console.log(`⚠️  ${endpoint}: ${response.status} ${response.statusText}`);
      }
      
    } catch (error) {
      console.error(`❌ ${endpoint}: ${error.message}`);
    }
  }
});
