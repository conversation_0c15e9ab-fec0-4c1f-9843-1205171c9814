# AERY Gateway Environment Variables

# CORS Configuration
# Set to 'false' to disable CORS completely for internal API usage
# Set to 'true' or omit for normal CORS behavior (default: true)
CORS_ENABLED=true

# CORS Origins (only used when CORS_ENABLED=true)
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://localhost:3001

# CORS Methods (only used when CORS_ENABLED=true)
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH

# CORS Headers (only used when CORS_ENABLED=true)
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-API-Key,X-Requested-With,Accept,Origin,Cache-Control,Pragma

# CORS Credentials (only used when CORS_ENABLED=true)
CORS_ALLOW_CREDENTIALS=true

# CORS Max Age (only used when CORS_ENABLED=true)
CORS_MAX_AGE=86400

# Server Configuration
PORT=8000
HOST=0.0.0.0
ENVIRONMENT=development

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/aery

# Redis
REDIS_URL=redis://localhost:6379
REDIS_QUEUE_PREFIX=aery:queue

# API Keys
OPENROUTER_API_KEY=your_openrouter_api_key
API_SECRET_KEY=your_api_secret_key
JWT_SECRET=your_jwt_secret
COHERE_API_KEY=your_cohere_api_key

# AI Services
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000
SCREENSHOT_PATH=./artifacts/screenshots

# Pre-scripts
PRESCRIPT_CACHE_TTL=86400
PRESCRIPT_SUCCESS_THRESHOLD=0.7

# RAG Configuration
EMBEDDING_MODEL=embed-english-v3.0
RAG_TOP_K=5
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200

# Security
ALLOWED_ORIGINS=http://localhost:8000