#!/usr/bin/env -S deno run -A --watch=src/

import { start } from "$fresh/server.ts";
import manifest from "./fresh.gen.ts";
import config from "./fresh.config.ts";
import { config as envConfig } from "./src/lib/config.ts";
import { db } from "./src/lib/database.ts";
import { redis } from "./src/lib/redis.ts";
import { AuthService } from "./src/lib/auth.ts";

// Initialize services
async function initializeServices() {
  try {
    console.log("🚀 Initializing AERY Gateway...");

    // Initialize authentication
    await AuthService.initJwtKey();
    console.log("✅ Authentication initialized");

    // Connect to database
    await db.connect();
    console.log("✅ Database connected");

    // Connect to Redis
    await redis.connect();
    console.log("✅ Redis connected");

    // Test connections
    const dbHealth = await db.healthCheck();
    const redisHealth = await redis.healthCheck();

    if (!dbHealth) {
      throw new Error("Database health check failed");
    }

    if (!redisHealth) {
      throw new Error("Redis health check failed");
    }

    console.log("✅ All services initialized successfully");
  } catch (error) {
    console.error("❌ Failed to initialize services:", error);
    Deno.exit(1);
  }
}

// Graceful shutdown
function setupGracefulShutdown() {
  const shutdown = async (signal: string) => {
    console.log(`📡 Received ${signal}, shutting down gracefully...`);

    try {
      // Close Redis connection
      await redis.disconnect();
      console.log("✅ Redis disconnected");

      // Close database connection
      await db.disconnect();
      console.log("✅ Database disconnected");

      console.log("👋 AERY Gateway shutdown complete");
      Deno.exit(0);
    } catch (error) {
      console.error("❌ Error during shutdown:", error);
      Deno.exit(1);
    }
  };

  // Handle shutdown signals
  Deno.addSignalListener("SIGINT", () => shutdown("SIGINT"));
  Deno.addSignalListener("SIGTERM", () => shutdown("SIGTERM"));
}

// Error handling
function setupErrorHandling() {
  globalThis.addEventListener("error", (event) => {
    console.error("🚨 Unhandled error:", event.error);
  });

  globalThis.addEventListener("unhandledrejection", (event) => {
    console.error("🚨 Unhandled promise rejection:", event.reason);
    event.preventDefault();
  });
}

// Main function
async function main() {
  try {
    // Setup error handling
    setupErrorHandling();

    // Setup graceful shutdown
    setupGracefulShutdown();

    // Initialize services
    await initializeServices();

    // Start the server
    console.log(`🌐 Starting AERY Gateway on port ${envConfig.server.port}`);
    console.log(`📊 Environment: ${envConfig.environment}`);
    console.log(`🔗 Server: ${envConfig.server.host}:${envConfig.server.port}`);

    await start(manifest, config);
  } catch (error) {
    console.error("❌ Failed to start AERY Gateway:", error);
    Deno.exit(1);
  }
}

// Start the application
if (import.meta.main) {
  main();
}

export { main };
