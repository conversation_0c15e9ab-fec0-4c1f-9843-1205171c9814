{"name": "aery-gateway", "version": "1.0.0", "description": "AERY Browser Automation API - Gateway Service", "type": "module", "scripts": {"dev": "deno task start", "start": "deno run -A --watch=src/ main.ts", "build": "deno run -A build.ts", "preview": "deno run -A main.ts", "test": "deno test -A", "fmt": "deno fmt", "lint": "deno lint", "check": "deno task fmt && deno task lint && deno check **/*.ts", "health": "curl -f http://localhost:8000/api/health || exit 1"}, "keywords": ["automation", "browser", "ai", "api", "gateway", "deno", "fresh"], "author": "AERY Team", "license": "MIT"}