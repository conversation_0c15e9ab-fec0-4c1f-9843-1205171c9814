import { defineConfig } from "$fresh/server.ts";

// Función para obtener configuración CORS condicional
function getCorsConfig() {
  const corsEnabled = Deno.env.get("CORS_ENABLED") !== "false";
  
  if (!corsEnabled) {
    console.log("🚫 CORS deshabilitado en fresh.config.ts");
    return undefined;
  }
  
  console.log("✅ CORS habilitado en fresh.config.ts");
  return {
    origin: [
      "http://localhost:3000", // Client
      "http://localhost:8080", // Alternative client port
      "http://localhost:3001", // Development
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-API-Key",
      "X-Requested-With",
      "Accept",
      "Origin",
    ],
  };
}

export default defineConfig({
  port: parseInt(Deno.env.get("PORT") || "8000"),
  hostname: Deno.env.get("HOST") || "0.0.0.0",

  // No static files for API-only server
  staticDir: null,

  // Build configuration
  build: {
    target: ["chrome99", "firefox99", "safari15"],
  },

  // Development configuration
  dev: true,

  // Server configuration
  server: {
    // CORS configuration for API (condicional)
    ...(getCorsConfig() && { cors: getCorsConfig() }),

    // Security headers
    headers: {
      "X-Content-Type-Options": "nosniff",
      "X-Frame-Options": "DENY",
      "X-XSS-Protection": "1; mode=block",
      "Referrer-Policy": "strict-origin-when-cross-origin",
      "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
    },
  },

  // Plugin configuration
  plugins: [],

  // Route configuration for API
  routes: {
    // API routes configuration
    "/api/*": {
      // Enable JSON parsing
      bodyParser: {
        json: {
          limit: "10mb",
        },
        urlencoded: {
          limit: "10mb",
          extended: true,
        },
      },

      // Rate limiting
      rateLimit: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 1000, // requests per window
        message: {
          error: "Too many requests",
          message: "Rate limit exceeded. Please try again later.",
        },
      },
    },
  },

  // Error handling
  errorHandler: (error: Error, request: Request) => {
    console.error("🚨 Gateway error:", error);

    return new Response(
      JSON.stringify({
        error: "Internal Server Error",
        message: "An error occurred processing your request",
        timestamp: new Date().toISOString(),
        path: new URL(request.url).pathname,
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
  },

  // 404 handler for API
  notFoundHandler: (request: Request) => {
    const url = new URL(request.url);

    return new Response(
      JSON.stringify({
        error: "Not Found",
        message: "API endpoint not found",
        timestamp: new Date().toISOString(),
        path: url.pathname,
      }),
      {
        status: 404,
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
  },
});
