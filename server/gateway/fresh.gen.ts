// DO NOT EDIT. This file is generated by Fresh.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $_app from "./routes/_app.tsx";
import * as $api_index from "./routes/api/index.ts";
import * as $api_models_id_ from "./routes/api/models/[id].ts";
import * as $api_models_agents from "./routes/api/models/agents.ts";
import * as $api_models_index from "./routes/api/models/index.ts";
import * as $api_models_stats from "./routes/api/models/stats.ts";
import * as $api_models_test from "./routes/api/models/test.ts";
import * as $api_rag_id_stories_action_ from "./routes/api/rag/[id]/stories/[action].ts";
import * as $api_rag_knowledge from "./routes/api/rag/knowledge.ts";
import * as $api_rag_projects from "./routes/api/rag/projects.ts";
import * as $api_rag_projects_id_knowledge from "./routes/api/rag/projects/[id]/knowledge.ts";
import * as $api_rag_stories from "./routes/api/rag/stories.ts";
import * as $auth_login from "./routes/auth/login.ts";
import * as $auth_register from "./routes/auth/register.ts";
import * as $execute from "./routes/execute.ts";
import * as $health from "./routes/health.ts";
import * as $metrics_index from "./routes/metrics/index.ts";
import * as $prescripts_index from "./routes/prescripts/index.ts";
import * as $rag_projects from "./routes/rag/projects.ts";
import * as $tasks_id_status from "./routes/tasks/[id]/status.ts";
import * as $tasks_index from "./routes/tasks/index.ts";

const manifest = {
  routes: {
    "./routes/_app.tsx": $_app,
    "./routes/api/index.ts": $api_index,
    "./routes/api/models/[id].ts": $api_models_id_,
    "./routes/api/models/agents.ts": $api_models_agents,
    "./routes/api/models/index.ts": $api_models_index,
    "./routes/api/models/stats.ts": $api_models_stats,
    "./routes/api/models/test.ts": $api_models_test,
    "./routes/api/rag/[id]/stories/[action].ts": $api_rag_id_stories_action_,
    "./routes/api/rag/knowledge.ts": $api_rag_knowledge,
    "./routes/api/rag/projects.ts": $api_rag_projects,
    "./routes/api/rag/projects/[id]/knowledge.ts": $api_rag_projects_id_knowledge,
    "./routes/api/rag/stories.ts": $api_rag_stories,
    "./routes/auth/login.ts": $auth_login,
    "./routes/auth/register.ts": $auth_register,
    "./routes/execute.ts": $execute,
    "./routes/health.ts": $health,
    "./routes/metrics/index.ts": $metrics_index,
    "./routes/prescripts/index.ts": $prescripts_index,
    "./routes/rag/projects.ts": $rag_projects,
    "./routes/tasks/[id]/status.ts": $tasks_id_status,
    "./routes/tasks/index.ts": $tasks_index,
  },
  islands: {},
  baseUrl: import.meta.url,
};

export default manifest;
