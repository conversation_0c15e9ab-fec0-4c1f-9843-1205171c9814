#!/usr/bin/env python3
"""
Motor de Automatización del Navegador para AERY

Este módulo maneja la ejecución de acciones de automatización web
usando Playwright, incluyendo capturas de pantalla, extracción de datos
y ejecución de pre-scripts optimizados.
"""

import asyncio
import json
import time
import uuid
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime

from loguru import logger
from playwright.async_api import (
    async_playwright,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    Playwright,
    TimeoutError as PlaywrightTimeoutError
)

from ..utils.config import get_config
from ..utils.selectors import SelectorEngine
from ..utils.screenshots import ScreenshotManager
from ..utils.data_extractor import DataExtractor


class BrowserEngine:
    """Motor principal de automatización del navegador"""
    
    def __init__(self):
        self.config = get_config()
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.contexts: Dict[str, BrowserContext] = {}
        self.pages: Dict[str, Page] = {}
        self.selector_engine = SelectorEngine()
        self.screenshot_manager = ScreenshotManager()
        self.data_extractor = DataExtractor()
        self.initialized = False
        
        # Configuración por defecto
        self.default_timeout = 30000  # 30 segundos
        self.default_viewport = {"width": 1920, "height": 1080}
        self.user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    async def initialize(self):
        """Inicializa Playwright y el navegador"""
        try:
            logger.info("🚀 Inicializando motor de navegador")
            
            # Inicializar Playwright
            self.playwright = await async_playwright().start()
            
            # Configurar navegador
            browser_config = {
                "headless": self.config.browser_headless,
                "args": [
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding"
                ]
            }
            
            # Lanzar navegador
            self.browser = await self.playwright.chromium.launch(**browser_config)
            
            # Componentes ya inicializados en el constructor
            
            self.initialized = True
            logger.info("✅ Motor de navegador inicializado")
            
        except Exception as e:
            logger.error(f"❌ Error inicializando navegador: {e}")
            raise
    
    async def create_context(
        self,
        context_id: Optional[str] = None,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """Crea un nuevo contexto de navegador"""
        
        if not self.initialized:
            raise RuntimeError("Motor no inicializado")
        
        try:
            context_id = context_id or str(uuid.uuid4())
            
            # Configuración del contexto
            context_options = {
                "viewport": options.get("viewport", self.default_viewport),
                "user_agent": options.get("user_agent", self.user_agent),
                "ignore_https_errors": True,
                "java_script_enabled": True,
                "accept_downloads": True,
                "record_video_dir": options.get("record_video") and "./videos" or None,
                "record_har_path": options.get("record_har") and f"./hars/{context_id}.har" or None
            }
            
            # Crear contexto
            context = await self.browser.new_context(**context_options)
            
            # Configurar interceptores
            await self._setup_context_interceptors(context, options)
            
            self.contexts[context_id] = context
            
            logger.info(f"✅ Contexto {context_id} creado")
            return context_id
            
        except Exception as e:
            logger.error(f"❌ Error creando contexto: {e}")
            raise
    
    async def create_page(
        self,
        context_id: str,
        page_id: Optional[str] = None
    ) -> str:
        """Crea una nueva página en el contexto especificado"""
        
        try:
            if context_id not in self.contexts:
                raise ValueError(f"Contexto {context_id} no existe")
            
            page_id = page_id or str(uuid.uuid4())
            context = self.contexts[context_id]
            
            # Crear página
            page = await context.new_page()
            
            # Configurar timeouts
            page.set_default_timeout(self.default_timeout)
            page.set_default_navigation_timeout(self.default_timeout)
            
            # Configurar manejadores de eventos
            await self._setup_page_handlers(page)
            
            self.pages[page_id] = page
            
            logger.info(f"✅ Página {page_id} creada en contexto {context_id}")
            return page_id
            
        except Exception as e:
            logger.error(f"❌ Error creando página: {e}")
            raise
    
    async def execute_actions(
        self,
        actions: List[Dict[str, Any]],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta una secuencia de acciones"""
        
        start_time = time.time()
        execution_id = str(uuid.uuid4())
        
        try:
            logger.info(f"🎬 Ejecutando {len(actions)} acciones (ID: {execution_id})")
            
            # Crear contexto y página
            context_id = await self.create_context(options=options)
            page_id = await self.create_page(context_id)
            page = self.pages[page_id]
            
            # Resultados de ejecución
            results = []
            artifacts = []
            extracted_data = {}
            
            # Ejecutar acciones secuencialmente
            for i, action in enumerate(actions):
                try:
                    logger.info(f"🔄 Ejecutando acción {i+1}/{len(actions)}: {action.get('type', 'unknown')}")
                    
                    # Ejecutar acción individual
                    action_result = await self._execute_single_action(
                        page, action, i, options
                    )
                    
                    results.append(action_result)
                    
                    # Recopilar artefactos
                    if action_result.get("artifacts"):
                        artifacts.extend(action_result["artifacts"])
                    
                    # Recopilar datos extraídos
                    if action_result.get("extracted_data"):
                        extracted_data.update(action_result["extracted_data"])
                    
                    # Verificar si la acción falló
                    if not action_result.get("success", True):
                        logger.warning(f"⚠️ Acción {i+1} falló: {action_result.get('error')}")
                        
                        # Decidir si continuar o abortar
                        if action.get("critical", False):
                            raise Exception(f"Acción crítica falló: {action_result.get('error')}")
                    
                    # Espera entre acciones si se especifica
                    if action.get("wait_after"):
                        await asyncio.sleep(action["wait_after"] / 1000)
                    
                except Exception as e:
                    logger.error(f"❌ Error en acción {i+1}: {e}")
                    
                    error_result = {
                        "action_index": i,
                        "success": False,
                        "error": str(e),
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    
                    results.append(error_result)
                    
                    # Si es crítica, abortar
                    if action.get("critical", False):
                        raise
            
            # Captura final de pantalla
            final_screenshot_result = await self.screenshot_manager.take_screenshot(
                page, f"final_{execution_id}"
            )
            final_screenshot = final_screenshot_result.get('filepath') if final_screenshot_result.get('success') else None
            
            if final_screenshot:
                artifacts.append({
                    "type": "screenshot",
                    "name": "final_state",
                    "path": final_screenshot,
                    "timestamp": datetime.utcnow().isoformat()
                })
            
            execution_time = time.time() - start_time
            
            # Limpiar recursos
            await self._cleanup_execution(context_id, page_id)
            
            logger.info(f"✅ Ejecución {execution_id} completada en {execution_time:.2f}s")
            
            return {
                "success": True,
                "execution_id": execution_id,
                "results": results,
                "artifacts": artifacts,
                "data": extracted_data,
                "executionTime": execution_time,
                "timing": {
                    "start": start_time,
                    "end": time.time(),
                    "duration": execution_time
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Error ejecutando acciones: {e}")
            
            # Limpiar en caso de error
            try:
                await self._cleanup_execution(context_id, page_id)
            except:
                pass
            
            return {
                "success": False,
                "execution_id": execution_id,
                "error": str(e),
                "executionTime": time.time() - start_time
            }
    
    async def execute_prescript(
        self,
        prescript: Dict[str, Any],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta un pre-script optimizado"""
        
        try:
            logger.info(f"⚡ Ejecutando pre-script v{prescript.get('version', '1.0')}")
            
            # Validar pre-script
            if not self._validate_prescript(prescript):
                raise ValueError("Pre-script inválido")
            
            # Ejecutar acciones del pre-script
            actions = prescript.get("actions", [])
            
            # Añadir metadatos de optimización
            for action in actions:
                action["optimized"] = True
                action["prescript_version"] = prescript.get("version")
            
            # Ejecutar con configuración optimizada
            optimized_options = {
                **(options or {}),
                "fast_mode": True,
                "skip_screenshots": prescript.get("metadata", {}).get("skip_screenshots", False),
                "reduced_waits": True
            }
            
            result = await self.execute_actions(actions, optimized_options)
            
            if result["success"]:
                logger.info("✅ Pre-script ejecutado exitosamente")
            else:
                logger.warning("⚠️ Pre-script falló, se requiere fallback")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error ejecutando pre-script: {e}")
            return {
                "success": False,
                "error": str(e),
                "executionTime": 0
            }
    
    async def _execute_single_action(
        self,
        page: Page,
        action: Dict[str, Any],
        index: int,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta una acción individual con reintentos automáticos"""
        
        max_retries = options.get('max_retries', 2) if options else 2
        retry_delay = options.get('retry_delay', 1) if options else 1
        
        for attempt in range(max_retries + 1):
            if attempt > 0:
                logger.info(f"🔄 Reintentando acción {action.get('type')} (intento {attempt + 1}/{max_retries + 1})")
                await asyncio.sleep(retry_delay * attempt)  # Backoff exponencial
            
            result = await self._execute_single_action_attempt(page, action, index, options)
            
            # Si la acción fue exitosa o no se recomienda reintentar, retornar
            if result.get('success') or not result.get('retry_recommended', False):
                if attempt > 0 and result.get('success'):
                    logger.info(f"✅ Acción {action.get('type')} exitosa después de {attempt + 1} intentos")
                return result
        
        # Si llegamos aquí, todos los intentos fallaron
        logger.error(f"❌ Acción {action.get('type')} falló después de {max_retries + 1} intentos")
        return result
    
    async def _execute_single_action_attempt(
        self,
        page: Page,
        action: Dict[str, Any],
        index: int,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta un intento individual de una acción"""
        
        action_type = action.get("type")
        start_time = time.time()
        
        try:
            # Captura de pantalla antes (si está habilitada)
            if options and options.get("capture_before", False):
                await self.screenshot_manager.take_screenshot(
                    page, f"before_action_{index}"
                )
            
            # Ejecutar según el tipo de acción
            if action_type == "navigate":
                result = await self._action_navigate(page, action)
            elif action_type == "click":
                result = await self._action_click(page, action)
            elif action_type == "type":
                result = await self._action_type(page, action)
            elif action_type == "select":
                result = await self._action_select(page, action)
            elif action_type == "wait":
                result = await self._action_wait(page, action)
            elif action_type == "scroll":
                result = await self._action_scroll(page, action)
            elif action_type == "extract":
                result = await self._action_extract(page, action)
            elif action_type == "screenshot":
                result = await self._action_screenshot(page, action)
            elif action_type == "execute_script":
                result = await self._action_execute_script(page, action)
            elif action_type == "upload_file":
                result = await self._action_upload_file(page, action)
            elif action_type == "download":
                result = await self._action_download(page, action)
            else:
                raise ValueError(f"Tipo de acción no soportado: {action_type}")
            
            # Añadir metadatos
            result.update({
                "action_index": index,
                "action_type": action_type,
                "execution_time": time.time() - start_time,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            return result
            
        except PlaywrightTimeoutError as e:
            logger.error(f"⏰ Timeout en acción {action_type}: {e}")
            return {
                "action_index": index,
                "action_type": action_type,
                "success": False,
                "error": f"Timeout: {str(e)}",
                "error_type": "timeout",
                "execution_time": time.time() - start_time,
                "timestamp": datetime.utcnow().isoformat(),
                "retry_recommended": True
            }
        except ValueError as e:
            logger.error(f"❌ Error de validación en acción {action_type}: {e}")
            return {
                "action_index": index,
                "action_type": action_type,
                "success": False,
                "error": str(e),
                "error_type": "validation",
                "execution_time": time.time() - start_time,
                "timestamp": datetime.utcnow().isoformat(),
                "retry_recommended": False
            }
        except Exception as e:
            error_msg = str(e)
            error_type = "unknown"
            retry_recommended = True
            
            # Clasificar tipos de error comunes
            if "element not found" in error_msg.lower() or "selector" in error_msg.lower():
                error_type = "element_not_found"
                retry_recommended = True
            elif "network" in error_msg.lower() or "connection" in error_msg.lower():
                error_type = "network"
                retry_recommended = True
            elif "permission" in error_msg.lower() or "denied" in error_msg.lower():
                error_type = "permission"
                retry_recommended = False
            
            logger.error(f"❌ Error en acción {action_type} (tipo: {error_type}): {e}")
            return {
                "action_index": index,
                "action_type": action_type,
                "success": False,
                "error": error_msg,
                "error_type": error_type,
                "execution_time": time.time() - start_time,
                "timestamp": datetime.utcnow().isoformat(),
                "retry_recommended": retry_recommended
            }
    
    async def _action_navigate(self, page: Page, action: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta acción de navegación"""
        url = action.get("url")
        if not url:
            raise ValueError("URL requerida para navegación")
        
        await page.goto(url, wait_until="domcontentloaded")
        
        return {
            "success": True,
            "url": page.url,
            "title": await page.title()
        }
    
    async def _action_click(self, page: Page, action: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta acción de click"""
        selector = action.get("selector")
        if not selector:
            raise ValueError("Selector requerido para click")
        
        # Esperar elemento
        await page.wait_for_selector(selector, timeout=self.default_timeout)
        
        # Click
        await page.click(selector)
        
        return {
            "success": True,
            "selector": selector
        }
    
    async def _action_type(self, page: Page, action: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta acción de escritura"""
        selector = action.get("selector")
        text = action.get("text")
        
        if not selector or text is None:
            raise ValueError("Selector y texto requeridos para escritura")
        
        # Esperar elemento
        await page.wait_for_selector(selector, timeout=self.default_timeout)
        
        # Limpiar y escribir
        if action.get("clear", True):
            await page.fill(selector, "")
        
        await page.type(selector, str(text), delay=action.get("delay", 50))
        
        return {
            "success": True,
            "selector": selector,
            "text_length": len(str(text))
        }
    
    async def _action_wait(self, page: Page, action: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta acción de espera"""
        wait_type = action.get("wait_type", "timeout")
        
        if wait_type == "timeout":
            duration = action.get("duration", 1000)
            await asyncio.sleep(duration / 1000)
        elif wait_type == "selector":
            selector = action.get("selector")
            if not selector:
                raise ValueError("Selector requerido para espera")
            await page.wait_for_selector(selector, timeout=self.default_timeout)
        elif wait_type == "load_state":
            state = action.get("state", "domcontentloaded")
            await page.wait_for_load_state(state)
        
        return {
            "success": True,
            "wait_type": wait_type
        }
    
    async def _action_extract(self, page: Page, action: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta acción de extracción de datos"""
        extraction_config = action.get("config", {})
        
        extracted_data = await self.data_extractor.extract(
            page, extraction_config
        )
        
        return {
            "success": True,
            "extracted_data": extracted_data
        }
    
    async def _action_screenshot(self, page: Page, action: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta acción de captura de pantalla"""
        name = action.get("name", f"screenshot_{int(time.time())}")
        
        screenshot_result = await self.screenshot_manager.take_screenshot(page, name)
        
        if screenshot_result.get('success'):
            return {
                "success": True,
                "artifacts": [{
                    "type": "screenshot",
                    "name": name,
                    "path": screenshot_result.get('filepath'),
                    "base64": screenshot_result.get('base64'),
                    "timestamp": datetime.utcnow().isoformat()
                }]
            }
        else:
            return {
                "success": False,
                "error": screenshot_result.get('error', 'Error desconocido en captura')
            }
    
    def _validate_prescript(self, prescript: Dict[str, Any]) -> bool:
        """Valida la estructura de un pre-script"""
        required_fields = ["version", "actions"]
        
        for field in required_fields:
            if field not in prescript:
                logger.error(f"Campo requerido faltante en pre-script: {field}")
                return False
        
        if not isinstance(prescript["actions"], list):
            logger.error("Las acciones del pre-script deben ser una lista")
            return False
        
        return True
    
    async def _setup_context_interceptors(
        self,
        context: BrowserContext,
        options: Optional[Dict[str, Any]] = None
    ):
        """Configura interceptores para el contexto"""
        
        # Interceptor de requests
        async def handle_request(request):
            # Bloquear recursos innecesarios en modo rápido
            if options and options.get("fast_mode"):
                resource_type = request.resource_type
                if resource_type in ["image", "font", "media"]:
                    await request.abort()
                    return
            
            await request.continue_()
        
        await context.route("**/*", handle_request)
    
    async def _setup_page_handlers(self, page: Page):
        """Configura manejadores de eventos para la página"""
        
        # Manejador de errores
        page.on("pageerror", lambda error: logger.warning(f"Error en página: {error}"))
        
        # Manejador de console
        page.on("console", lambda msg: logger.debug(f"Console: {msg.text}"))
        
        # Manejador de diálogos
        page.on("dialog", lambda dialog: asyncio.create_task(dialog.accept()))
    
    async def _cleanup_execution(self, context_id: str, page_id: str):
        """Limpia recursos después de la ejecución"""
        try:
            # Cerrar página
            if page_id in self.pages:
                await self.pages[page_id].close()
                del self.pages[page_id]
            
            # Cerrar contexto
            if context_id in self.contexts:
                await self.contexts[context_id].close()
                del self.contexts[context_id]
                
        except Exception as e:
            logger.error(f"❌ Error limpiando ejecución: {e}")
    
    async def cleanup(self):
        """Limpia todos los recursos del motor"""
        try:
            logger.info("🧹 Limpiando motor de navegador")
            
            # Cerrar todas las páginas
            for page in self.pages.values():
                try:
                    await page.close()
                except:
                    pass
            
            # Cerrar todos los contextos
            for context in self.contexts.values():
                try:
                    await context.close()
                except:
                    pass
            
            # Cerrar navegador
            if self.browser:
                await self.browser.close()
            
            # Cerrar Playwright
            if self.playwright:
                await self.playwright.stop()
            
            # Limpiar componentes
            await self.screenshot_manager.cleanup()
            await self.data_extractor.cleanup()
            
            self.pages.clear()
            self.contexts.clear()
            self.initialized = False
            
            logger.info("✅ Motor de navegador limpiado")
            
        except Exception as e:
            logger.error(f"❌ Error limpiando motor: {e}")