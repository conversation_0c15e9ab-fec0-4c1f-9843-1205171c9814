"""API para gestión de modelos de IA"""

import json
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel
from loguru import logger

from ..utils.model_config import get_model_config_manager


# Modelos Pydantic para la API
class ModelInfo(BaseModel):
    """Información de un modelo"""
    name: str
    provider: str
    cost_per_1k_tokens: float
    max_tokens: int
    temperature: float
    top_p: float
    recommended_for: list


class ModelConfigRequest(BaseModel):
    """Solicitud para configurar un modelo"""
    model_id: str
    model_info: Optional[ModelInfo] = None


class AgentModelRequest(BaseModel):
    """Solicitud para configurar modelo de un agente"""
    agent_type: str
    model_id: str


class CurrentModelRequest(BaseModel):
    """Solicitud para cambiar el modelo actual"""
    model_id: str


# Router de la API
router = APIRouter(prefix="/api/models", tags=["models"])


@router.get("/current")
async def get_current_model():
    """Obtiene el modelo actual"""
    try:
        manager = get_model_config_manager()
        current_model = manager.get_current_model()
        model_config = manager.get_model_config(current_model)
        
        return {
            "success": True,
            "current_model": current_model,
            "config": model_config,
            "summary": manager.get_config_summary()
        }
    except Exception as e:
        logger.error(f"❌ Error obteniendo modelo actual: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo modelo actual: {str(e)}"
        )


@router.post("/current")
async def set_current_model(request: CurrentModelRequest):
    """Establece el modelo actual"""
    try:
        manager = get_model_config_manager()
        success = manager.set_current_model(request.model_id)
        
        if success:
            return {
                "success": True,
                "message": f"Modelo actualizado a: {request.model_id}",
                "current_model": manager.get_current_model()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No se pudo establecer el modelo: {request.model_id}"
            )
    except Exception as e:
        logger.error(f"❌ Error estableciendo modelo actual: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error estableciendo modelo actual: {str(e)}"
        )


@router.get("/available")
async def get_available_models():
    """Obtiene todos los modelos disponibles"""
    try:
        manager = get_model_config_manager()
        available_models = manager.get_available_models()
        
        return {
            "success": True,
            "models": available_models,
            "total": len(available_models)
        }
    except Exception as e:
        logger.error(f"❌ Error obteniendo modelos disponibles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo modelos disponibles: {str(e)}"
        )


@router.get("/agents")
async def get_agent_models():
    """Obtiene los modelos asignados a cada agente"""
    try:
        manager = get_model_config_manager()
        agent_types = ["instruction_analyzer", "action_planner", "element_selector", "validator", "self_healer"]
        
        agent_models = {}
        for agent_type in agent_types:
            agent_models[agent_type] = manager.get_model_for_agent(agent_type)
        
        return {
            "success": True,
            "agent_models": agent_models
        }
    except Exception as e:
        logger.error(f"❌ Error obteniendo modelos de agentes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo modelos de agentes: {str(e)}"
        )


@router.post("/agents")
async def set_agent_model(request: AgentModelRequest):
    """Establece el modelo para un agente específico"""
    try:
        manager = get_model_config_manager()
        success = manager.set_model_for_agent(request.agent_type, request.model_id)
        
        if success:
            return {
                "success": True,
                "message": f"Modelo para {request.agent_type} actualizado a: {request.model_id}",
                "agent_type": request.agent_type,
                "model_id": request.model_id
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No se pudo establecer el modelo para {request.agent_type}"
            )
    except Exception as e:
        logger.error(f"❌ Error estableciendo modelo de agente: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error estableciendo modelo de agente: {str(e)}"
        )


@router.post("/add")
async def add_model(request: ModelConfigRequest):
    """Agrega un nuevo modelo a la configuración"""
    try:
        manager = get_model_config_manager()
        
        if request.model_info:
            model_config = {
                "name": request.model_info.name,
                "provider": request.model_info.provider,
                "cost_per_1k_tokens": request.model_info.cost_per_1k_tokens,
                "max_tokens": request.model_info.max_tokens,
                "temperature": request.model_info.temperature,
                "top_p": request.model_info.top_p,
                "recommended_for": request.model_info.recommended_for
            }
        else:
            # Configuración por defecto
            model_config = {
                "name": request.model_id,
                "provider": "Unknown",
                "cost_per_1k_tokens": 0.001,
                "max_tokens": 4096,
                "temperature": 0.1,
                "top_p": 0.9,
                "recommended_for": ["general"]
            }
        
        success = manager.add_model(request.model_id, model_config)
        
        if success:
            return {
                "success": True,
                "message": f"Modelo agregado: {request.model_id}",
                "model_id": request.model_id,
                "config": model_config
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No se pudo agregar el modelo: {request.model_id}"
            )
    except Exception as e:
        logger.error(f"❌ Error agregando modelo: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error agregando modelo: {str(e)}"
        )


@router.delete("/{model_id}")
async def remove_model(model_id: str):
    """Elimina un modelo de la configuración"""
    try:
        manager = get_model_config_manager()
        success = manager.remove_model(model_id)
        
        if success:
            return {
                "success": True,
                "message": f"Modelo eliminado: {model_id}",
                "current_model": manager.get_current_model()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Modelo no encontrado: {model_id}"
            )
    except Exception as e:
        logger.error(f"❌ Error eliminando modelo: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error eliminando modelo: {str(e)}"
        )


@router.get("/config/{model_id}")
async def get_model_config(model_id: str):
    """Obtiene la configuración de un modelo específico"""
    try:
        manager = get_model_config_manager()
        model_config = manager.get_model_config(model_id)
        
        if model_config:
            return {
                "success": True,
                "model_id": model_id,
                "config": model_config
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Configuración no encontrada para el modelo: {model_id}"
            )
    except Exception as e:
        logger.error(f"❌ Error obteniendo configuración del modelo: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo configuración del modelo: {str(e)}"
        )


@router.post("/reload")
async def reload_config():
    """Recarga la configuración desde el archivo"""
    try:
        manager = get_model_config_manager()
        success = manager.reload_config()
        
        if success:
            return {
                "success": True,
                "message": "Configuración recargada exitosamente",
                "summary": manager.get_config_summary()
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No se pudo recargar la configuración"
            )
    except Exception as e:
        logger.error(f"❌ Error recargando configuración: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error recargando configuración: {str(e)}"
        )


@router.get("/summary")
async def get_config_summary():
    """Obtiene un resumen de la configuración actual"""
    try:
        manager = get_model_config_manager()
        summary = manager.get_config_summary()
        
        return {
            "success": True,
            "summary": summary
        }
    except Exception as e:
        logger.error(f"❌ Error obteniendo resumen de configuración: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo resumen de configuración: {str(e)}"
        )


@router.get("/fallback")
async def get_fallback_models():
    """Obtiene los modelos de respaldo"""
    try:
        manager = get_model_config_manager()
        fallback_models = manager.get_fallback_models()
        
        return {
            "success": True,
            "fallback_models": fallback_models
        }
    except Exception as e:
        logger.error(f"❌ Error obteniendo modelos de respaldo: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo modelos de respaldo: {str(e)}"
        )