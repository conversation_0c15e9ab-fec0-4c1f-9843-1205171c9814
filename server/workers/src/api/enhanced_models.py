"""
API mejorada para gestión de modelos con base de datos PostgreSQL y cache Redis
"""

from typing import Dict, Any, Optional, List
from fastapi import APIRouter, HTTPException, status, Query
from pydantic import BaseModel, Field
from decimal import Decimal
from loguru import logger

from ..utils.db_model_manager import get_db_model_manager


# === MODELOS PYDANTIC ===

class ModelProviderResponse(BaseModel):
    id: str
    name: str
    display_name: str
    api_base_url: str
    auth_type: str
    config: Dict[str, Any]
    enabled: bool
    rate_limit_rpm: int
    rate_limit_tpm: int


class ModelCategoryResponse(BaseModel):
    id: str
    name: str
    display_name: str
    description: str
    icon: str
    enabled: bool


class AIModelResponse(BaseModel):
    id: str
    model_id: str
    name: str
    provider_id: str
    category_id: str
    context_length: int
    max_tokens: Optional[int]
    supports_streaming: bool
    supports_function_calling: bool
    supports_vision: bool
    input_cost_per_1m: Decimal
    output_cost_per_1m: Decimal
    default_temperature: Decimal
    default_top_p: Decimal
    default_top_k: Optional[int]
    default_max_tokens: Optional[int]
    enabled: bool
    features: List[str]
    metadata: Dict[str, Any]


class CreateModelRequest(BaseModel):
    model_id: str = Field(..., description="ID único del modelo")
    name: str = Field(..., description="Nombre descriptivo del modelo")
    provider_name: str = Field(..., description="Nombre del proveedor")
    category_name: str = Field(..., description="Nombre de la categoría")
    context_length: int = Field(..., description="Longitud del contexto")
    max_tokens: Optional[int] = Field(None, description="Máximo tokens de salida")
    supports_streaming: bool = Field(True, description="Soporte para streaming")
    supports_function_calling: bool = Field(False, description="Soporte para function calling")
    supports_vision: bool = Field(False, description="Soporte para visión")
    input_cost_per_1m: Decimal = Field(..., description="Costo por 1M tokens de entrada")
    output_cost_per_1m: Decimal = Field(..., description="Costo por 1M tokens de salida")
    default_temperature: Decimal = Field(Decimal('0.1'), description="Temperatura por defecto")
    default_top_p: Decimal = Field(Decimal('0.9'), description="Top-p por defecto")
    default_top_k: Optional[int] = Field(None, description="Top-k por defecto")
    default_max_tokens: Optional[int] = Field(None, description="Max tokens por defecto")
    enabled: bool = Field(True, description="Estado habilitado")
    features: List[str] = Field(default_factory=list, description="Lista de características")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Metadatos adicionales")


class UpdateModelRequest(BaseModel):
    name: Optional[str] = None
    context_length: Optional[int] = None
    max_tokens: Optional[int] = None
    supports_streaming: Optional[bool] = None
    supports_function_calling: Optional[bool] = None
    supports_vision: Optional[bool] = None
    input_cost_per_1m: Optional[Decimal] = None
    output_cost_per_1m: Optional[Decimal] = None
    default_temperature: Optional[Decimal] = None
    default_top_p: Optional[Decimal] = None
    default_top_k: Optional[int] = None
    default_max_tokens: Optional[int] = None
    enabled: Optional[bool] = None
    features: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


class AgentConfigurationResponse(BaseModel):
    id: str
    agent_type: str
    display_name: str
    description: str
    primary_model_id: str
    fallback_model_id: Optional[str]
    temperature: Optional[Decimal]
    max_tokens: Optional[int]
    top_p: Optional[Decimal]
    system_prompt: Optional[str]
    enabled: bool


class UpdateAgentConfigRequest(BaseModel):
    primary_model_id: Optional[str] = None
    fallback_model_id: Optional[str] = None
    temperature: Optional[Decimal] = None
    max_tokens: Optional[int] = None
    top_p: Optional[Decimal] = None
    system_prompt: Optional[str] = None
    enabled: Optional[bool] = None


class ModelTestRequest(BaseModel):
    model_id: str
    prompt: str
    temperature: Optional[Decimal] = None
    max_tokens: Optional[int] = None
    top_p: Optional[Decimal] = None


# === ROUTER ===

router = APIRouter(prefix="/api/models", tags=["models"])


# === ENDPOINTS DE PROVEEDORES ===

@router.get("/providers", response_model=List[ModelProviderResponse])
async def get_providers(enabled_only: bool = Query(False, description="Solo proveedores habilitados")):
    """Obtiene todos los proveedores de modelos"""
    try:
        manager = await get_db_model_manager()
        providers = await manager.get_providers(enabled_only=enabled_only)
        
        return [
            ModelProviderResponse(
                id=p.id,
                name=p.name,
                display_name=p.display_name,
                api_base_url=p.api_base_url,
                auth_type=p.auth_type,
                config=p.config,
                enabled=p.enabled,
                rate_limit_rpm=p.rate_limit_rpm,
                rate_limit_tpm=p.rate_limit_tpm
            ) for p in providers
        ]
    except Exception as e:
        logger.error(f"Error obteniendo proveedores: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo proveedores: {str(e)}"
        )


# === ENDPOINTS DE CATEGORÍAS ===

@router.get("/categories", response_model=List[ModelCategoryResponse])
async def get_categories(enabled_only: bool = Query(False, description="Solo categorías habilitadas")):
    """Obtiene todas las categorías de modelos"""
    try:
        manager = await get_db_model_manager()
        categories = await manager.get_categories(enabled_only=enabled_only)
        
        return [
            ModelCategoryResponse(
                id=c.id,
                name=c.name,
                display_name=c.display_name,
                description=c.description,
                icon=c.icon,
                enabled=c.enabled
            ) for c in categories
        ]
    except Exception as e:
        logger.error(f"Error obteniendo categorías: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo categorías: {str(e)}"
        )


# === ENDPOINTS DE MODELOS ===

@router.get("", response_model=Dict[str, Any])
async def get_models(
    provider: Optional[str] = Query(None, description="Filtrar por proveedor"),
    category: Optional[str] = Query(None, description="Filtrar por categoría"),
    enabled: Optional[bool] = Query(None, description="Filtrar por estado")
):
    """Obtiene todos los modelos con filtros opcionales"""
    try:
        manager = await get_db_model_manager()
        
        # Obtener modelos
        models = await manager.get_models(
            provider_name=provider,
            category_name=category,
            enabled_only=enabled
        )
        
        # Obtener proveedores y categorías para metadatos
        providers = await manager.get_providers()
        categories = await manager.get_categories()
        
        model_responses = []
        for m in models:
            model_responses.append(AIModelResponse(
                id=m.id,
                model_id=m.model_id,
                name=m.name,
                provider_id=m.provider_id,
                category_id=m.category_id,
                context_length=m.context_length,
                max_tokens=m.max_tokens,
                supports_streaming=m.supports_streaming,
                supports_function_calling=m.supports_function_calling,
                supports_vision=m.supports_vision,
                input_cost_per_1m=m.input_cost_per_1m,
                output_cost_per_1m=m.output_cost_per_1m,
                default_temperature=m.default_temperature,
                default_top_p=m.default_top_p,
                default_top_k=m.default_top_k,
                default_max_tokens=m.default_max_tokens,
                enabled=m.enabled,
                features=m.features,
                metadata=m.metadata
            ))
        
        return {
            "success": True,
            "models": model_responses,
            "providers": [p.name for p in providers if p.enabled],
            "categories": [c.name for c in categories if c.enabled],
            "total": len(model_responses)
        }
        
    except Exception as e:
        logger.error(f"Error obteniendo modelos: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo modelos: {str(e)}"
        )


@router.get("/{model_id}", response_model=Dict[str, Any])
async def get_model(model_id: str):
    """Obtiene un modelo específico por ID"""
    try:
        manager = await get_db_model_manager()
        model = await manager.get_model_by_id(model_id)
        
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Modelo no encontrado: {model_id}"
            )
        
        return {
            "success": True,
            "model": AIModelResponse(
                id=model.id,
                model_id=model.model_id,
                name=model.name,
                provider_id=model.provider_id,
                category_id=model.category_id,
                context_length=model.context_length,
                max_tokens=model.max_tokens,
                supports_streaming=model.supports_streaming,
                supports_function_calling=model.supports_function_calling,
                supports_vision=model.supports_vision,
                input_cost_per_1m=model.input_cost_per_1m,
                output_cost_per_1m=model.output_cost_per_1m,
                default_temperature=model.default_temperature,
                default_top_p=model.default_top_p,
                default_top_k=model.default_top_k,
                default_max_tokens=model.default_max_tokens,
                enabled=model.enabled,
                features=model.features,
                metadata=model.metadata
            )
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error obteniendo modelo {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo modelo: {str(e)}"
        )


@router.post("", response_model=Dict[str, Any])
async def create_model(request: CreateModelRequest):
    """Crea un nuevo modelo"""
    try:
        manager = await get_db_model_manager()
        
        # Verificar que el modelo no exista
        existing = await manager.get_model_by_id(request.model_id)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"El modelo {request.model_id} ya existe"
            )
        
        # Obtener proveedor y categoría por nombre
        provider = await manager.get_provider_by_name(request.provider_name)
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Proveedor no encontrado: {request.provider_name}"
            )
        
        categories = await manager.get_categories()
        category = next((c for c in categories if c.name == request.category_name), None)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Categoría no encontrada: {request.category_name}"
            )
        
        # Preparar datos del modelo
        model_data = {
            "model_id": request.model_id,
            "name": request.name,
            "provider_id": provider.id,
            "category_id": category.id,
            "context_length": request.context_length,
            "max_tokens": request.max_tokens,
            "supports_streaming": request.supports_streaming,
            "supports_function_calling": request.supports_function_calling,
            "supports_vision": request.supports_vision,
            "input_cost_per_1m": request.input_cost_per_1m,
            "output_cost_per_1m": request.output_cost_per_1m,
            "default_temperature": request.default_temperature,
            "default_top_p": request.default_top_p,
            "default_top_k": request.default_top_k,
            "default_max_tokens": request.default_max_tokens,
            "enabled": request.enabled,
            "features": request.features,
            "metadata": request.metadata
        }
        
        # Crear modelo
        created_model = await manager.create_model(model_data)
        
        return {
            "success": True,
            "message": f"Modelo {request.model_id} creado exitosamente",
            "model": AIModelResponse(
                id=created_model.id,
                model_id=created_model.model_id,
                name=created_model.name,
                provider_id=created_model.provider_id,
                category_id=created_model.category_id,
                context_length=created_model.context_length,
                max_tokens=created_model.max_tokens,
                supports_streaming=created_model.supports_streaming,
                supports_function_calling=created_model.supports_function_calling,
                supports_vision=created_model.supports_vision,
                input_cost_per_1m=created_model.input_cost_per_1m,
                output_cost_per_1m=created_model.output_cost_per_1m,
                default_temperature=created_model.default_temperature,
                default_top_p=created_model.default_top_p,
                default_top_k=created_model.default_top_k,
                default_max_tokens=created_model.default_max_tokens,
                enabled=created_model.enabled,
                features=created_model.features,
                metadata=created_model.metadata
            )
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creando modelo: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creando modelo: {str(e)}"
        )


@router.put("/{model_id}", response_model=Dict[str, Any])
async def update_model(model_id: str, request: UpdateModelRequest):
    """Actualiza un modelo existente"""
    try:
        manager = await get_db_model_manager()
        
        # Verificar que el modelo existe
        existing = await manager.get_model_by_id(model_id)
        if not existing:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Modelo no encontrado: {model_id}"
            )
        
        # Preparar datos de actualización (solo campos no None)
        update_data = {}
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        if not update_data:
            # Retornar modelo sin cambios
            return {
                "success": True,
                "message": "No hay cambios para aplicar",
                "model": existing
            }
        
        # Actualizar modelo
        updated_model = await manager.update_model(model_id, update_data)
        
        return {
            "success": True,
            "message": f"Modelo {model_id} actualizado exitosamente",
            "model": AIModelResponse(
                id=updated_model.id,
                model_id=updated_model.model_id,
                name=updated_model.name,
                provider_id=updated_model.provider_id,
                category_id=updated_model.category_id,
                context_length=updated_model.context_length,
                max_tokens=updated_model.max_tokens,
                supports_streaming=updated_model.supports_streaming,
                supports_function_calling=updated_model.supports_function_calling,
                supports_vision=updated_model.supports_vision,
                input_cost_per_1m=updated_model.input_cost_per_1m,
                output_cost_per_1m=updated_model.output_cost_per_1m,
                default_temperature=updated_model.default_temperature,
                default_top_p=updated_model.default_top_p,
                default_top_k=updated_model.default_top_k,
                default_max_tokens=updated_model.default_max_tokens,
                enabled=updated_model.enabled,
                features=updated_model.features,
                metadata=updated_model.metadata
            )
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error actualizando modelo {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error actualizando modelo: {str(e)}"
        )


@router.delete("/{model_id}", response_model=Dict[str, Any])
async def delete_model(model_id: str):
    """Elimina un modelo"""
    try:
        manager = await get_db_model_manager()
        
        # Verificar que el modelo existe
        existing = await manager.get_model_by_id(model_id)
        if not existing:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Modelo no encontrado: {model_id}"
            )
        
        # Eliminar modelo
        success = await manager.delete_model(model_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"No se pudo eliminar el modelo: {model_id}"
            )
        
        return {
            "success": True,
            "message": f"Modelo {model_id} eliminado exitosamente"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error eliminando modelo {model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error eliminando modelo: {str(e)}"
        )


# === ENDPOINTS DE CONFIGURACIÓN DE AGENTES ===

@router.get("/agents", response_model=Dict[str, Any])
async def get_agent_configurations():
    """Obtiene todas las configuraciones de agentes"""
    try:
        manager = await get_db_model_manager()
        configurations = await manager.get_agent_configurations()
        
        config_responses = []
        for config in configurations:
            config_responses.append(AgentConfigurationResponse(
                id=config.id,
                agent_type=config.agent_type,
                display_name=config.display_name,
                description=config.description,
                primary_model_id=config.primary_model_id,
                fallback_model_id=config.fallback_model_id,
                temperature=config.temperature,
                max_tokens=config.max_tokens,
                top_p=config.top_p,
                system_prompt=config.system_prompt,
                enabled=config.enabled
            ))
        
        return {
            "success": True,
            "selections": config_responses
        }
        
    except Exception as e:
        logger.error(f"Error obteniendo configuraciones de agentes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo configuraciones de agentes: {str(e)}"
        )


@router.put("/agents/{agent_type}", response_model=Dict[str, Any])
async def update_agent_configuration(agent_type: str, request: UpdateAgentConfigRequest):
    """Actualiza la configuración de un agente específico"""
    try:
        manager = await get_db_model_manager()
        
        # Preparar datos de actualización
        update_data = {}
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No hay datos para actualizar"
            )
        
        # Actualizar configuración
        updated_config = await manager.update_agent_configuration(agent_type, update_data)
        
        if not updated_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Configuración de agente no encontrada: {agent_type}"
            )
        
        return {
            "success": True,
            "message": f"Configuración del agente {agent_type} actualizada exitosamente",
            "agent_type": agent_type,
            "configuration": AgentConfigurationResponse(
                id=updated_config.id,
                agent_type=updated_config.agent_type,
                display_name=updated_config.display_name,
                description=updated_config.description,
                primary_model_id=updated_config.primary_model_id,
                fallback_model_id=updated_config.fallback_model_id,
                temperature=updated_config.temperature,
                max_tokens=updated_config.max_tokens,
                top_p=updated_config.top_p,
                system_prompt=updated_config.system_prompt,
                enabled=updated_config.enabled
            )
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error actualizando configuración del agente {agent_type}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error actualizando configuración del agente: {str(e)}"
        )


# === ENDPOINTS DE ESTADÍSTICAS ===

@router.get("/stats", response_model=Dict[str, Any])
async def get_model_stats():
    """Obtiene estadísticas de uso de modelos"""
    try:
        # TODO: Implementar estadísticas de uso desde model_usage_stats
        return {
            "success": True,
            "stats": [],
            "total_cost": 0,
            "total_requests": 0,
            "message": "Estadísticas de uso disponibles próximamente"
        }
        
    except Exception as e:
        logger.error(f"Error obteniendo estadísticas: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error obteniendo estadísticas: {str(e)}"
        )


# === ENDPOINTS DE TESTING ===

@router.post("/test", response_model=Dict[str, Any])
async def test_model(request: ModelTestRequest):
    """Prueba un modelo con un prompt específico"""
    try:
        manager = await get_db_model_manager()
        
        # Verificar que el modelo existe
        model = await manager.get_model_by_id(request.model_id)
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Modelo no encontrado: {request.model_id}"
            )
        
        # TODO: Implementar test real del modelo
        return {
            "success": True,
            "model_id": request.model_id,
            "prompt": request.prompt,
            "response": "Función de test de modelo disponible próximamente",
            "tokens_used": 0,
            "response_time": 0,
            "cost": 0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error probando modelo {request.model_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error probando modelo: {str(e)}"
        )
