#!/usr/bin/env python3
"""
Coordinador de Agentes IA para AERY

Este módulo coordina múltiples agentes IA usando PocketFlow para
analizar instrucciones en lenguaje natural y generar acciones
de automatización web.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

from loguru import logger
# from pocketflow import PocketFlow, Agent, Workflow
# from pocketflow.providers import OpenRouterProvider
from openai import OpenAI

from ..utils.config import get_config, get_openrouter_config
from ..utils.prompts import PromptTemplates


class AgentCoordinator:
    """Coordinador principal de agentes IA"""
    
    def __init__(self):
        self.config = get_config()
        self.openrouter_config = get_openrouter_config()
        self.client: Optional[OpenAI] = None
        self.agents: Dict[str, str] = {}  # Simplified to store agent names
        self.workflows: Dict[str, str] = {}  # Simplified to store workflow names
        self.prompt_templates = PromptTemplates()
        self.initialized = False
    
    async def initialize(self):
        """Inicializa el cliente OpenAI y los agentes"""
        try:
            logger.info("🚀 Inicializando coordinador de agentes IA")
            
            # Configurar cliente OpenAI con OpenRouter usando configuración dinámica
            self.client = OpenAI(
                api_key=self.openrouter_config.api_key,
                base_url=self.openrouter_config.base_url
            )
            
            # Crear agentes especializados (simplificado)
            await self._create_agents()
            
            # Crear workflows (simplificado)
            await self._create_workflows()
            
            self.initialized = True
            logger.info("✅ Coordinador de agentes inicializado exitosamente")
            
        except Exception as e:
            logger.error(f"❌ Error inicializando coordinador: {e}")
            raise
    
    async def _create_agents(self):
        """Crea los agentes especializados (simplificado)"""
        
        # Registrar agentes disponibles
        self.agents["instruction_analyzer"] = "InstructionAnalyzer"
        self.agents["action_planner"] = "ActionPlanner"
        self.agents["element_selector"] = "ElementSelector"
        self.agents["validator"] = "ActionValidator"
        self.agents["self_healer"] = "SelfHealer"
        
        logger.info(f"✅ Agentes creados: {list(self.agents.keys())}")
    
    async def _create_workflows(self):
        """Crea los workflows de procesamiento (simplificado)"""
        
        # Registrar workflows disponibles
        self.workflows["sequential"] = "SequentialProcessing"
        self.workflows["parallel"] = "ParallelProcessing"
        
        logger.info(f"✅ Workflows creados: {list(self.workflows.keys())}")
    
    async def _generate_actions_with_openai(self, instruction: str, url: str) -> List[Dict[str, Any]]:
        """Genera acciones usando OpenAI con configuración dinámica de modelos"""
        
        try:
            # Obtener modelo y configuración dinámicamente
            current_model = self.openrouter_config.get_model_for_agent("action_planner")
            model_config = self.openrouter_config.get_model_config(current_model)
            
            logger.info(f"🤖 Usando modelo: {current_model} para generar acciones")
            
            # Prompt para generar acciones
            prompt = f"""
Analiza la siguiente instrucción y genera una secuencia de acciones web para ejecutarla en la URL: {url}

Instrucción: {instruction}

Genera una lista de acciones en formato JSON. Cada acción debe tener:
- type: tipo de acción (click, fill, navigate, wait, etc.)
- selector: selector CSS del elemento (si aplica)
- value: valor a ingresar (si aplica)
- description: descripción de la acción

Ejemplo:
[
  {{
    "type": "navigate",
    "url": "{url}",
    "description": "Navegar a la página"
  }},
  {{
    "type": "click",
    "selector": "#login-button",
    "description": "Hacer clic en el botón de login"
  }}
]

Responde solo con el JSON de las acciones:
"""
            
            response = await self.client.chat.completions.create(
                model=current_model,
                messages=[
                    {"role": "system", "content": "Eres un experto en automatización web. Genera acciones precisas y ejecutables."},
                    {"role": "user", "content": prompt}
                ],
                temperature=model_config.get("temperature", 0.1),
                max_tokens=model_config.get("max_tokens", 2000),
                top_p=model_config.get("top_p", 0.9)
            )
            
            content = response.choices[0].message.content.strip()
            
            # Intentar parsear el JSON
            try:
                import json
                actions = json.loads(content)
                if isinstance(actions, list):
                    logger.info(f"✅ Generadas {len(actions)} acciones con OpenAI")
                    return actions
                else:
                    raise ValueError("La respuesta no es una lista")
            except json.JSONDecodeError:
                logger.warning("⚠️ Error parseando JSON, generando acciones por defecto")
                return self._generate_default_actions(instruction, url)
                
        except Exception as e:
            logger.error(f"❌ Error generando acciones con OpenAI: {e}")
            return self._generate_default_actions(instruction, url)
    
    def _generate_default_actions(self, instruction: str, url: str) -> List[Dict[str, Any]]:
        """Genera acciones por defecto cuando falla OpenAI"""
        
        return [
            {
                "type": "navigate",
                "url": url,
                "description": f"Navegar a {url}"
            },
            {
                "type": "wait",
                "duration": 2000,
                "description": "Esperar a que cargue la página"
            },
            {
                "type": "screenshot",
                "description": f"Tomar captura para: {instruction}"
            }
        ]
    
    async def coordinate_agents(
        self,
        task_data: Dict[str, Any],
        workflow_type: str = "sequential"
    ) -> Dict[str, Any]:
        """Coordina la ejecución de agentes para procesar una tarea (simplificado)"""
        
        try:
            logger.info(f"🎯 Iniciando coordinación de agentes para tarea: {task_data.get('taskId', 'unknown')}")
            
            if not self.initialized:
                raise Exception("Coordinador no inicializado")
            
            # Procesar la instrucción usando OpenAI directamente
            instruction = task_data.get('instruction', '')
            url = task_data.get('url', '')
            
            # Generar acciones usando el cliente de OpenAI
            actions = await self._generate_actions_with_openai(instruction, url)
            
            result = {
                "success": True,
                "actions": actions,
                "confidence": 0.8,
                "metadata": {
                    "workflow_type": workflow_type,
                    "agents_used": list(self.agents.keys()),
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            logger.info(f"✅ Coordinación completada exitosamente")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error en coordinación de agentes: {e}")
            return {
                "success": False,
                "error": str(e),
                "actions": [],
                "confidence": 0.0,
                "metadata": {
                    "error_type": "coordination_error",
                    "timestamp": datetime.now().isoformat()
                }
            }
    

    

    

    

    

    

    
    def get_agent_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas de los agentes"""
        
        return {
            "total_agents": len(self.agents),
            "total_workflows": len(self.workflows),
            "initialized": self.initialized,
            "agents": list(self.agents.keys()),
            "workflows": list(self.workflows.keys())
        }
    
    async def cleanup(self):
        """Limpia recursos del coordinador"""
        
        try:
            logger.info("🧹 Limpiando coordinador de agentes")
            
            if self.client:
                # No hay cleanup específico para OpenAI client
                self.client = None
            
            self.agents.clear()
            self.workflows.clear()
            self.initialized = False
            
            logger.info("✅ Coordinador limpiado")
            
        except Exception as e:
            logger.error(f"❌ Error limpiando coordinador: {e}")