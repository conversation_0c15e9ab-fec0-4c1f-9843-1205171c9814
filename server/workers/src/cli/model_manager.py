#!/usr/bin/env python3
"""CLI para gestión de modelos de IA"""

import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any
import logging

# Agregar el directorio padre al path para importar módulos
sys.path.append(str(Path(__file__).parent.parent))

from utils.model_config import get_model_config_manager

logger = logging.getLogger(__name__)


def setup_logging():
    """Configura el logging para el CLI"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)-8s | %(message)s",
        datefmt="%H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)]
    )


def list_models():
    """Lista todos los modelos disponibles"""
    try:
        manager = get_model_config_manager()
        models = manager.get_available_models()
        current_model = manager.get_current_model()
        
        print("\n📋 Modelos Disponibles:")
        print("=" * 50)
        
        for model_id, config in models.items():
            status = "🟢 ACTUAL" if model_id == current_model else "⚪"
            print(f"{status} {model_id}")
            print(f"   Nombre: {config.get('name', 'N/A')}")
            print(f"   Proveedor: {config.get('provider', 'N/A')}")
            print(f"   Costo/1K tokens: ${config.get('cost_per_1k_tokens', 0):.4f}")
            print(f"   Max tokens: {config.get('max_tokens', 'N/A')}")
            print(f"   Recomendado para: {', '.join(config.get('recommended_for', []))}")
            print()
        
        print(f"Total de modelos: {len(models)}")
        print(f"Modelo actual: {current_model}")
        
    except Exception as e:
        logger.error(f"❌ Error listando modelos: {e}")
        return False
    return True


def show_current():
    """Muestra el modelo actual"""
    try:
        manager = get_model_config_manager()
        current_model = manager.get_current_model()
        config = manager.get_model_config(current_model)
        
        print(f"\n🤖 Modelo Actual: {current_model}")
        print("=" * 40)
        print(f"Nombre: {config.get('name', 'N/A')}")
        print(f"Proveedor: {config.get('provider', 'N/A')}")
        print(f"Temperature: {config.get('temperature', 'N/A')}")
        print(f"Max tokens: {config.get('max_tokens', 'N/A')}")
        print(f"Top P: {config.get('top_p', 'N/A')}")
        
    except Exception as e:
        logger.error(f"❌ Error obteniendo modelo actual: {e}")
        return False
    return True


def set_current_model(model_id: str):
    """Establece el modelo actual"""
    try:
        manager = get_model_config_manager()
        success = manager.set_current_model(model_id)
        
        if success:
            logger.info(f"✅ Modelo actualizado a: {model_id}")
        else:
            logger.error(f"❌ No se pudo establecer el modelo: {model_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error estableciendo modelo: {e}")
        return False
    return True


def show_agents():
    """Muestra los modelos asignados a cada agente"""
    try:
        manager = get_model_config_manager()
        agent_types = ["instruction_analyzer", "action_planner", "element_selector", "validator", "self_healer"]
        
        print("\n🤖 Modelos por Agente:")
        print("=" * 40)
        
        for agent_type in agent_types:
            model = manager.get_model_for_agent(agent_type)
            print(f"{agent_type}: {model}")
        
    except Exception as e:
        logger.error(f"❌ Error obteniendo modelos de agentes: {e}")
        return False
    return True


def set_agent_model(agent_type: str, model_id: str):
    """Establece el modelo para un agente específico"""
    try:
        manager = get_model_config_manager()
        success = manager.set_model_for_agent(agent_type, model_id)
        
        if success:
            logger.info(f"✅ Modelo para {agent_type} actualizado a: {model_id}")
        else:
            logger.error(f"❌ No se pudo establecer el modelo para {agent_type}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error estableciendo modelo de agente: {e}")
        return False
    return True


def add_model(model_id: str, name: str = None, provider: str = None, cost: float = None):
    """Agrega un nuevo modelo"""
    try:
        manager = get_model_config_manager()
        
        model_config = {
            "name": name or model_id,
            "provider": provider or "Unknown",
            "cost_per_1k_tokens": cost or 0.001,
            "max_tokens": 4096,
            "temperature": 0.1,
            "top_p": 0.9,
            "recommended_for": ["general"]
        }
        
        success = manager.add_model(model_id, model_config)
        
        if success:
            logger.info(f"✅ Modelo agregado: {model_id}")
        else:
            logger.error(f"❌ No se pudo agregar el modelo: {model_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error agregando modelo: {e}")
        return False
    return True


def remove_model(model_id: str):
    """Elimina un modelo"""
    try:
        manager = get_model_config_manager()
        success = manager.remove_model(model_id)
        
        if success:
            logger.info(f"✅ Modelo eliminado: {model_id}")
        else:
            logger.error(f"❌ No se pudo eliminar el modelo: {model_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error eliminando modelo: {e}")
        return False
    return True


def show_summary():
    """Muestra un resumen de la configuración"""
    try:
        manager = get_model_config_manager()
        summary = manager.get_config_summary()
        
        print("\n📊 Resumen de Configuración:")
        print("=" * 40)
        print(f"Modelo actual: {summary['current_model']}")
        print(f"Total de modelos: {summary['total_models']}")
        print(f"Archivo de configuración: {summary['config_file']}")
        print(f"Última actualización: {summary['last_updated']}")
        
        print("\nModelos por agente:")
        for agent, model in summary['agent_specific_models'].items():
            print(f"  {agent}: {model}")
        
        print(f"\nModelos de respaldo: {', '.join(summary['fallback_models'])}")
        
    except Exception as e:
        logger.error(f"❌ Error obteniendo resumen: {e}")
        return False
    return True


def reload_config():
    """Recarga la configuración"""
    try:
        manager = get_model_config_manager()
        success = manager.reload_config()
        
        if success:
            logger.info("✅ Configuración recargada")
        else:
            logger.error("❌ No se pudo recargar la configuración")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error recargando configuración: {e}")
        return False
    return True


def main():
    """Función principal del CLI"""
    setup_logging()
    
    parser = argparse.ArgumentParser(
        description="Gestor de modelos de IA para AERY",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  python model_manager.py list                    # Lista todos los modelos
  python model_manager.py current                 # Muestra el modelo actual
  python model_manager.py set openai/gpt-4.1-mini # Establece modelo actual
  python model_manager.py agents                  # Muestra modelos por agente
  python model_manager.py set-agent action_planner openai/gpt-4o
  python model_manager.py add custom/model --name "Mi Modelo" --provider "Custom"
  python model_manager.py remove custom/model
  python model_manager.py summary                 # Resumen de configuración
  python model_manager.py reload                  # Recarga configuración
"""
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Comandos disponibles")
    
    # Comando list
    subparsers.add_parser("list", help="Lista todos los modelos disponibles")
    
    # Comando current
    subparsers.add_parser("current", help="Muestra el modelo actual")
    
    # Comando set
    set_parser = subparsers.add_parser("set", help="Establece el modelo actual")
    set_parser.add_argument("model_id", help="ID del modelo a establecer")
    
    # Comando agents
    subparsers.add_parser("agents", help="Muestra modelos asignados a cada agente")
    
    # Comando set-agent
    set_agent_parser = subparsers.add_parser("set-agent", help="Establece modelo para un agente")
    set_agent_parser.add_argument("agent_type", help="Tipo de agente")
    set_agent_parser.add_argument("model_id", help="ID del modelo")
    
    # Comando add
    add_parser = subparsers.add_parser("add", help="Agrega un nuevo modelo")
    add_parser.add_argument("model_id", help="ID del modelo")
    add_parser.add_argument("--name", help="Nombre del modelo")
    add_parser.add_argument("--provider", help="Proveedor del modelo")
    add_parser.add_argument("--cost", type=float, help="Costo por 1K tokens")
    
    # Comando remove
    remove_parser = subparsers.add_parser("remove", help="Elimina un modelo")
    remove_parser.add_argument("model_id", help="ID del modelo a eliminar")
    
    # Comando summary
    subparsers.add_parser("summary", help="Muestra resumen de configuración")
    
    # Comando reload
    subparsers.add_parser("reload", help="Recarga la configuración")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    success = True
    
    try:
        if args.command == "list":
            success = list_models()
        elif args.command == "current":
            success = show_current()
        elif args.command == "set":
            success = set_current_model(args.model_id)
        elif args.command == "agents":
            success = show_agents()
        elif args.command == "set-agent":
            success = set_agent_model(args.agent_type, args.model_id)
        elif args.command == "add":
            success = add_model(args.model_id, args.name, args.provider, args.cost)
        elif args.command == "remove":
            success = remove_model(args.model_id)
        elif args.command == "summary":
            success = show_summary()
        elif args.command == "reload":
            success = reload_config()
        else:
            logger.error(f"❌ Comando desconocido: {args.command}")
            success = False
    
    except KeyboardInterrupt:
        logger.info("\n👋 Operación cancelada por el usuario")
        success = False
    except Exception as e:
        logger.error(f"❌ Error inesperado: {e}")
        success = False
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()