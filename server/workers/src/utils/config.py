#!/usr/bin/env python3
"""
Configuración para AERY Workers

Este módulo maneja toda la configuración de los workers,
incluyendo variables de entorno, configuración de Redis,
OpenRouter, Playwright y otras dependencias.
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
import logging

logger = logging.getLogger(__name__)

# Importar el gestor de modelos dinámico
try:
    from .model_config import get_model_config_manager, get_current_model, get_model_for_agent
except ImportError:
    # Fallback si no está disponible
    def get_current_model() -> str:
        return "openai/gpt-4.1-mini"
    
    def get_model_for_agent(agent_type: str) -> str:
        return "openai/gpt-4.1-mini"
    
    def get_model_config_manager():
        return None
class WorkerConfig(BaseSettings):
    """Configuración principal de los workers"""
    
    # Identificación del worker
    worker_id: str = Field(default_factory=lambda: f"worker-{os.getpid()}")
    worker_type: str = "automation"
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    redis_db: int = 0
    redis_password: Optional[str] = None
    redis_ssl: bool = False
    
    # OpenRouter (LLM Provider)
    openrouter_api_key: str = ""
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    openrouter_default_model: str = "anthropic/claude-3.5-sonnet"
    openrouter_timeout: int = 120
    
    # Playwright/Browser
    browser_headless: bool = True
    browser_timeout: int = 30000
    browser_viewport_width: int = 1920
    browser_viewport_height: int = 1080
    browser_user_agent: str = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # Concurrencia y límites
    max_concurrent_tasks: int = 3
    task_timeout: int = 300  # 5 minutos
    max_retries: int = 2
    retry_delay: int = 5  # segundos
    
    # Heartbeat y monitoreo
    heartbeat_interval: int = 30  # segundos
    metrics_interval: int = 60  # segundos
    health_check_interval: int = 120  # segundos
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    log_file: Optional[str] = None
    
    # Directorios
    base_dir: Path = Field(default_factory=lambda: Path.cwd())
    screenshots_dir: str = "./artifacts/screenshots"
    videos_dir: str = "./artifacts/videos"
    downloads_dir: str = "./artifacts/downloads"
    logs_dir: str = "./logs"
    
    # Características del worker
    capabilities: list = Field(default_factory=lambda: [
        "web_automation",
        "ai_agents",
        "screenshot_capture",
        "data_extraction",
        "file_upload",
        "file_download"
    ])
    
    # PocketFlow
    pocketflow_max_concurrent: int = 3
    pocketflow_retry_attempts: int = 2
    pocketflow_timeout: int = 120
    
    # Optimizaciones
    enable_fast_mode: bool = True
    enable_prescripts: bool = True
    enable_self_healing: bool = True
    prescript_cache_ttl: int = 86400  # 24 horas
    
    # Seguridad
    allowed_domains: list = Field(default_factory=list)
    blocked_domains: list = Field(default_factory=list)
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_prefix="AERY_",
        case_sensitive=False,
        extra="ignore"  # Ignora campos extra
    )


class RedisConfig(BaseSettings):
    """Configuración específica de Redis"""
    
    url: str = "redis://localhost:6379"
    db: int = 0
    password: Optional[str] = None
    ssl: bool = False
    
    # Pool de conexiones
    max_connections: int = 20
    retry_on_timeout: bool = True
    socket_keepalive: bool = True
    socket_keepalive_options: Dict[str, int] = Field(default_factory=dict)
    
    # Timeouts
    socket_connect_timeout: int = 5
    socket_timeout: int = 5
    
    # Colas
    queue_fast_execution: str = "queue:fast_execution"
    queue_full_processing: str = "queue:full_processing"
    queue_failed: str = "queue:failed"
    queue_retry: str = "queue:retry"
    
    # Prefijos de claves
    task_prefix: str = "task:"
    result_prefix: str = "result:"
    worker_prefix: str = "worker:"
    prescript_prefix: str = "prescript:"
    metrics_prefix: str = "metrics:"
    
    model_config = SettingsConfigDict(
        env_prefix="REDIS_",
        extra="ignore"
    )


class OpenRouterConfig(BaseSettings):
    """Configuración de OpenRouter con soporte para modelos dinámicos"""
    
    api_key: str = ""
    base_url: str = "https://openrouter.ai/api/v1"
    timeout: int = 120
    
    # Modelos por agente (fallback estático)
    models: Dict[str, str] = Field(default_factory=lambda: {
        "instruction_analyzer": "openai/gpt-4.1-mini",
        "action_planner": "openai/gpt-4.1-mini",
        "element_selector": "openai/gpt-4.1-mini",
        "validator": "openai/gpt-4.1-mini",
        "self_healer": "openai/gpt-4.1-mini"
    })
    
    # Configuración por modelo (fallback estático)
    model_configs: Dict[str, Dict[str, Any]] = Field(default_factory=lambda: {
        "anthropic/claude-3.5-sonnet": {
            "temperature": 0.1,
            "max_tokens": 2000,
            "top_p": 0.9
        },
        "openai/gpt-4o": {
            "temperature": 0.1,
            "max_tokens": 1500,
            "top_p": 0.9
        },
        "openai/gpt-4.1-mini": {
            "temperature": 0.1,
            "max_tokens": 1500,
            "top_p": 0.9
        }
    })
    
    # Rate limiting
    requests_per_minute: int = 60
    tokens_per_minute: int = 100000
    
    def get_model_for_agent(self, agent_type: str) -> str:
        """Obtiene el modelo para un agente específico usando el gestor dinámico"""
        try:
            # Intentar usar el gestor dinámico primero
            dynamic_model = get_model_for_agent(agent_type)
            if dynamic_model:
                return dynamic_model
        except Exception as e:
            logger.warning(f"Error obteniendo modelo dinámico para {agent_type}: {e}")
        
        # Fallback a configuración estática
        return self.models.get(agent_type, "openai/gpt-4.1-mini")
    
    def get_current_model(self) -> str:
        """Obtiene el modelo actual usando el gestor dinámico"""
        try:
            # Intentar usar el gestor dinámico primero
            dynamic_model = get_current_model()
            if dynamic_model:
                return dynamic_model
        except Exception as e:
            logger.warning(f"Error obteniendo modelo dinámico actual: {e}")
        
        # Fallback a configuración estática
        return "openai/gpt-4.1-mini"
    
    def get_model_config(self, model_id: Optional[str] = None) -> Dict[str, Any]:
        """Obtiene la configuración de un modelo específico"""
        if model_id is None:
            model_id = self.get_current_model()
        
        try:
            # Intentar usar el gestor dinámico primero
            manager = get_model_config_manager()
            if manager:
                dynamic_config = manager.get_model_config(model_id)
                if dynamic_config:
                    return {
                        "temperature": dynamic_config.get("temperature", 0.1),
                        "max_tokens": dynamic_config.get("max_tokens", 1500),
                        "top_p": dynamic_config.get("top_p", 0.9)
                    }
        except Exception as e:
            logger.warning(f"Error obteniendo configuración dinámica para {model_id}: {e}")
        
        # Fallback a configuración estática
        return self.model_configs.get(model_id, {
            "temperature": 0.1,
            "max_tokens": 1500,
            "top_p": 0.9
        })
    
    model_config = SettingsConfigDict(
        env_prefix="OPENROUTER_",
        extra="ignore",
        protected_namespaces=('settings_',)
    )


class PlaywrightConfig(BaseSettings):
    """Configuración específica de Playwright"""
    
    # Browser
    headless: bool = True
    browser_type: str = "chromium"  # chromium, firefox, webkit
    
    # Viewport
    viewport_width: int = 1920
    viewport_height: int = 1080
    
    # Timeouts
    default_timeout: int = 30000
    navigation_timeout: int = 30000
    
    # User Agent
    user_agent: str = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # Browser args
    browser_args: list = Field(default_factory=lambda: [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding"
    ])
    
    # Recording
    record_video: bool = False
    record_har: bool = False
    
    # Downloads
    accept_downloads: bool = True
    download_path: str = "./artifacts/downloads"
    
    model_config = SettingsConfigDict(
        env_prefix="PLAYWRIGHT_",
        extra="ignore"
    )


# Instancia global de configuración
_config: Optional[WorkerConfig] = None
_redis_config: Optional[RedisConfig] = None
_openrouter_config: Optional[OpenRouterConfig] = None
_playwright_config: Optional[PlaywrightConfig] = None


def get_config() -> WorkerConfig:
    """Obtiene la configuración principal del worker"""
    global _config
    
    if _config is None:
        _config = WorkerConfig()
        
        # Crear directorios necesarios
        _create_directories(_config)
        
        logger.info(f"✅ Configuración cargada para worker {_config.worker_id}")
    
    return _config


def get_redis_config() -> RedisConfig:
    """Obtiene la configuración de Redis"""
    global _redis_config
    
    if _redis_config is None:
        _redis_config = RedisConfig()
        logger.debug("✅ Configuración de Redis cargada")
    
    return _redis_config


def get_openrouter_config() -> OpenRouterConfig:
    """Obtiene la configuración de OpenRouter"""
    global _openrouter_config
    
    if _openrouter_config is None:
        _openrouter_config = OpenRouterConfig()
        
        if not _openrouter_config.api_key:
            logger.warning("⚠️ OpenRouter API key no configurada")
        else:
            logger.debug("✅ Configuración de OpenRouter cargada")
    
    return _openrouter_config


def get_playwright_config() -> PlaywrightConfig:
    """Obtiene la configuración de Playwright"""
    global _playwright_config
    
    if _playwright_config is None:
        _playwright_config = PlaywrightConfig()
        logger.debug("✅ Configuración de Playwright cargada")
    
    return _playwright_config


def _create_directories(config: WorkerConfig):
    """Crea los directorios necesarios"""
    directories = [
        config.screenshots_dir,
        config.videos_dir,
        config.downloads_dir,
        config.logs_dir,
        "./artifacts",
        "./artifacts/hars"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            logger.warning(f"⚠️ No se pudo crear directorio {directory}: {e}")


def reload_config():
    """Recarga toda la configuración"""
    global _config, _redis_config, _openrouter_config, _playwright_config
    
    _config = None
    _redis_config = None
    _openrouter_config = None
    _playwright_config = None
    
    logger.info("🔄 Configuración recargada")


def validate_config() -> bool:
    """Valida que la configuración sea correcta"""
    try:
        config = get_config()
        redis_config = get_redis_config()
        openrouter_config = get_openrouter_config()
        playwright_config = get_playwright_config()
        
        # Validaciones básicas
        if not openrouter_config.api_key:
            logger.error("❌ OpenRouter API key requerida")
            return False
        
        if config.max_concurrent_tasks <= 0:
            logger.error("❌ max_concurrent_tasks debe ser mayor a 0")
            return False
        
        if config.task_timeout <= 0:
            logger.error("❌ task_timeout debe ser mayor a 0")
            return False
        
        logger.info("✅ Configuración validada exitosamente")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error validando configuración: {e}")
        return False


def get_environment_info() -> Dict[str, Any]:
    """Obtiene información del entorno"""
    config = get_config()
    
    return {
        "worker_id": config.worker_id,
        "worker_type": config.worker_type,
        "capabilities": config.capabilities,
        "max_concurrent_tasks": config.max_concurrent_tasks,
        "python_version": os.sys.version,
        "platform": os.name,
        "cwd": str(Path.cwd()),
        "env_vars": {
            key: value for key, value in os.environ.items()
            if key.startswith("AERY_") and "KEY" not in key and "PASSWORD" not in key
        }
    }


if __name__ == "__main__":
    # Test de configuración
    print("🧪 Probando configuración...")
    
    config = get_config()
    print(f"Worker ID: {config.worker_id}")
    print(f"Redis URL: {config.redis_url}")
    print(f"Capabilities: {config.capabilities}")
    
    if validate_config():
        print("✅ Configuración válida")
    else:
        print("❌ Configuración inválida")
    
    env_info = get_environment_info()
    print(f"Environment: {env_info}")