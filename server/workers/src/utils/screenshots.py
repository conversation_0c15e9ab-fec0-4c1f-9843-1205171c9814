"""Gestor de capturas de pantalla para automatización del navegador"""

import os
import base64
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime
from loguru import logger
from playwright.async_api import Page


class ScreenshotManager:
    """Gestor para capturas de pantalla durante la automatización"""
    
    def __init__(self, base_path: str = "/tmp/screenshots"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
    async def take_screenshot(
        self,
        page: Page,
        name: Optional[str] = None,
        full_page: bool = False,
        element_selector: Optional[str] = None
    ) -> Dict[str, Any]:
        """Toma una captura de pantalla"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = name or f"screenshot_{timestamp}.png"
            
            if not filename.endswith('.png'):
                filename += '.png'
            
            filepath = self.base_path / filename
            
            screenshot_options = {
                'path': str(filepath),
                'full_page': full_page
            }
            
            if element_selector:
                # Captura de un elemento específico
                element = await page.locator(element_selector).first
                if await element.is_visible():
                    screenshot_bytes = await element.screenshot()
                else:
                    logger.warning(f"Elemento no visible: {element_selector}")
                    screenshot_bytes = await page.screenshot(**screenshot_options)
            else:
                # Captura de página completa o viewport
                screenshot_bytes = await page.screenshot(**screenshot_options)
            
            # Convertir a base64 para respuesta
            screenshot_base64 = base64.b64encode(screenshot_bytes).decode('utf-8')
            
            return {
                'success': True,
                'filename': filename,
                'filepath': str(filepath),
                'base64': screenshot_base64,
                'size': len(screenshot_bytes),
                'timestamp': timestamp
            }
            
        except Exception as e:
            logger.error(f"Error tomando captura: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S")
            }
    
    async def take_comparison_screenshots(
        self,
        page: Page,
        before_action: str,
        after_action: str
    ) -> Dict[str, Any]:
        """Toma capturas antes y después de una acción"""
        try:
            before_result = await self.take_screenshot(page, f"before_{before_action}")
            
            # Aquí se ejecutaría la acción
            
            after_result = await self.take_screenshot(page, f"after_{after_action}")
            
            return {
                'before': before_result,
                'after': after_result,
                'comparison_available': before_result['success'] and after_result['success']
            }
            
        except Exception as e:
            logger.error(f"Error en capturas de comparación: {e}")
            return {
                'error': str(e),
                'comparison_available': False
            }
    
    def cleanup_old_screenshots(self, days_old: int = 7):
        """Limpia capturas antiguas"""
        try:
            import time
            cutoff_time = time.time() - (days_old * 24 * 60 * 60)
            
            for file_path in self.base_path.glob("*.png"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    logger.info(f"Captura eliminada: {file_path.name}")
                    
        except Exception as e:
            logger.error(f"Error limpiando capturas: {e}")
    
    def get_screenshot_info(self, filename: str) -> Dict[str, Any]:
        """Obtiene información de una captura"""
        try:
            filepath = self.base_path / filename
            
            if not filepath.exists():
                return {'exists': False}
            
            stat = filepath.stat()
            
            return {
                'exists': True,
                'filename': filename,
                'filepath': str(filepath),
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error obteniendo info de captura: {e}")
            return {'exists': False, 'error': str(e)}
    
    async def cleanup(self):
        """Limpia recursos del gestor de capturas"""
        try:
            # Limpiar capturas antiguas
            self.cleanup_old_screenshots()
            logger.info("✅ ScreenshotManager limpiado exitosamente")
        except Exception as e:
            logger.error(f"❌ Error limpiando ScreenshotManager: {e}")