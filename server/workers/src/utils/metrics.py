#!/usr/bin/env python3
"""
Sistema de Métricas para AERY Workers

Este módulo recopila, procesa y envía métricas de rendimiento
de los workers, incluyendo tiempos de ejecución, tasas de éxito,
y estadísticas de uso de recursos.
"""

import asyncio
import json
import time
import psutil
from collections import defaultdict, deque
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

import redis.asyncio as redis
from loguru import logger

from .config import get_config, get_redis_config


@dataclass
class TaskMetric:
    """Métrica de una tarea individual"""
    task_id: str
    worker_id: str
    strategy: str
    execution_time: float
    success: bool
    error_type: Optional[str] = None
    actions_count: int = 0
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()


@dataclass
class WorkerMetric:
    """Métrica del worker"""
    worker_id: str
    cpu_percent: float
    memory_percent: float
    active_tasks: int
    total_tasks_completed: int
    success_rate: float
    avg_execution_time: float
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()


@dataclass
class SystemMetric:
    """Métrica del sistema"""
    total_workers: int
    total_active_tasks: int
    queue_sizes: Dict[str, int]
    system_cpu: float
    system_memory: float
    redis_memory: Optional[float] = None
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.utcnow().isoformat()


class MetricsCollector:
    """Recopilador principal de métricas"""
    
    def __init__(self):
        self.config = get_config()
        self.redis_config = get_redis_config()
        self.redis_client: Optional[redis.Redis] = None
        
        # Buffers locales para métricas
        self.task_metrics: deque = deque(maxlen=1000)
        self.worker_metrics: deque = deque(maxlen=100)
        self.system_metrics: deque = deque(maxlen=100)
        
        # Contadores y estadísticas
        self.task_counters = defaultdict(int)
        self.execution_times = deque(maxlen=100)
        self.error_counts = defaultdict(int)
        self.strategy_stats = defaultdict(lambda: {'count': 0, 'success': 0, 'total_time': 0})
        
        # Estado del worker
        self.start_time = time.time()
        self.last_collection = time.time()
        
        logger.info(f"📊 Inicializando recopilador de métricas para {self.config.worker_id}")
    
    async def initialize(self):
        """Inicializa la conexión a Redis"""
        try:
            self.redis_client = redis.from_url(
                self.redis_config.url,
                decode_responses=True
            )
            await self.redis_client.ping()
            logger.info("✅ Conexión a Redis para métricas establecida")
        except Exception as e:
            logger.error(f"❌ Error conectando a Redis para métricas: {e}")
            raise
    
    def record_task_completion(
        self,
        task_id: str,
        execution_time: float,
        success: bool,
        strategy: str,
        error_type: Optional[str] = None,
        actions_count: int = 0
    ):
        """Registra la finalización de una tarea"""
        
        # Crear métrica
        metric = TaskMetric(
            task_id=task_id,
            worker_id=self.config.worker_id,
            strategy=strategy,
            execution_time=execution_time,
            success=success,
            error_type=error_type,
            actions_count=actions_count
        )
        
        # Añadir a buffer
        self.task_metrics.append(metric)
        
        # Actualizar contadores
        self.task_counters['total'] += 1
        if success:
            self.task_counters['success'] += 1
        else:
            self.task_counters['failed'] += 1
            if error_type:
                self.error_counts[error_type] += 1
        
        # Actualizar estadísticas de estrategia
        strategy_stat = self.strategy_stats[strategy]
        strategy_stat['count'] += 1
        strategy_stat['total_time'] += execution_time
        if success:
            strategy_stat['success'] += 1
        
        # Actualizar tiempos de ejecución
        self.execution_times.append(execution_time)
        
        logger.debug(f"📊 Métrica registrada: {task_id} - {strategy} - {execution_time:.2f}s - {'✅' if success else '❌'}")
    
    def record_worker_state(self, active_tasks: int):
        """Registra el estado actual del worker"""
        
        try:
            # Obtener métricas del sistema
            process = psutil.Process()
            cpu_percent = process.cpu_percent()
            memory_info = process.memory_info()
            memory_percent = (memory_info.rss / psutil.virtual_memory().total) * 100
            
            # Calcular estadísticas
            total_completed = self.task_counters['total']
            success_rate = (
                self.task_counters['success'] / total_completed
                if total_completed > 0 else 0.0
            )
            
            avg_execution_time = (
                sum(self.execution_times) / len(self.execution_times)
                if self.execution_times else 0.0
            )
            
            # Crear métrica
            metric = WorkerMetric(
                worker_id=self.config.worker_id,
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                active_tasks=active_tasks,
                total_tasks_completed=total_completed,
                success_rate=success_rate,
                avg_execution_time=avg_execution_time
            )
            
            # Añadir a buffer
            self.worker_metrics.append(metric)
            
        except Exception as e:
            logger.error(f"❌ Error registrando estado del worker: {e}")
    
    async def collect_system_metrics(self):
        """Recopila métricas del sistema completo"""
        
        try:
            if not self.redis_client:
                return
            
            # Obtener información de workers activos
            active_workers = await self.redis_client.scard("workers:active")
            
            # Obtener tamaños de colas
            queue_sizes = {}
            for queue in ["queue:fast_execution", "queue:full_processing", "queue:failed"]:
                size = await self.redis_client.llen(queue)
                queue_sizes[queue.split(":")[1]] = size
            
            # Contar tareas activas
            active_tasks_pattern = "task:*"
            active_tasks = len(await self.redis_client.keys(active_tasks_pattern))
            
            # Métricas del sistema
            system_cpu = psutil.cpu_percent(interval=1)
            system_memory = psutil.virtual_memory().percent
            
            # Métricas de Redis
            redis_info = await self.redis_client.info("memory")
            redis_memory = redis_info.get("used_memory_human")
            
            # Crear métrica
            metric = SystemMetric(
                total_workers=active_workers,
                total_active_tasks=active_tasks,
                queue_sizes=queue_sizes,
                system_cpu=system_cpu,
                system_memory=system_memory,
                redis_memory=redis_memory
            )
            
            # Añadir a buffer
            self.system_metrics.append(metric)
            
            logger.debug(f"📊 Métricas del sistema: {active_workers} workers, {active_tasks} tareas activas")
            
        except Exception as e:
            logger.error(f"❌ Error recopilando métricas del sistema: {e}")
    
    async def collect_and_send(self):
        """Recopila y envía todas las métricas"""
        
        try:
            current_time = time.time()
            
            # Recopilar métricas del sistema
            await self.collect_system_metrics()
            
            # Enviar métricas a Redis
            await self._send_metrics_to_redis()
            
            # Limpiar buffers antiguos
            self._cleanup_old_metrics()
            
            self.last_collection = current_time
            
        except Exception as e:
            logger.error(f"❌ Error en recopilación de métricas: {e}")
    
    async def _send_metrics_to_redis(self):
        """Envía métricas a Redis"""
        
        if not self.redis_client:
            return
        
        try:
            # Preparar datos para envío
            metrics_data = {
                "worker_id": self.config.worker_id,
                "timestamp": datetime.utcnow().isoformat(),
                "task_metrics": [asdict(m) for m in list(self.task_metrics)[-10:]],  # Últimas 10
                "worker_metrics": [asdict(m) for m in list(self.worker_metrics)[-5:]],  # Últimas 5
                "system_metrics": [asdict(m) for m in list(self.system_metrics)[-3:]],  # Últimas 3
                "summary": self._generate_summary()
            }
            
            # Enviar a Redis con TTL
            key = f"metrics:worker:{self.config.worker_id}"
            await self.redis_client.setex(
                key,
                3600,  # 1 hora TTL
                json.dumps(metrics_data)
            )
            
            # Publicar actualización
            await self.redis_client.publish(
                "metrics_updates",
                json.dumps({
                    "worker_id": self.config.worker_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "summary": metrics_data["summary"]
                })
            )
            
            logger.debug(f"📊 Métricas enviadas a Redis: {key}")
            
        except Exception as e:
            logger.error(f"❌ Error enviando métricas a Redis: {e}")
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Genera un resumen de métricas"""
        
        total_tasks = self.task_counters['total']
        uptime = time.time() - self.start_time
        
        summary = {
            "uptime_seconds": uptime,
            "total_tasks": total_tasks,
            "success_rate": (
                self.task_counters['success'] / total_tasks
                if total_tasks > 0 else 0.0
            ),
            "tasks_per_hour": (
                total_tasks / (uptime / 3600)
                if uptime > 0 else 0.0
            ),
            "avg_execution_time": (
                sum(self.execution_times) / len(self.execution_times)
                if self.execution_times else 0.0
            ),
            "strategy_breakdown": {},
            "top_errors": {},
            "performance_trend": self._calculate_performance_trend()
        }
        
        # Desglose por estrategia
        for strategy, stats in self.strategy_stats.items():
            if stats['count'] > 0:
                summary["strategy_breakdown"][strategy] = {
                    "count": stats['count'],
                    "success_rate": stats['success'] / stats['count'],
                    "avg_time": stats['total_time'] / stats['count']
                }
        
        # Top errores
        sorted_errors = sorted(
            self.error_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )
        summary["top_errors"] = dict(sorted_errors[:5])
        
        return summary
    
    def _calculate_performance_trend(self) -> str:
        """Calcula la tendencia de rendimiento"""
        
        if len(self.execution_times) < 10:
            return "insufficient_data"
        
        # Comparar últimas 10 vs anteriores 10
        recent = list(self.execution_times)[-10:]
        previous = list(self.execution_times)[-20:-10] if len(self.execution_times) >= 20 else []
        
        if not previous:
            return "stable"
        
        recent_avg = sum(recent) / len(recent)
        previous_avg = sum(previous) / len(previous)
        
        change_percent = ((recent_avg - previous_avg) / previous_avg) * 100
        
        if change_percent > 10:
            return "degrading"
        elif change_percent < -10:
            return "improving"
        else:
            return "stable"
    
    def _cleanup_old_metrics(self):
        """Limpia métricas antiguas de los buffers"""
        
        current_time = time.time()
        cutoff_time = current_time - 3600  # 1 hora
        
        # Limpiar métricas de tareas antiguas
        while (self.task_metrics and 
               datetime.fromisoformat(self.task_metrics[0].timestamp.replace('Z', '+00:00')).timestamp() < cutoff_time):
            self.task_metrics.popleft()
        
        # Limpiar métricas de worker antiguas
        while (self.worker_metrics and 
               datetime.fromisoformat(self.worker_metrics[0].timestamp.replace('Z', '+00:00')).timestamp() < cutoff_time):
            self.worker_metrics.popleft()
        
        # Limpiar métricas de sistema antiguas
        while (self.system_metrics and 
               datetime.fromisoformat(self.system_metrics[0].timestamp.replace('Z', '+00:00')).timestamp() < cutoff_time):
            self.system_metrics.popleft()
    
    async def get_worker_stats(self) -> Dict[str, Any]:
        """Obtiene estadísticas actuales del worker"""
        
        return {
            "worker_id": self.config.worker_id,
            "uptime": time.time() - self.start_time,
            "total_tasks": self.task_counters['total'],
            "success_rate": (
                self.task_counters['success'] / self.task_counters['total']
                if self.task_counters['total'] > 0 else 0.0
            ),
            "current_memory_mb": psutil.Process().memory_info().rss / 1024 / 1024,
            "current_cpu_percent": psutil.Process().cpu_percent(),
            "metrics_buffer_sizes": {
                "task_metrics": len(self.task_metrics),
                "worker_metrics": len(self.worker_metrics),
                "system_metrics": len(self.system_metrics)
            },
            "last_collection": self.last_collection
        }
    
    async def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """Genera un reporte de rendimiento"""
        
        try:
            if not self.redis_client:
                return {"error": "Redis no disponible"}
            
            # Obtener métricas históricas
            pattern = f"metrics:worker:{self.config.worker_id}*"
            keys = await self.redis_client.keys(pattern)
            
            historical_data = []
            for key in keys:
                data = await self.redis_client.get(key)
                if data:
                    historical_data.append(json.loads(data))
            
            # Generar reporte
            report = {
                "worker_id": self.config.worker_id,
                "report_period_hours": hours,
                "generated_at": datetime.utcnow().isoformat(),
                "summary": self._generate_summary(),
                "trends": self._analyze_trends(historical_data),
                "recommendations": self._generate_recommendations()
            }
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Error generando reporte: {e}")
            return {"error": str(e)}
    
    def _analyze_trends(self, historical_data: List[Dict]) -> Dict[str, Any]:
        """Analiza tendencias en datos históricos"""
        
        if not historical_data:
            return {"status": "insufficient_data"}
        
        # Extraer métricas clave
        success_rates = []
        execution_times = []
        task_counts = []
        
        for data in historical_data:
            summary = data.get("summary", {})
            success_rates.append(summary.get("success_rate", 0))
            execution_times.append(summary.get("avg_execution_time", 0))
            task_counts.append(summary.get("total_tasks", 0))
        
        return {
            "success_rate_trend": self._calculate_trend(success_rates),
            "execution_time_trend": self._calculate_trend(execution_times),
            "throughput_trend": self._calculate_trend(task_counts),
            "data_points": len(historical_data)
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calcula tendencia de una serie de valores"""
        
        if len(values) < 2:
            return "stable"
        
        # Calcular pendiente simple
        n = len(values)
        x_avg = (n - 1) / 2
        y_avg = sum(values) / n
        
        numerator = sum((i - x_avg) * (values[i] - y_avg) for i in range(n))
        denominator = sum((i - x_avg) ** 2 for i in range(n))
        
        if denominator == 0:
            return "stable"
        
        slope = numerator / denominator
        
        if slope > 0.01:
            return "improving"
        elif slope < -0.01:
            return "degrading"
        else:
            return "stable"
    
    def _generate_recommendations(self) -> List[str]:
        """Genera recomendaciones basadas en métricas"""
        
        recommendations = []
        
        # Analizar tasa de éxito
        success_rate = (
            self.task_counters['success'] / self.task_counters['total']
            if self.task_counters['total'] > 0 else 1.0
        )
        
        if success_rate < 0.8:
            recommendations.append("Tasa de éxito baja - revisar selectores y manejo de errores")
        
        # Analizar tiempos de ejecución
        if self.execution_times:
            avg_time = sum(self.execution_times) / len(self.execution_times)
            if avg_time > 30:
                recommendations.append("Tiempos de ejecución altos - considerar optimizaciones")
        
        # Analizar errores frecuentes
        if self.error_counts:
            most_common_error = max(self.error_counts.items(), key=lambda x: x[1])
            if most_common_error[1] > 5:
                recommendations.append(f"Error frecuente: {most_common_error[0]} - investigar causa raíz")
        
        # Analizar uso de memoria
        try:
            memory_percent = (psutil.Process().memory_info().rss / psutil.virtual_memory().total) * 100
            if memory_percent > 80:
                recommendations.append("Alto uso de memoria - considerar reinicio del worker")
        except:
            pass
        
        if not recommendations:
            recommendations.append("Rendimiento óptimo - continuar monitoreo")
        
        return recommendations
    
    async def cleanup(self):
        """Limpia recursos del recopilador"""
        
        try:
            logger.info("🧹 Limpiando recopilador de métricas")
            
            # Enviar métricas finales
            await self._send_metrics_to_redis()
            
            # Cerrar conexión Redis
            if self.redis_client:
                await self.redis_client.close()
            
            # Limpiar buffers
            self.task_metrics.clear()
            self.worker_metrics.clear()
            self.system_metrics.clear()
            
            logger.info("✅ Recopilador de métricas limpiado")
            
        except Exception as e:
            logger.error(f"❌ Error limpiando métricas: {e}")


class MetricsAggregator:
    """Agregador de métricas de múltiples workers"""
    
    def __init__(self):
        self.redis_config = get_redis_config()
        self.redis_client: Optional[redis.Redis] = None
    
    async def initialize(self):
        """Inicializa el agregador"""
        self.redis_client = redis.from_url(
            self.redis_config.url,
            decode_responses=True
        )
        await self.redis_client.ping()
        logger.info("✅ Agregador de métricas inicializado")
    
    async def get_global_metrics(self) -> Dict[str, Any]:
        """Obtiene métricas globales del sistema"""
        
        try:
            # Obtener métricas de todos los workers
            pattern = "metrics:worker:*"
            keys = await self.redis_client.keys(pattern)
            
            all_metrics = []
            for key in keys:
                data = await self.redis_client.get(key)
                if data:
                    all_metrics.append(json.loads(data))
            
            # Agregar métricas
            aggregated = self._aggregate_metrics(all_metrics)
            
            return aggregated
            
        except Exception as e:
            logger.error(f"❌ Error obteniendo métricas globales: {e}")
            return {}
    
    def _aggregate_metrics(self, metrics_list: List[Dict]) -> Dict[str, Any]:
        """Agrega métricas de múltiples workers"""
        
        if not metrics_list:
            return {"status": "no_data"}
        
        # Inicializar agregados
        total_tasks = 0
        total_success = 0
        total_workers = len(metrics_list)
        execution_times = []
        strategy_stats = defaultdict(lambda: {'count': 0, 'success': 0})
        
        # Procesar cada worker
        for worker_metrics in metrics_list:
            summary = worker_metrics.get("summary", {})
            
            total_tasks += summary.get("total_tasks", 0)
            total_success += summary.get("total_tasks", 0) * summary.get("success_rate", 0)
            
            if summary.get("avg_execution_time"):
                execution_times.append(summary["avg_execution_time"])
            
            # Agregar estadísticas de estrategia
            for strategy, stats in summary.get("strategy_breakdown", {}).items():
                strategy_stats[strategy]['count'] += stats.get('count', 0)
                strategy_stats[strategy]['success'] += stats.get('count', 0) * stats.get('success_rate', 0)
        
        # Calcular agregados
        global_success_rate = total_success / total_tasks if total_tasks > 0 else 0
        global_avg_time = sum(execution_times) / len(execution_times) if execution_times else 0
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "total_workers": total_workers,
            "total_tasks": total_tasks,
            "global_success_rate": global_success_rate,
            "global_avg_execution_time": global_avg_time,
            "strategy_performance": {
                strategy: {
                    "count": stats['count'],
                    "success_rate": stats['success'] / stats['count'] if stats['count'] > 0 else 0
                }
                for strategy, stats in strategy_stats.items()
            },
            "system_health": self._assess_system_health(global_success_rate, total_workers)
        }
    
    def _assess_system_health(self, success_rate: float, worker_count: int) -> str:
        """Evalúa la salud general del sistema"""
        
        if success_rate > 0.95 and worker_count > 0:
            return "excellent"
        elif success_rate > 0.85 and worker_count > 0:
            return "good"
        elif success_rate > 0.7 and worker_count > 0:
            return "fair"
        elif worker_count > 0:
            return "poor"
        else:
            return "no_workers"
    
    async def cleanup(self):
        """Limpia el agregador"""
        if self.redis_client:
            await self.redis_client.close()