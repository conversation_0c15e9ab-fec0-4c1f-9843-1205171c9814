"""Extractor de datos para automatización del navegador"""

import re
import json
from typing import Dict, List, Any, Optional, Union
from loguru import logger
from playwright.async_api import Page, Locator


class DataExtractor:
    """Extractor de datos de páginas web durante la automatización"""
    
    def __init__(self):
        self.extraction_cache = {}
        
    async def extract_text(
        self,
        page: Page,
        selector: str,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Extrae texto de elementos"""
        try:
            options = options or {}
            multiple = options.get('multiple', False)
            clean = options.get('clean', True)
            
            if multiple:
                elements = await page.locator(selector).all()
                texts = []
                
                for element in elements:
                    if await element.is_visible():
                        text = await element.text_content()
                        if clean and text:
                            text = self._clean_text(text)
                        texts.append(text)
                
                return {
                    'success': True,
                    'data': texts,
                    'count': len(texts),
                    'selector': selector
                }
            else:
                element = page.locator(selector).first
                
                if await element.is_visible():
                    text = await element.text_content()
                    if clean and text:
                        text = self._clean_text(text)
                    
                    return {
                        'success': True,
                        'data': text,
                        'selector': selector
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Elemento no visible',
                        'selector': selector
                    }
                    
        except Exception as e:
            logger.error(f"Error extrayendo texto: {e}")
            return {
                'success': False,
                'error': str(e),
                'selector': selector
            }
    
    async def extract_attributes(
        self,
        page: Page,
        selector: str,
        attributes: List[str],
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Extrae atributos de elementos"""
        try:
            options = options or {}
            multiple = options.get('multiple', False)
            
            if multiple:
                elements = await page.locator(selector).all()
                results = []
                
                for element in elements:
                    if await element.is_visible():
                        attrs = {}
                        for attr in attributes:
                            value = await element.get_attribute(attr)
                            attrs[attr] = value
                        results.append(attrs)
                
                return {
                    'success': True,
                    'data': results,
                    'count': len(results),
                    'selector': selector
                }
            else:
                element = page.locator(selector).first
                
                if await element.is_visible():
                    attrs = {}
                    for attr in attributes:
                        value = await element.get_attribute(attr)
                        attrs[attr] = value
                    
                    return {
                        'success': True,
                        'data': attrs,
                        'selector': selector
                    }
                else:
                    return {
                        'success': False,
                        'error': 'Elemento no visible',
                        'selector': selector
                    }
                    
        except Exception as e:
            logger.error(f"Error extrayendo atributos: {e}")
            return {
                'success': False,
                'error': str(e),
                'selector': selector
            }
    
    async def extract_table_data(
        self,
        page: Page,
        table_selector: str,
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Extrae datos de tablas"""
        try:
            options = options or {}
            include_headers = options.get('include_headers', True)
            
            table = page.locator(table_selector).first
            
            if not await table.is_visible():
                return {
                    'success': False,
                    'error': 'Tabla no visible',
                    'selector': table_selector
                }
            
            data = []
            headers = []
            
            # Extraer headers si se solicita
            if include_headers:
                header_rows = await table.locator('thead tr, tr:first-child').all()
                if header_rows:
                    header_cells = await header_rows[0].locator('th, td').all()
                    for cell in header_cells:
                        text = await cell.text_content()
                        headers.append(self._clean_text(text) if text else '')
            
            # Extraer filas de datos
            body_selector = 'tbody tr' if include_headers else 'tr'
            if include_headers:
                body_selector = 'tbody tr, tr:not(:first-child)'
            
            rows = await table.locator(body_selector).all()
            
            for row in rows:
                cells = await row.locator('td, th').all()
                row_data = []
                
                for cell in cells:
                    text = await cell.text_content()
                    row_data.append(self._clean_text(text) if text else '')
                
                if row_data:  # Solo agregar filas no vacías
                    data.append(row_data)
            
            result = {
                'success': True,
                'data': data,
                'rows': len(data),
                'selector': table_selector
            }
            
            if headers:
                result['headers'] = headers
                result['columns'] = len(headers)
            
            return result
            
        except Exception as e:
            logger.error(f"Error extrayendo tabla: {e}")
            return {
                'success': False,
                'error': str(e),
                'selector': table_selector
            }
    
    async def extract_form_data(
        self,
        page: Page,
        form_selector: str
    ) -> Dict[str, Any]:
        """Extrae datos de formularios"""
        try:
            form = page.locator(form_selector).first
            
            if not await form.is_visible():
                return {
                    'success': False,
                    'error': 'Formulario no visible',
                    'selector': form_selector
                }
            
            form_data = {}
            
            # Extraer inputs
            inputs = await form.locator('input, textarea, select').all()
            
            for input_elem in inputs:
                name = await input_elem.get_attribute('name')
                input_type = await input_elem.get_attribute('type')
                value = await input_elem.input_value() if input_type != 'file' else None
                
                if name:
                    form_data[name] = {
                        'type': input_type or 'text',
                        'value': value,
                        'required': await input_elem.get_attribute('required') is not None
                    }
            
            return {
                'success': True,
                'data': form_data,
                'fields': len(form_data),
                'selector': form_selector
            }
            
        except Exception as e:
            logger.error(f"Error extrayendo formulario: {e}")
            return {
                'success': False,
                'error': str(e),
                'selector': form_selector
            }
    
    def _clean_text(self, text: str) -> str:
        """Limpia texto extraído"""
        if not text:
            return ''
        
        # Remover espacios extra y saltos de línea
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remover caracteres de control
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        
        return text
    
    async def extract_custom(
        self,
        page: Page,
        extraction_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extracción personalizada basada en configuración"""
        try:
            results = {}
            
            for key, config in extraction_config.items():
                selector = config.get('selector')
                extract_type = config.get('type', 'text')
                options = config.get('options', {})
                
                if not selector:
                    continue
                
                if extract_type == 'text':
                    result = await self.extract_text(page, selector, options)
                elif extract_type == 'attributes':
                    attributes = config.get('attributes', [])
                    result = await self.extract_attributes(page, selector, attributes, options)
                elif extract_type == 'table':
                    result = await self.extract_table_data(page, selector, options)
                elif extract_type == 'form':
                    result = await self.extract_form_data(page, selector)
                else:
                    result = {'success': False, 'error': f'Tipo no soportado: {extract_type}'}
                
                results[key] = result
            
            return {
                'success': True,
                'data': results,
                'extractions': len(results)
            }
            
        except Exception as e:
            logger.error(f"Error en extracción personalizada: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def cleanup(self):
        """Limpia recursos del extractor de datos"""
        try:
            # Limpiar cache de extracción
            self.extraction_cache.clear()
            logger.info("✅ DataExtractor limpiado exitosamente")
        except Exception as e:
            logger.error(f"❌ Error limpiando DataExtractor: {e}")