"""Motor de selección de elementos para automatización del navegador"""

import re
from typing import Dict, List, Optional, Any
from loguru import logger


class SelectorEngine:
    """Motor para generar y optimizar selectores CSS y XPath"""
    
    def __init__(self):
        self.selector_cache = {}
        
    def generate_selector(self, element_info: Dict[str, Any]) -> str:
        """Genera un selector CSS optimizado para un elemento"""
        try:
            # Priorizar por ID si existe
            if element_info.get('id'):
                return f"#{element_info['id']}"
            
            # Usar clases si están disponibles
            if element_info.get('class'):
                classes = element_info['class'].split()
                if classes:
                    return f".{'.'.join(classes)}"
            
            # Usar atributos específicos
            if element_info.get('name'):
                return f"[name='{element_info['name']}']"
            
            if element_info.get('data-testid'):
                return f"[data-testid='{element_info['data-testid']}']"
            
            # Fallback a tag + texto si está disponible
            tag = element_info.get('tag', 'div')
            text = element_info.get('text', '').strip()
            
            if text and len(text) < 50:
                return f"{tag}:has-text('{text}')"
            
            return tag
            
        except Exception as e:
            logger.error(f"Error generando selector: {e}")
            return "body"
    
    def validate_selector(self, selector: str) -> bool:
        """Valida si un selector CSS es válido"""
        try:
            # Validaciones básicas
            if not selector or not isinstance(selector, str):
                return False
            
            # Verificar caracteres prohibidos
            forbidden_chars = ['<', '>', '{', '}', '(', ')']
            if any(char in selector for char in forbidden_chars):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validando selector: {e}")
            return False
    
    def optimize_selector(self, selector: str) -> str:
        """Optimiza un selector para mejor rendimiento"""
        try:
            if not self.validate_selector(selector):
                return selector
            
            # Remover espacios extra
            selector = re.sub(r'\s+', ' ', selector.strip())
            
            # Optimizaciones básicas
            selector = selector.replace(' > ', '>')
            selector = selector.replace(' + ', '+')
            
            return selector
            
        except Exception as e:
            logger.error(f"Error optimizando selector: {e}")
            return selector
    
    def get_fallback_selectors(self, element_info: Dict[str, Any]) -> List[str]:
        """Genera selectores alternativos para un elemento"""
        selectors = []
        
        try:
            # Selector principal
            main_selector = self.generate_selector(element_info)
            selectors.append(main_selector)
            
            # Selectores alternativos
            tag = element_info.get('tag', 'div')
            
            if element_info.get('type'):
                selectors.append(f"{tag}[type='{element_info['type']}']")  
            
            if element_info.get('placeholder'):
                selectors.append(f"{tag}[placeholder*='{element_info['placeholder']}']")  
            
            if element_info.get('title'):
                selectors.append(f"{tag}[title*='{element_info['title']}']")  
            
            # Selector por posición como último recurso
            selectors.append(f"{tag}:nth-child(1)")
            
        except Exception as e:
            logger.error(f"Error generando selectores alternativos: {e}")
            
        return list(dict.fromkeys(selectors))  # Remover duplicados