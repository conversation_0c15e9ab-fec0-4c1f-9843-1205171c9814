"""
Gestor de modelos dinámico con base de datos PostgreSQL y cache Redis
"""

import json
import redis
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import asyncpg
import asyncio
from loguru import logger
import os
from dataclasses import dataclass, asdict
from decimal import Decimal


@dataclass
class ModelProvider:
    id: str
    name: str
    display_name: str
    api_base_url: str
    auth_type: str
    config: Dict[str, Any]
    enabled: bool
    rate_limit_rpm: int
    rate_limit_tpm: int


@dataclass
class ModelCategory:
    id: str
    name: str
    display_name: str
    description: str
    icon: str
    enabled: bool


@dataclass
class AIModel:
    id: str
    model_id: str
    name: str
    provider_id: str
    category_id: str
    context_length: int
    max_tokens: Optional[int]
    supports_streaming: bool
    supports_function_calling: bool
    supports_vision: bool
    input_cost_per_1m: Decimal
    output_cost_per_1m: Decimal
    default_temperature: Decimal
    default_top_p: Decimal
    default_top_k: Optional[int]
    default_max_tokens: Optional[int]
    enabled: bool
    features: List[str]
    metadata: Dict[str, Any]


@dataclass
class AgentConfiguration:
    id: str
    agent_type: str
    display_name: str
    description: str
    primary_model_id: str
    fallback_model_id: Optional[str]
    temperature: Optional[Decimal]
    max_tokens: Optional[int]
    top_p: Optional[Decimal]
    system_prompt: Optional[str]
    enabled: bool


class DatabaseModelManager:
    """Gestor de modelos con PostgreSQL y Redis cache"""
    
    def __init__(self):
        self.pg_pool: Optional[asyncpg.Pool] = None
        self.redis_client: Optional[redis.Redis] = None
        self.cache_ttl = 300  # 5 minutos
        self._init_connections()
    
    def _init_connections(self):
        """Inicializa las conexiones a PostgreSQL y Redis"""
        try:
            # Redis
            redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            
            logger.info("✅ Conexión a Redis establecida")
        except Exception as e:
            logger.error(f"❌ Error conectando a Redis: {e}")
    
    async def init_database(self):
        """Inicializa el pool de conexiones de PostgreSQL"""
        try:
            database_url = os.getenv(
                'DATABASE_URL', 
                'postgresql://aery_user:aery_password@localhost:5432/aery_db'
            )
            
            self.pg_pool = await asyncpg.create_pool(
                database_url,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            logger.info("✅ Pool de conexiones PostgreSQL establecido")
        except Exception as e:
            logger.error(f"❌ Error estableciendo pool PostgreSQL: {e}")
            raise
    
    async def close(self):
        """Cierra las conexiones"""
        if self.pg_pool:
            await self.pg_pool.close()
        if self.redis_client:
            self.redis_client.close()
    
    # === CACHE UTILITIES ===
    
    def _get_cache_key(self, prefix: str, identifier: str) -> str:
        """Genera una clave de cache"""
        return f"aery:models:{prefix}:{identifier}"
    
    def _set_cache(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """Guarda datos en cache"""
        try:
            if self.redis_client:
                cache_data = json.dumps(data, default=str)
                return self.redis_client.setex(
                    key, 
                    ttl or self.cache_ttl, 
                    cache_data
                )
        except Exception as e:
            logger.warning(f"Error guardando en cache {key}: {e}")
        return False
    
    def _get_cache(self, key: str) -> Optional[Any]:
        """Obtiene datos del cache"""
        try:
            if self.redis_client:
                cached = self.redis_client.get(key)
                if cached:
                    return json.loads(cached)
        except Exception as e:
            logger.warning(f"Error obteniendo cache {key}: {e}")
        return None
    
    def _invalidate_cache_pattern(self, pattern: str):
        """Invalida múltiples claves de cache por patrón"""
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    self.redis_client.delete(*keys)
        except Exception as e:
            logger.warning(f"Error invalidando cache {pattern}: {e}")
    
    # === MODEL PROVIDERS ===
    
    async def get_providers(self, enabled_only: bool = False) -> List[ModelProvider]:
        """Obtiene todos los proveedores"""
        cache_key = self._get_cache_key("providers", "all" if not enabled_only else "enabled")
        
        # Intentar obtener del cache
        cached = self._get_cache(cache_key)
        if cached:
            return [ModelProvider(**p) for p in cached]
        
        # Obtener de la base de datos
        query = """
            SELECT id, name, display_name, api_base_url, auth_type, config, 
                   enabled, rate_limit_rpm, rate_limit_tpm
            FROM model_providers 
        """
        if enabled_only:
            query += " WHERE enabled = TRUE"
        query += " ORDER BY display_name"
        
        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch(query)
            
            providers = []
            for row in rows:
                providers.append(ModelProvider(
                    id=str(row['id']),
                    name=row['name'],
                    display_name=row['display_name'],
                    api_base_url=row['api_base_url'],
                    auth_type=row['auth_type'],
                    config=row['config'],
                    enabled=row['enabled'],
                    rate_limit_rpm=row['rate_limit_rpm'],
                    rate_limit_tpm=row['rate_limit_tpm']
                ))
            
            # Guardar en cache
            cache_data = [asdict(p) for p in providers]
            self._set_cache(cache_key, cache_data)
            
            return providers
    
    async def get_provider_by_name(self, name: str) -> Optional[ModelProvider]:
        """Obtiene un proveedor por nombre"""
        cache_key = self._get_cache_key("provider", name)
        
        # Intentar cache
        cached = self._get_cache(cache_key)
        if cached:
            return ModelProvider(**cached)
        
        query = """
            SELECT id, name, display_name, api_base_url, auth_type, config,
                   enabled, rate_limit_rpm, rate_limit_tpm
            FROM model_providers WHERE name = $1
        """
        
        async with self.pg_pool.acquire() as conn:
            row = await conn.fetchrow(query, name)
            if not row:
                return None
            
            provider = ModelProvider(
                id=str(row['id']),
                name=row['name'],
                display_name=row['display_name'],
                api_base_url=row['api_base_url'],
                auth_type=row['auth_type'],
                config=row['config'],
                enabled=row['enabled'],
                rate_limit_rpm=row['rate_limit_rpm'],
                rate_limit_tpm=row['rate_limit_tpm']
            )
            
            # Cache
            self._set_cache(cache_key, asdict(provider))
            
            return provider
    
    # === MODEL CATEGORIES ===
    
    async def get_categories(self, enabled_only: bool = False) -> List[ModelCategory]:
        """Obtiene todas las categorías"""
        cache_key = self._get_cache_key("categories", "all" if not enabled_only else "enabled")
        
        cached = self._get_cache(cache_key)
        if cached:
            return [ModelCategory(**c) for c in cached]
        
        query = """
            SELECT id, name, display_name, description, icon, enabled
            FROM model_categories
        """
        if enabled_only:
            query += " WHERE enabled = TRUE"
        query += " ORDER BY display_name"
        
        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch(query)
            
            categories = []
            for row in rows:
                categories.append(ModelCategory(
                    id=str(row['id']),
                    name=row['name'],
                    display_name=row['display_name'],
                    description=row['description'],
                    icon=row['icon'],
                    enabled=row['enabled']
                ))
            
            # Cache
            cache_data = [asdict(c) for c in categories]
            self._set_cache(cache_key, cache_data)
            
            return categories
    
    # === AI MODELS ===
    
    async def get_models(
        self, 
        provider_name: Optional[str] = None,
        category_name: Optional[str] = None,
        enabled_only: bool = False
    ) -> List[AIModel]:
        """Obtiene modelos con filtros opcionales"""
        
        # Generar clave de cache única para los filtros
        cache_parts = ["models"]
        if provider_name:
            cache_parts.append(f"provider_{provider_name}")
        if category_name:
            cache_parts.append(f"category_{category_name}")
        if enabled_only:
            cache_parts.append("enabled")
        
        cache_key = self._get_cache_key("_".join(cache_parts), "all")
        
        cached = self._get_cache(cache_key)
        if cached:
            return [AIModel(**m) for m in cached]
        
        # Query base
        query = """
            SELECT m.id, m.model_id, m.name, m.provider_id, m.category_id,
                   m.context_length, m.max_tokens, m.supports_streaming,
                   m.supports_function_calling, m.supports_vision,
                   m.input_cost_per_1m, m.output_cost_per_1m,
                   m.default_temperature, m.default_top_p, m.default_top_k,
                   m.default_max_tokens, m.enabled, m.features, m.metadata
            FROM ai_models m
            LEFT JOIN model_providers p ON m.provider_id = p.id
            LEFT JOIN model_categories c ON m.category_id = c.id
            WHERE 1=1
        """
        
        params = []
        param_count = 0
        
        if provider_name:
            param_count += 1
            query += f" AND p.name = ${param_count}"
            params.append(provider_name)
        
        if category_name:
            param_count += 1
            query += f" AND c.name = ${param_count}"
            params.append(category_name)
        
        if enabled_only:
            query += " AND m.enabled = TRUE"
        
        query += " ORDER BY m.name"
        
        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            
            models = []
            for row in rows:
                models.append(AIModel(
                    id=str(row['id']),
                    model_id=row['model_id'],
                    name=row['name'],
                    provider_id=str(row['provider_id']),
                    category_id=str(row['category_id']),
                    context_length=row['context_length'],
                    max_tokens=row['max_tokens'],
                    supports_streaming=row['supports_streaming'],
                    supports_function_calling=row['supports_function_calling'],
                    supports_vision=row['supports_vision'],
                    input_cost_per_1m=row['input_cost_per_1m'],
                    output_cost_per_1m=row['output_cost_per_1m'],
                    default_temperature=row['default_temperature'],
                    default_top_p=row['default_top_p'],
                    default_top_k=row['default_top_k'],
                    default_max_tokens=row['default_max_tokens'],
                    enabled=row['enabled'],
                    features=row['features'] or [],
                    metadata=row['metadata'] or {}
                ))
            
            # Cache
            cache_data = [asdict(m) for m in models]
            self._set_cache(cache_key, cache_data)
            
            return models
    
    async def get_model_by_id(self, model_id: str) -> Optional[AIModel]:
        """Obtiene un modelo por su ID"""
        cache_key = self._get_cache_key("model", model_id)
        
        cached = self._get_cache(cache_key)
        if cached:
            return AIModel(**cached)
        
        query = """
            SELECT id, model_id, name, provider_id, category_id,
                   context_length, max_tokens, supports_streaming,
                   supports_function_calling, supports_vision,
                   input_cost_per_1m, output_cost_per_1m,
                   default_temperature, default_top_p, default_top_k,
                   default_max_tokens, enabled, features, metadata
            FROM ai_models WHERE model_id = $1
        """
        
        async with self.pg_pool.acquire() as conn:
            row = await conn.fetchrow(query, model_id)
            if not row:
                return None
            
            model = AIModel(
                id=str(row['id']),
                model_id=row['model_id'],
                name=row['name'],
                provider_id=str(row['provider_id']),
                category_id=str(row['category_id']),
                context_length=row['context_length'],
                max_tokens=row['max_tokens'],
                supports_streaming=row['supports_streaming'],
                supports_function_calling=row['supports_function_calling'],
                supports_vision=row['supports_vision'],
                input_cost_per_1m=row['input_cost_per_1m'],
                output_cost_per_1m=row['output_cost_per_1m'],
                default_temperature=row['default_temperature'],
                default_top_p=row['default_top_p'],
                default_top_k=row['default_top_k'],
                default_max_tokens=row['default_max_tokens'],
                enabled=row['enabled'],
                features=row['features'] or [],
                metadata=row['metadata'] or {}
            )
            
            # Cache
            self._set_cache(cache_key, asdict(model))
            
            return model
    
    async def create_model(self, model_data: Dict[str, Any]) -> AIModel:
        """Crea un nuevo modelo"""
        query = """
            INSERT INTO ai_models (
                model_id, name, provider_id, category_id, context_length,
                max_tokens, supports_streaming, supports_function_calling,
                supports_vision, input_cost_per_1m, output_cost_per_1m,
                default_temperature, default_top_p, default_top_k,
                default_max_tokens, enabled, features, metadata
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
            ) RETURNING id
        """
        
        async with self.pg_pool.acquire() as conn:
            row = await conn.fetchrow(
                query,
                model_data['model_id'],
                model_data['name'],
                model_data['provider_id'],
                model_data['category_id'],
                model_data['context_length'],
                model_data.get('max_tokens'),
                model_data.get('supports_streaming', True),
                model_data.get('supports_function_calling', False),
                model_data.get('supports_vision', False),
                model_data['input_cost_per_1m'],
                model_data['output_cost_per_1m'],
                model_data.get('default_temperature', 0.1),
                model_data.get('default_top_p', 0.9),
                model_data.get('default_top_k'),
                model_data.get('default_max_tokens'),
                model_data.get('enabled', True),
                json.dumps(model_data.get('features', [])),
                json.dumps(model_data.get('metadata', {}))
            )
        
        # Invalidar cache
        self._invalidate_cache_pattern("aery:models:*")
        
        # Obtener el modelo creado
        return await self.get_model_by_id(model_data['model_id'])
    
    async def update_model(self, model_id: str, model_data: Dict[str, Any]) -> Optional[AIModel]:
        """Actualiza un modelo existente"""
        
        # Construir query dinámicamente
        set_clauses = []
        params = []
        param_count = 0
        
        for field, value in model_data.items():
            if field in ['name', 'context_length', 'max_tokens', 'supports_streaming',
                        'supports_function_calling', 'supports_vision', 'input_cost_per_1m',
                        'output_cost_per_1m', 'default_temperature', 'default_top_p',
                        'default_top_k', 'default_max_tokens', 'enabled']:
                param_count += 1
                set_clauses.append(f"{field} = ${param_count}")
                params.append(value)
            elif field in ['features', 'metadata']:
                param_count += 1
                set_clauses.append(f"{field} = ${param_count}")
                params.append(json.dumps(value))
        
        if not set_clauses:
            return await self.get_model_by_id(model_id)
        
        param_count += 1
        query = f"""
            UPDATE ai_models 
            SET {', '.join(set_clauses)}, updated_at = NOW()
            WHERE model_id = ${param_count}
        """
        params.append(model_id)
        
        async with self.pg_pool.acquire() as conn:
            await conn.execute(query, *params)
        
        # Invalidar cache
        self._invalidate_cache_pattern("aery:models:*")
        
        return await self.get_model_by_id(model_id)
    
    async def delete_model(self, model_id: str) -> bool:
        """Elimina un modelo"""
        query = "DELETE FROM ai_models WHERE model_id = $1"
        
        async with self.pg_pool.acquire() as conn:
            result = await conn.execute(query, model_id)
        
        # Invalidar cache
        self._invalidate_cache_pattern("aery:models:*")
        
        return "DELETE 1" in result
    
    # === AGENT CONFIGURATIONS ===
    
    async def get_agent_configurations(self) -> List[AgentConfiguration]:
        """Obtiene todas las configuraciones de agentes"""
        cache_key = self._get_cache_key("agent_configs", "all")
        
        cached = self._get_cache(cache_key)
        if cached:
            return [AgentConfiguration(**a) for a in cached]
        
        query = """
            SELECT id, agent_type, display_name, description, primary_model_id,
                   fallback_model_id, temperature, max_tokens, top_p, 
                   system_prompt, enabled
            FROM agent_configurations
            ORDER BY agent_type
        """
        
        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch(query)
            
            configs = []
            for row in rows:
                configs.append(AgentConfiguration(
                    id=str(row['id']),
                    agent_type=row['agent_type'],
                    display_name=row['display_name'],
                    description=row['description'],
                    primary_model_id=str(row['primary_model_id']),
                    fallback_model_id=str(row['fallback_model_id']) if row['fallback_model_id'] else None,
                    temperature=row['temperature'],
                    max_tokens=row['max_tokens'],
                    top_p=row['top_p'],
                    system_prompt=row['system_prompt'],
                    enabled=row['enabled']
                ))
            
            # Cache
            cache_data = [asdict(c) for c in configs]
            self._set_cache(cache_key, cache_data)
            
            return configs
    
    async def get_agent_configuration(self, agent_type: str) -> Optional[AgentConfiguration]:
        """Obtiene la configuración de un agente específico"""
        cache_key = self._get_cache_key("agent_config", agent_type)
        
        cached = self._get_cache(cache_key)
        if cached:
            return AgentConfiguration(**cached)
        
        query = """
            SELECT id, agent_type, display_name, description, primary_model_id,
                   fallback_model_id, temperature, max_tokens, top_p, 
                   system_prompt, enabled
            FROM agent_configurations WHERE agent_type = $1
        """
        
        async with self.pg_pool.acquire() as conn:
            row = await conn.fetchrow(query, agent_type)
            if not row:
                return None
            
            config = AgentConfiguration(
                id=str(row['id']),
                agent_type=row['agent_type'],
                display_name=row['display_name'],
                description=row['description'],
                primary_model_id=str(row['primary_model_id']),
                fallback_model_id=str(row['fallback_model_id']) if row['fallback_model_id'] else None,
                temperature=row['temperature'],
                max_tokens=row['max_tokens'],
                top_p=row['top_p'],
                system_prompt=row['system_prompt'],
                enabled=row['enabled']
            )
            
            # Cache
            self._set_cache(cache_key, asdict(config))
            
            return config
    
    async def update_agent_configuration(
        self, 
        agent_type: str, 
        config_data: Dict[str, Any]
    ) -> Optional[AgentConfiguration]:
        """Actualiza la configuración de un agente"""
        
        # Verificar si existe
        existing = await self.get_agent_configuration(agent_type)
        if not existing:
            return None
        
        # Construir query dinámicamente
        set_clauses = []
        params = []
        param_count = 0
        
        for field, value in config_data.items():
            if field in ['primary_model_id', 'fallback_model_id', 'temperature', 
                        'max_tokens', 'top_p', 'system_prompt', 'enabled']:
                param_count += 1
                set_clauses.append(f"{field} = ${param_count}")
                params.append(value)
        
        if not set_clauses:
            return existing
        
        param_count += 1
        query = f"""
            UPDATE agent_configurations 
            SET {', '.join(set_clauses)}, updated_at = NOW()
            WHERE agent_type = ${param_count}
        """
        params.append(agent_type)
        
        async with self.pg_pool.acquire() as conn:
            await conn.execute(query, *params)
        
        # Invalidar cache
        self._invalidate_cache_pattern("aery:models:agent_*")
        
        return await self.get_agent_configuration(agent_type)


# Instancia global
_db_model_manager: Optional[DatabaseModelManager] = None


async def get_db_model_manager() -> DatabaseModelManager:
    """Obtiene la instancia global del gestor de modelos de BD"""
    global _db_model_manager
    if _db_model_manager is None:
        _db_model_manager = DatabaseModelManager()
        await _db_model_manager.init_database()
    return _db_model_manager
