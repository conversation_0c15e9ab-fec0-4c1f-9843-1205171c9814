"""Gestor de configuración de modelos de IA"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ModelConfigManager:
    """Gestor para la configuración dinámica de modelos de IA"""
    
    def __init__(self, config_file: str = "model_config.json"):
        self.config_file = Path(config_file)
        # Usar directorio relativo al proyecto
        current_dir = Path(__file__).parent.parent.parent
        self.config_dir = current_dir / "config"
        self.config_dir.mkdir(exist_ok=True)
        self.config_path = self.config_dir / self.config_file
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Carga la configuración desde el archivo JSON"""
        default_config = {
            "current_model": "openai/gpt-4.1-mini",
            "available_models": {
                "openai/gpt-4.1-mini": {
                    "name": "GPT-4.1 Mini",
                    "provider": "OpenAI",
                    "cost_per_1k_tokens": 0.0001,
                    "max_tokens": 4096,
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "recommended_for": ["general", "fast_responses"]
                },
                "openai/gpt-4o": {
                    "name": "GPT-4 Omni",
                    "provider": "OpenAI",
                    "cost_per_1k_tokens": 0.005,
                    "max_tokens": 4096,
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "recommended_for": ["complex_tasks", "reasoning"]
                },
                "anthropic/claude-3.5-sonnet": {
                    "name": "Claude 3.5 Sonnet",
                    "provider": "Anthropic",
                    "cost_per_1k_tokens": 0.003,
                    "max_tokens": 4096,
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "recommended_for": ["analysis", "writing", "coding"]
                },
                "anthropic/claude-3-haiku": {
                    "name": "Claude 3 Haiku",
                    "provider": "Anthropic",
                    "cost_per_1k_tokens": 0.00025,
                    "max_tokens": 4096,
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "recommended_for": ["fast_responses", "simple_tasks"]
                }
            },
            "agent_specific_models": {
                "instruction_analyzer": "openai/gpt-4.1-mini",
                "action_planner": "openai/gpt-4.1-mini",
                "element_selector": "openai/gpt-4.1-mini",
                "validator": "openai/gpt-4.1-mini",
                "self_healer": "openai/gpt-4.1-mini"
            },
            "fallback_models": [
                "openai/gpt-4.1-mini",
                "anthropic/claude-3-haiku"
            ],
            "last_updated": "2024-01-01T00:00:00Z"
        }
        
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge con configuración por defecto para nuevos campos
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            else:
                # Crear archivo de configuración por defecto
                self._save_config(default_config)
                logger.info(f"✅ Archivo de configuración creado: {self.config_path}")
                return default_config
                
        except Exception as e:
            logger.error(f"❌ Error cargando configuración: {e}")
            return default_config
    
    def _save_config(self, config: Dict[str, Any]) -> bool:
        """Guarda la configuración en el archivo JSON"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"❌ Error guardando configuración: {e}")
            return False
    
    def get_current_model(self) -> str:
        """Obtiene el modelo actual"""
        return self._config.get("current_model", "openai/gpt-4.1-mini")
    
    def set_current_model(self, model_id: str) -> bool:
        """Establece el modelo actual"""
        if model_id in self._config.get("available_models", {}):
            self._config["current_model"] = model_id
            self._config["last_updated"] = self._get_current_timestamp()
            success = self._save_config(self._config)
            if success:
                logger.info(f"✅ Modelo actualizado a: {model_id}")
            return success
        else:
            logger.error(f"❌ Modelo no disponible: {model_id}")
            return False
    
    def get_model_for_agent(self, agent_type: str) -> str:
        """Obtiene el modelo específico para un tipo de agente"""
        agent_models = self._config.get("agent_specific_models", {})
        return agent_models.get(agent_type, self.get_current_model())
    
    def set_model_for_agent(self, agent_type: str, model_id: str) -> bool:
        """Establece el modelo para un tipo específico de agente"""
        if model_id in self._config.get("available_models", {}):
            if "agent_specific_models" not in self._config:
                self._config["agent_specific_models"] = {}
            
            self._config["agent_specific_models"][agent_type] = model_id
            self._config["last_updated"] = self._get_current_timestamp()
            success = self._save_config(self._config)
            if success:
                logger.info(f"✅ Modelo para {agent_type} actualizado a: {model_id}")
            return success
        else:
            logger.error(f"❌ Modelo no disponible: {model_id}")
            return False
    
    def get_model_config(self, model_id: Optional[str] = None) -> Dict[str, Any]:
        """Obtiene la configuración de un modelo específico"""
        if model_id is None:
            model_id = self.get_current_model()
        
        available_models = self._config.get("available_models", {})
        return available_models.get(model_id, {})
    
    def get_available_models(self) -> Dict[str, Any]:
        """Obtiene todos los modelos disponibles"""
        return self._config.get("available_models", {})
    
    def add_model(self, model_id: str, model_config: Dict[str, Any]) -> bool:
        """Agrega un nuevo modelo a la configuración"""
        if "available_models" not in self._config:
            self._config["available_models"] = {}
        
        self._config["available_models"][model_id] = model_config
        self._config["last_updated"] = self._get_current_timestamp()
        success = self._save_config(self._config)
        if success:
            logger.info(f"✅ Modelo agregado: {model_id}")
        return success
    
    def remove_model(self, model_id: str) -> bool:
        """Elimina un modelo de la configuración"""
        if model_id in self._config.get("available_models", {}):
            del self._config["available_models"][model_id]
            
            # Si era el modelo actual, cambiar al primero disponible
            if self._config.get("current_model") == model_id:
                available = list(self._config.get("available_models", {}).keys())
                if available:
                    self._config["current_model"] = available[0]
                    logger.info(f"🔄 Modelo actual cambiado a: {available[0]}")
            
            self._config["last_updated"] = self._get_current_timestamp()
            success = self._save_config(self._config)
            if success:
                logger.info(f"✅ Modelo eliminado: {model_id}")
            return success
        else:
            logger.error(f"❌ Modelo no encontrado: {model_id}")
            return False
    
    def get_fallback_models(self) -> list:
        """Obtiene la lista de modelos de respaldo"""
        return self._config.get("fallback_models", ["openai/gpt-4.1-mini"])
    
    def reload_config(self) -> bool:
        """Recarga la configuración desde el archivo"""
        try:
            self._config = self._load_config()
            logger.info("✅ Configuración recargada")
            return True
        except Exception as e:
            logger.error(f"❌ Error recargando configuración: {e}")
            return False
    
    def _get_current_timestamp(self) -> str:
        """Obtiene el timestamp actual en formato ISO"""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Obtiene un resumen de la configuración actual"""
        return {
            "current_model": self.get_current_model(),
            "total_models": len(self._config.get("available_models", {})),
            "agent_specific_models": self._config.get("agent_specific_models", {}),
            "fallback_models": self.get_fallback_models(),
            "last_updated": self._config.get("last_updated"),
            "config_file": str(self.config_path)
        }


# Instancia global del gestor
_model_config_manager: Optional[ModelConfigManager] = None


def get_model_config_manager() -> ModelConfigManager:
    """Obtiene la instancia global del gestor de configuración de modelos"""
    global _model_config_manager
    if _model_config_manager is None:
        _model_config_manager = ModelConfigManager()
    return _model_config_manager


def get_current_model() -> str:
    """Función de conveniencia para obtener el modelo actual"""
    return get_model_config_manager().get_current_model()


def get_model_for_agent(agent_type: str) -> str:
    """Función de conveniencia para obtener el modelo de un agente"""
    return get_model_config_manager().get_model_for_agent(agent_type)


def get_model_config(model_id: Optional[str] = None) -> Dict[str, Any]:
    """Función de conveniencia para obtener la configuración de un modelo"""
    return get_model_config_manager().get_model_config(model_id)