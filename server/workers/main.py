#!/usr/bin/env python3
"""
AERY Worker - Procesador de tareas de automatización con IA

Este worker se conecta a Redis para recibir tareas de automatización,
las procesa usando agentes IA con PocketFlow y ejecuta las acciones
con Playwright.
"""

import asyncio
import json
import logging
import os
import signal
import sys
import time
from typing import Dict, Any, Optional
from datetime import datetime

import redis.asyncio as redis
from fastapi import FastAPI
from loguru import logger
from pydantic_settings import BaseSettings, SettingsConfigDict

from src.agents.coordinator import AgentCoordinator
from src.automation.browser_engine import BrowserEngine
from src.utils.config import WorkerConfig
from src.utils.metrics import MetricsCollector


class WorkerSettings(BaseSettings):
    """Configuración del worker"""
    worker_id: str = f"worker-{os.getpid()}"
    redis_url: str = "redis://localhost:6379"
    database_url: str = "postgresql://localhost:5432/aery"
    openrouter_api_key: str = ""
    max_concurrent_tasks: int = 3
    task_timeout: int = 300  # 5 minutos
    heartbeat_interval: int = 30  # segundos
    log_level: str = "INFO"
    
    model_config = SettingsConfigDict(
        env_file=".env",
        extra="ignore"  # Ignore extra environment variables
    )


class AERYWorker:
    """Worker principal para procesar tareas de automatización"""
    
    def __init__(self):
        self.settings = WorkerSettings()
        self.redis_client: Optional[redis.Redis] = None
        self.agent_coordinator = AgentCoordinator()
        self.browser_engine = BrowserEngine()
        self.metrics = MetricsCollector()
        self.running = False
        self.current_tasks: Dict[str, asyncio.Task] = {}
        
        # Configurar logging
        logger.remove()
        logger.add(
            sys.stdout,
            level=self.settings.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        
        logger.info(f"🚀 Iniciando AERY Worker {self.settings.worker_id}")
    
    async def start(self):
        """Inicia el worker"""
        try:
            # Conectar a Redis
            await self._connect_redis()
            
            # Inicializar componentes
            await self.agent_coordinator.initialize()
            await self.browser_engine.initialize()
            
            # Registrar worker
            await self._register_worker()
            
            # Configurar signal handlers
            self._setup_signal_handlers()
            
            self.running = True
            logger.info(f"✅ Worker {self.settings.worker_id} iniciado exitosamente")
            
            # Iniciar loops principales
            await asyncio.gather(
                self._task_processor_loop(),
                self._heartbeat_loop(),
                self._metrics_loop(),
            )
            
        except Exception as e:
            logger.error(f"❌ Error iniciando worker: {e}")
            await self.shutdown()
            raise
    
    async def _connect_redis(self):
        """Conecta a Redis"""
        try:
            self.redis_client = redis.from_url(
                self.settings.redis_url,
                decode_responses=True,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
            )
            
            # Verificar conexión
            await self.redis_client.ping()
            logger.info("✅ Conectado a Redis")
            
        except Exception as e:
            logger.error(f"❌ Error conectando a Redis: {e}")
            raise
    
    async def _register_worker(self):
        """Registra el worker en Redis"""
        worker_info = {
            "worker_id": self.settings.worker_id,
            "status": "active",
            "max_concurrent_tasks": self.settings.max_concurrent_tasks,
            "current_tasks": 0,
            "started_at": datetime.utcnow().isoformat(),
            "last_heartbeat": datetime.utcnow().isoformat(),
            "capabilities": [
                "web_automation",
                "ai_agents",
                "screenshot_capture",
                "data_extraction",
            ],
        }
        
        await self.redis_client.setex(
            f"worker:{self.settings.worker_id}",
            self.settings.heartbeat_interval * 3,  # TTL 3x heartbeat
            json.dumps(worker_info)
        )
        
        await self.redis_client.sadd("workers:active", self.settings.worker_id)
        logger.info(f"📝 Worker {self.settings.worker_id} registrado")
    
    async def _task_processor_loop(self):
        """Loop principal para procesar tareas"""
        logger.info("🔄 Iniciando loop de procesamiento de tareas")
        
        while self.running:
            try:
                # Verificar capacidad
                if len(self.current_tasks) >= self.settings.max_concurrent_tasks:
                    await asyncio.sleep(1)
                    continue
                
                # Buscar tareas en las colas (prioridad: fast -> full)
                task_data = await self._get_next_task()
                
                if task_data:
                    # Procesar tarea en background
                    task_coroutine = self._process_task(task_data)
                    task = asyncio.create_task(task_coroutine)
                    self.current_tasks[task_data["id"]] = task
                    
                    # Cleanup cuando termine
                    task.add_done_callback(
                        lambda t, tid=task_data["id"]: self.current_tasks.pop(tid, None)
                    )
                else:
                    # No hay tareas, esperar un poco
                    await asyncio.sleep(2)
                    
            except Exception as e:
                logger.error(f"❌ Error en loop de procesamiento: {e}")
                await asyncio.sleep(5)
    
    async def _get_next_task(self) -> Optional[Dict[str, Any]]:
        """Obtiene la siguiente tarea de las colas"""
        try:
            logger.info("🔍 Buscando tareas en las colas...")
            # Intentar cola de prioridad primero, luego cola normal
            result = await self.redis_client.brpop(
                ["aery:queue:priority", "aery:queue:tasks"],
                timeout=5
            )
            
            if result:
                queue_name, task_json = result
                task_data = json.loads(task_json)
                
                logger.info(f"📥 Nueva tarea recibida: {task_data['id']} desde {queue_name}")
                return task_data
            else:
                logger.info("⏰ Timeout esperando tareas")
                
        except Exception as e:
            logger.error(f"❌ Error obteniendo tarea: {e}")
        
        return None
    
    async def _process_task(self, task_data: Dict[str, Any]):
        """Procesa una tarea individual"""
        task_id = task_data["id"]
        start_time = time.time()
        
        try:
            logger.info(f"🔄 Procesando tarea {task_id}")
            
            # Obtener detalles completos de la tarea
            task_details = await self.redis_client.get(f"task:{task_id}")
            if not task_details:
                raise Exception(f"Tarea {task_id} no encontrada")
            
            task = json.loads(task_details)
            
            # Actualizar estado a "processing"
            await self._update_task_status(task_id, "processing", 0)
            
            # Determinar estrategia de procesamiento
            if task["strategy"] == "optimized_prescript":
                result = await self._execute_prescript(task)
            else:
                result = await self._execute_full_ai_processing(task)
            
            # Guardar resultado
            await self._save_task_result(task_id, result)
            
            # Actualizar estado final
            final_status = "completed" if result["success"] else "failed"
            await self._update_task_status(task_id, final_status, 100)
            
            execution_time = time.time() - start_time
            logger.info(f"✅ Tarea {task_id} completada en {execution_time:.2f}s")
            
            # Métricas
            self.metrics.record_task_completion(
                task_id, 
                execution_time, 
                result["success"],
                task["strategy"]
            )
            
        except Exception as e:
            logger.error(f"❌ Error procesando tarea {task_id}: {e}")
            
            # Guardar error
            error_result = {
                "taskId": task_id,
                "success": False,
                "error": str(e),
                "executionTime": time.time() - start_time,
                "strategy": task_data.get("strategy", "unknown"),
            }
            
            await self._save_task_result(task_id, error_result)
            await self._update_task_status(task_id, "failed", 100)
    
    async def _execute_prescript(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta una tarea usando pre-script optimizado"""
        logger.info(f"⚡ Ejecutando pre-script para tarea {task['taskId']}")
        
        try:
            # Ejecutar acciones del pre-script directamente
            result = await self.browser_engine.execute_prescript(
                task["preScript"],
                task["options"]
            )
            
            if result["success"]:
                # Actualizar métricas del pre-script
                await self._update_prescript_metrics(
                    task["instructionHash"],
                    True,
                    result["executionTime"]
                )
                
                return {
                    "taskId": task["taskId"],
                    "success": True,
                    "data": result["data"],
                    "artifacts": result["artifacts"],
                    "executionTime": result["executionTime"],
                    "strategy": "optimized_prescript",
                    "costSaving": 90,  # 90% ahorro vs procesamiento completo
                }
            else:
                # Pre-script falló, activar self-healing
                logger.warning(f"🔧 Pre-script falló, activando self-healing para {task['taskId']}")
                return await self._execute_self_healing(task, result["error"])
                
        except Exception as e:
            logger.error(f"❌ Error ejecutando pre-script: {e}")
            # Fallback a procesamiento completo
            return await self._execute_full_ai_processing(task)
    
    async def _execute_full_ai_processing(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Ejecuta procesamiento completo con agentes IA"""
        logger.info(f"🧠 Ejecutando procesamiento completo IA para tarea {task['taskId']}")
        
        try:
            # Coordinar agentes IA
            await self._update_task_status(task["taskId"], "processing", 20)
            
            agent_result = await self.agent_coordinator.coordinate_agents(
                task_data=task,
                workflow_type="sequential"
            )
            
            await self._update_task_status(task["taskId"], "processing", 60)
            
            # Ejecutar acciones generadas
            execution_result = await self.browser_engine.execute_actions(
                agent_result["actions"],
                task["options"]
            )
            
            await self._update_task_status(task["taskId"], "processing", 90)
            
            if execution_result["success"]:
                # Generar pre-script para futuras ejecuciones
                await self._generate_prescript(
                    task["instructionHash"],
                    agent_result["actions"],
                    execution_result
                )
                
                return {
                    "taskId": task["taskId"],
                    "success": True,
                    "data": {
                        "actions": agent_result["actions"],
                        "selectors": agent_result.get("selectors", {}),
                        "timing": execution_result.get("timing", {}),
                        **execution_result["data"]
                    },
                    "artifacts": execution_result["artifacts"],
                    "executionTime": execution_result["executionTime"],
                    "strategy": "full_llm_processing",
                    "preScriptGenerated": True,
                    "agentConfidence": agent_result.get("confidence", 0.0),
                }
            else:
                return {
                    "taskId": task["taskId"],
                    "success": False,
                    "error": execution_result["error"],
                    "executionTime": execution_result["executionTime"],
                    "strategy": "full_llm_processing",
                }
                
        except Exception as e:
            logger.error(f"❌ Error en procesamiento IA: {e}")
            return {
                "taskId": task["taskId"],
                "success": False,
                "error": str(e),
                "executionTime": 0,
                "strategy": "full_llm_processing",
            }
    
    async def _execute_self_healing(self, task: Dict[str, Any], error: str) -> Dict[str, Any]:
        """Ejecuta self-healing cuando falla un pre-script"""
        logger.info(f"🔧 Ejecutando self-healing para tarea {task['taskId']}")
        
        try:
            # Analizar error con IA
            # healing_result = await self.agent_coordinator.analyze_and_heal(
            #     task["preScript"],
            #     error,
            #     task["instruction"]
            # )  # Comentado temporalmente
            healing_result = {'success': False, 'error': 'Auto-sanación temporalmente deshabilitada'}
            
            if healing_result["success"]:
                # Ejecutar acciones corregidas
                execution_result = await self.browser_engine.execute_actions(
                    healing_result["corrected_actions"],
                    task["options"]
                )
                
                if execution_result["success"]:
                    # Actualizar pre-script con correcciones
                    await self._update_prescript(
                        task["instructionHash"],
                        healing_result["corrected_actions"]
                    )
                    
                    return {
                        "taskId": task["taskId"],
                        "success": True,
                        "data": execution_result["data"],
                        "artifacts": execution_result["artifacts"],
                        "executionTime": execution_result["executionTime"],
                        "strategy": "self_healing",
                        "selfHealingApplied": True,
                        "healingReason": healing_result["reason"],
                    }
            
            # Self-healing falló, usar procesamiento completo
            logger.warning(f"🔄 Self-healing falló, usando procesamiento completo para {task['taskId']}")
            return await self._execute_full_ai_processing(task)
            
        except Exception as e:
            logger.error(f"❌ Error en self-healing: {e}")
            return await self._execute_full_ai_processing(task)
    
    async def _update_task_status(self, task_id: str, status: str, progress: int):
        """Actualiza el estado de una tarea"""
        try:
            # Publicar actualización
            update = {
                "taskId": task_id,
                "status": status,
                "progress": progress,
                "workerId": self.settings.worker_id,
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            await self.redis_client.publish(
                "task_status_updates",
                json.dumps(update)
            )
            
        except Exception as e:
            logger.error(f"❌ Error actualizando estado de tarea {task_id}: {e}")
    
    async def _save_task_result(self, task_id: str, result: Dict[str, Any]):
        """Guarda el resultado de una tarea"""
        try:
            await self.redis_client.setex(
                f"result:{task_id}",
                86400,  # 24 horas
                json.dumps(result)
            )
            
        except Exception as e:
            logger.error(f"❌ Error guardando resultado de tarea {task_id}: {e}")
    
    async def _heartbeat_loop(self):
        """Loop de heartbeat para mantener el worker registrado"""
        while self.running:
            try:
                await self._send_heartbeat()
                await asyncio.sleep(self.settings.heartbeat_interval)
            except Exception as e:
                logger.error(f"❌ Error en heartbeat: {e}")
                await asyncio.sleep(5)
    
    async def _send_heartbeat(self):
        """Envía heartbeat al registro de workers"""
        try:
            worker_info = {
                "worker_id": self.settings.worker_id,
                "status": "active",
                "current_tasks": len(self.current_tasks),
                "last_heartbeat": datetime.utcnow().isoformat(),
                "uptime": time.time() - self.start_time if hasattr(self, 'start_time') else 0,
            }
            
            await self.redis_client.setex(
                f"worker:{self.settings.worker_id}",
                self.settings.heartbeat_interval * 3,
                json.dumps(worker_info)
            )
            
        except Exception as e:
            logger.error(f"❌ Error enviando heartbeat: {e}")
    
    async def _metrics_loop(self):
        """Loop para recopilar y enviar métricas"""
        while self.running:
            try:
                await self.metrics.collect_and_send()
                await asyncio.sleep(60)  # Cada minuto
            except Exception as e:
                logger.error(f"❌ Error en métricas: {e}")
                await asyncio.sleep(30)
    
    def _setup_signal_handlers(self):
        """Configura manejadores de señales para shutdown graceful"""
        def signal_handler(signum, frame):
            logger.info(f"🛑 Señal {signum} recibida, iniciando shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def shutdown(self):
        """Shutdown graceful del worker"""
        logger.info(f"🛑 Iniciando shutdown del worker {self.settings.worker_id}")
        
        self.running = False
        
        # Esperar que terminen las tareas actuales
        if self.current_tasks:
            logger.info(f"⏳ Esperando {len(self.current_tasks)} tareas en progreso...")
            await asyncio.gather(*self.current_tasks.values(), return_exceptions=True)
        
        # Desregistrar worker
        if self.redis_client:
            try:
                await self.redis_client.srem("workers:active", self.settings.worker_id)
                await self.redis_client.delete(f"worker:{self.settings.worker_id}")
                await self.redis_client.close()
            except Exception as e:
                logger.error(f"❌ Error desregistrando worker: {e}")
        
        # Cerrar componentes
        await self.browser_engine.cleanup()
        # await self.agent_coordinator.cleanup()  # Comentado temporalmente
        
        logger.info(f"✅ Worker {self.settings.worker_id} cerrado exitosamente")


async def main():
    """Función principal"""
    worker = AERYWorker()
    worker.start_time = time.time()
    
    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("🛑 Interrupción por teclado")
    except Exception as e:
        logger.error(f"❌ Error fatal: {e}")
        sys.exit(1)
    finally:
        await worker.shutdown()


if __name__ == "__main__":
    asyncio.run(main())