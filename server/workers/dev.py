#!/usr/bin/env python3
"""
Development server with hot reload for AERY Workers
Uses watchdog to monitor file changes and restart the worker automatically.
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
except ImportError:
    print("❌ Error: watchdog not installed. Installing...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "watchdog"])
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler


class WorkerRestartHandler(FileSystemEventHandler):
    """Handler para reiniciar el worker cuando cambian los archivos"""
    
    def __init__(self, script_path: str):
        self.script_path = script_path
        self.process = None
        self.last_restart = 0
        self.restart_worker()
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        # Solo reiniciar para archivos Python y evitar restart muy frecuentes
        if event.src_path.endswith('.py'):
            current_time = time.time()
            if current_time - self.last_restart < 2:  # Evitar restart muy frecuentes
                return
            
            print(f"🔄 Detected change in {event.src_path}")
            self.restart_worker()
    
    def restart_worker(self):
        """Reinicia el proceso del worker"""
        if self.process:
            print("🛑 Stopping worker...")
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print("⚠️  Force killing worker...")
                self.process.kill()
                self.process.wait()
        
        print("🚀 Starting worker...")
        self.process = subprocess.Popen([
            sys.executable, self.script_path
        ])
        self.last_restart = time.time()
    
    def stop(self):
        """Detiene el proceso del worker"""
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.process.kill()
                self.process.wait()


def signal_handler(sig, frame):
    """Handler para señales del sistema"""
    print("\n🛑 Received interrupt signal...")
    sys.exit(0)


def main():
    """Función principal del servidor de desarrollo"""
    script_path = "main.py"
    
    if not Path(script_path).exists():
        print(f"❌ Error: {script_path} not found")
        return 1
    
    # Configurar handlers de señales
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🔥 AERY Workers Development Server")
    print("=" * 40)
    print(f"👀 Watching for changes in: {os.getcwd()}")
    print("🔄 Hot reload enabled")
    print("🛑 Press Ctrl+C to stop")
    print()
    
    # Configurar el observer
    event_handler = WorkerRestartHandler(script_path)
    observer = Observer()
    
    # Monitorear el directorio actual, excluyendo cache
    observer.schedule(event_handler, ".", recursive=True)
    observer.start()
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        pass
    finally:
        print("\n🛑 Shutting down...")
        observer.stop()
        event_handler.stop()
        observer.join()
        print("✅ Development server stopped")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
