#!/usr/bin/env python3
"""
Demostración del Sistema de Gestión de Modelos de IA

Este script demuestra las funcionalidades implementadas:
1. Configuración dinámica de modelos OpenRouter
2. Gestión de modelos por agente
3. Manejo de errores mejorado en browser engine
4. Reintentos automáticos
"""

import sys
import os
sys.path.append('.')

def main():
    print("🚀 DEMOSTRACIÓN DEL SISTEMA DE GESTIÓN DE MODELOS")
    print("=" * 50)
    
    try:
        # 1. Probar el sistema de configuración de modelos
        print("\n1️⃣ SISTEMA DE CONFIGURACIÓN DE MODELOS")
        print("-" * 40)
        
        from src.utils.model_config import get_model_config_manager
        manager = get_model_config_manager()
        
        print(f"✅ Modelo actual: {manager.get_current_model()}")
        print(f"📚 Modelos disponibles: {', '.join(manager.get_available_models())}")
        
        # 2. Probar la configuración dinámica
        print("\n2️⃣ CONFIGURACIÓN DINÁMICA")
        print("-" * 40)
        
        from src.utils.config import get_openrouter_config
        config = get_openrouter_config()
        
        print(f"🔑 API Key configurada: {'✅' if config.api_key else '❌'}")
        print(f"🌐 URL Base: {config.base_url}")
        print(f"🤖 Modelo para action_planner: {config.get_model_for_agent('action_planner')}")
        
        # 3. Mostrar mejoras en browser engine
        print("\n3️⃣ MEJORAS EN BROWSER ENGINE")
        print("-" * 40)
        print("✅ Manejo de errores granular implementado")
        print("✅ Sistema de reintentos automáticos")
        print("✅ Clasificación de errores por tipo")
        print("✅ Recomendaciones de reintento")
        
        # 4. Mostrar APIs disponibles
        print("\n4️⃣ APIS Y HERRAMIENTAS DISPONIBLES")
        print("-" * 40)
        print("🔧 API de gestión de modelos: src/api/model_management.py")
        print("💻 CLI de gestión: src/cli/model_manager.py")
        print("⚙️ Configuración dinámica: src/utils/model_config.py")
        
        print("\n🎉 SISTEMA IMPLEMENTADO EXITOSAMENTE")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error en la demostración: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())