{"name": "aery-workers", "version": "1.0.0", "description": "AERY Browser Automation API - Python Workers", "scripts": {"start": "python main.py", "dev": "python -m watchdog main.py", "test": "python -m pytest tests/", "lint": "python -m flake8 src/", "format": "python -m black src/", "type-check": "python -m mypy src/", "install": "pip install -r requirements.txt", "install-dev": "pip install -r requirements-dev.txt"}, "keywords": ["automation", "browser", "ai", "workers", "playwright", "python"], "author": "AERY Team", "license": "MIT"}