#!/usr/bin/env python3

import sys
import os
sys.path.append('.')

try:
    from src.utils.model_config import get_model_config_manager
    
    print("🔄 Inicializando sistema de gestión de modelos...")
    manager = get_model_config_manager()
    
    print("✅ Sistema de modelos funcionando correctamente")
    print(f"📋 Modelo actual: {manager.get_current_model()}")
    
    # Mostrar modelos disponibles
    available_models = manager.get_available_models()
    print(f"📚 Modelos disponibles: {', '.join(available_models)}")
    
    # Mostrar configuración de agentes
    config = manager.get_config()
    if 'agent_models' in config:
        print("🤖 Modelos por agente:")
        for agent, model in config['agent_models'].items():
            print(f"  - {agent}: {model}")
    else:
        print("🤖 No hay configuración específica de agentes")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)