# AERY Project Makefile
# Optimized for OrbStack development workflow

.PHONY: help dev-setup dev dev-orbstack prod build clean test lint fmt check install logs status health gateway workers db-reset db-migrate db-seed db-apply-new-scripts dev-logs orbstack docker monitor

# Default target
help: ## Show this help message
	@echo "AERY Browser Automation API - Development Commands"
	@echo "=================================================="
	@echo ""
	@echo "Quick Setup:"
	@echo "  dev-setup        Complete development environment setup (one-time)"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev              Start development environment (standard Docker)"
	@echo "  dev-orbstack     Start development environment (OrbStack optimized + hot reload)"
	@echo "  prod             Start production environment"
	@echo ""
	@echo "Build Commands:"
	@echo "  build            Build all Docker images"
	@echo "  build-gateway    Build gateway image only"
	@echo "  build-workers    Build workers image only"
	@echo ""
	@echo "Management Commands:"
	@echo "  install          Install dependencies for all services"
	@echo "  test             Run tests for all services"
	@echo "  lint             Run linting for all services"
	@echo "  fmt              Format code for all services"
	@echo "  check            Run all checks (fmt, lint, test)"
	@echo ""
	@echo "Database Commands:"
	@echo "  db-status            Check database status and tables"
	@echo "  db-reset             Reset database (OrbStack)"
	@echo "  db-reset-dev         Reset database (Development)"
	@echo "  db-migrate           Run database migrations manually"
	@echo "  db-seed              Seed development data"
	@echo "  db-init              Initialize database from scratch"
	@echo "  db-apply-new-scripts Apply only new SQL scripts (models & agent configs)"
	@echo ""
	@echo "Monitoring Commands:"
	@echo "  logs             Show logs for all services"
	@echo "  logs-gateway     Show gateway logs"
	@echo "  logs-workers     Show workers logs"
	@echo "  status           Show service status"
	@echo "  health           Check service health"
	@echo ""
	@echo "Cleanup Commands:"
	@echo "  stop             Stop all services (auto-detect environment)"
	@echo "  restart          Restart all services (auto-detect environment)"
	@echo "  clean            Stop and remove containers, networks, volumes"
	@echo "  reset            Complete reset (clean + remove images)"
	@echo ""
	@echo "OrbStack Specific Commands:"
	@echo "  orbstack                Alias for dev-orbstack"
	@echo "  orbstack-logs          Show OrbStack logs"
	@echo "  orbstack-restart       Restart OrbStack environment"
	@echo "  orbstack-stop          Stop OrbStack environment"
	@echo "  orbstack-rebuild       Rebuild and restart OrbStack"
	@echo ""

# Quick Development Setup
dev-setup: ## Complete development environment setup (one-time)
	@echo "🚀 Setting up AERY development environment..."
	@echo ""
	@echo "📋 Checking prerequisites..."
	@command -v docker >/dev/null 2>&1 || { echo "❌ Docker is required but not installed. Please install Docker first."; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "❌ Docker Compose is required but not installed. Please install Docker Compose first."; exit 1; }
	@echo "✅ Docker and Docker Compose are installed"
	@echo ""
	@echo "📁 Setting up environment file..."
	@if [ ! -f .env ]; then \
		if [ -f .env.example ]; then \
			cp .env.example .env; \
			echo "✅ Created .env from .env.example"; \
		else \
			echo "⚠️  .env.example not found, creating basic .env"; \
			echo "# AERY Environment Configuration" > .env; \
			echo "NODE_ENV=development" >> .env; \
			echo "DATABASE_URL=postgresql://aery:aery_dev_password@localhost:5432/aery" >> .env; \
			echo "REDIS_URL=redis://localhost:6379" >> .env; \
			echo "JWT_SECRET=your-super-secret-jwt-key-change-in-production" >> .env; \
		fi; \
	else \
		echo "✅ .env file already exists"; \
	fi
	@echo ""
	@echo "🐳 Building Docker images..."
	@$(MAKE) build
	@echo ""
	@echo "🗄️  Setting up database..."
	@docker-compose -f docker-compose.dev.yml up -d postgres redis
	@echo "⏳ Waiting for database to be ready..."
	@sleep 15
	@echo "✅ Checking database initialization..."
	@$(MAKE) db-status 2>/dev/null || echo "⚠️  Database may need manual initialization"
	@echo ""
	@echo "🎉 Setup complete! You can now run:"
	@echo "   make dev          # Start all services"
	@echo "   make dev-orbstack # Start with OrbStack optimization (macOS)"
	@echo ""
	@echo "🌐 Services will be available at:"
	@echo "   API Gateway:   http://localhost:8000"
	@echo "   PgAdmin:       http://localhost:8080"
	@echo "   Redis UI:      http://localhost:8081"
	@echo "   MailHog:       http://localhost:8025"

# Development Environment
dev: ## Start development environment (standard Docker)
	@echo "🚀 Starting AERY development environment..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Development environment started!"
	@echo "🔗 Gateway: http://localhost:8000"
	@echo "🗄️  Database UI: http://localhost:8080"
	@echo "📧 Redis UI: http://localhost:8081"
	@echo "📬 Mail UI: http://localhost:8025"

dev-orbstack: ## Start development environment (OrbStack optimized)
	@echo "🚀 Starting AERY development environment (OrbStack optimized)..."
	docker-compose -f docker-compose.orbstack.yml up -d
	@echo "⏳ Waiting for database to be ready..."
	@sleep 15
	@echo "🗄️  Running database migrations..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/01-init.sql 2>/dev/null || echo "⚠️  Migrations may have already been applied"
	@echo "🌱 Seeding development data..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/02-dev-seed.sql 2>/dev/null || echo "⚠️  Seed data may have already been applied"
	@echo "🔧 Setting up models schema..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/03-models-schema.sql 2>/dev/null || echo "⚠️  Models schema may have already been applied"
	@echo "📊 Seeding models data..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/04-models-seed.sql 2>/dev/null || echo "⚠️  Models seed data may have already been applied"
	@echo "🤖 Seeding agent configurations..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/05-agent-configs-seed.sql 2>/dev/null || echo "⚠️  Agent configs may have already been applied"
	@echo "✅ OrbStack development environment started!"
	@echo "🔗 Gateway: http://gateway.orb.local:8000"
	@echo "🗄️  Database UI: http://pgadmin.orb.local:8080"
	@echo "📧 Redis UI: http://redis-ui.orb.local:8081"
	@echo "📬 Mail UI: http://mail.orb.local:8025"
	@echo ""
	@echo "🔄 Hot reload is enabled for both gateway and workers"
	@echo "📝 Edit files and see changes automatically applied"

dev-orbstack-logs: ## Show logs for OrbStack development environment
	docker-compose -f docker-compose.orbstack.yml logs -f

dev-orbstack-restart: ## Restart OrbStack development environment
	@echo "🔄 Restarting OrbStack development environment..."
	docker-compose -f docker-compose.orbstack.yml restart
	@echo "✅ OrbStack environment restarted!"

dev-orbstack-stop: ## Stop OrbStack development environment
	@echo "🛑 Stopping OrbStack development environment..."
	docker-compose -f docker-compose.orbstack.yml down
	@echo "✅ OrbStack environment stopped!"

dev-orbstack-rebuild: ## Rebuild and restart OrbStack environment
	@echo "🔨 Rebuilding and restarting OrbStack environment..."
	docker-compose -f docker-compose.orbstack.yml down
	docker-compose -f docker-compose.orbstack.yml build --no-cache
	docker-compose -f docker-compose.orbstack.yml up -d
	@echo "✅ OrbStack environment rebuilt and restarted!"

dev-orbstack-debug: ## Start OrbStack with debug services (simple mode)
	@echo "🐛 Starting OrbStack debug environment..."
	docker-compose -f docker-compose.orbstack.yml --profile debug up -d
	@echo "✅ Debug services started!"
	@echo "🔗 Gateway: http://gateway.orb.local:8000 (normal) or http://gateway-simple.orb.local:8001 (debug)"
	@echo "🤖 Workers: Port 5678 (normal) or 5679 (debug)"

prod: ## Start production environment
	@echo "🚀 Starting AERY production environment..."
	docker-compose up -d
	@echo "✅ Production environment started!"
	@echo "🌐 Application: http://localhost"
	@echo "📊 Monitoring: http://localhost:3001"

# Build Commands
build: ## Build all Docker images
	@echo "🔨 Building all Docker images..."
	docker-compose build --parallel
	@echo "✅ All images built successfully!"

build-gateway: ## Build gateway image only
	@echo "🔨 Building gateway image..."
	docker-compose build gateway
	@echo "✅ Gateway image built!"

build-workers: ## Build workers image only
	@echo "🔨 Building workers image..."
	docker-compose build workers
	@echo "✅ Workers image built!"

# Development Commands
install: ## Install dependencies for all services
	@echo "📦 Installing dependencies..."
	@echo "Installing gateway dependencies..."
	cd server/gateway && deno cache main.ts
	@echo "Installing worker dependencies..."
	cd server/workers && pip install -r requirements.txt
	@echo "✅ All dependencies installed!"

test: ## Run tests for all services
	@echo "🧪 Running tests..."
	@echo "Testing gateway..."
	cd server/gateway && deno test -A
	@echo "Testing workers..."
	cd server/workers && python -m pytest
	@echo "✅ All tests passed!"

lint: ## Run linting for all services
	@echo "🔍 Running linting..."
	@echo "Linting gateway..."
	cd server/gateway && deno lint
	@echo "Linting workers..."
	cd server/workers && python -m flake8 src/
	@echo "✅ All linting passed!"

fmt: ## Format code for all services
	@echo "✨ Formatting code..."
	@echo "Formatting gateway..."
	cd server/gateway && deno fmt
	@echo "Formatting workers..."
	cd server/workers && python -m black src/
	@echo "✅ All code formatted!"

check: fmt lint test ## Run all checks (fmt, lint, test)
	@echo "✅ All checks completed successfully!"

# Monitoring Commands
logs: ## Show logs for all services
	docker-compose logs -f

logs-gateway: ## Show gateway logs
	docker-compose logs -f gateway

logs-workers: ## Show workers logs
	docker-compose logs -f workers

status: ## Show service status
	@echo "📊 Service Status:"
	@echo "=================="
	docker-compose ps

health: ## Check service health
	@./scripts/health-check.sh

health-quick: ## Quick health check
	@echo "🏥 Quick Health Check:"
	@echo "====================="
	@echo "Client Health:"
	@curl -f http://localhost:3000 > /dev/null 2>&1 && echo "✅ Client: Healthy" || echo "❌ Client: Unhealthy"
	@echo "Gateway Health:"
	@curl -f http://localhost:8000/health > /dev/null 2>&1 && echo "✅ Gateway: Healthy" || echo "❌ Gateway: Unhealthy"
	@echo "Database Health:"
	@docker-compose exec postgres pg_isready -U aery > /dev/null 2>&1 && echo "✅ Database: Healthy" || echo "❌ Database: Unhealthy"
	@echo "Redis Health:"
	@docker-compose exec redis redis-cli ping > /dev/null 2>&1 && echo "✅ Redis: Healthy" || echo "❌ Redis: Unhealthy"

# Individual Service Commands
client: ## Start only client service
	@echo "🌐 Starting client service..."
	docker-compose -f docker-compose.dev.yml up -d client
	@echo "✅ Client started at http://localhost:3000"

gateway: ## Start only gateway service
	@echo "🔗 Starting gateway service..."
	docker-compose -f docker-compose.dev.yml up -d postgres redis gateway
	@echo "✅ Gateway started at http://localhost:8000"

workers: ## Start only workers service
	@echo "🤖 Starting workers service..."
	docker-compose -f docker-compose.dev.yml up -d postgres redis workers
	@echo "✅ Workers started"

# Database Commands
db-reset: ## Reset database (OrbStack)
	@echo "🗄️  Resetting database..."
	docker-compose -f docker-compose.orbstack.yml stop postgres
	docker-compose -f docker-compose.orbstack.yml rm -f postgres
	docker volume rm aery_postgres_orbstack_data 2>/dev/null || true
	docker-compose -f docker-compose.orbstack.yml up -d postgres
	@echo "✅ Database reset complete"

db-reset-dev: ## Reset database (Development)
	@echo "🗄️  Resetting development database..."
	docker-compose -f docker-compose.dev.yml stop postgres
	docker-compose -f docker-compose.dev.yml rm -f postgres
	docker volume rm aery_postgres_data 2>/dev/null || true
	docker-compose -f docker-compose.dev.yml up -d postgres
	@echo "✅ Development database reset complete"

db-migrate: ## Run database migrations manually (OrbStack)
	@echo "🗄️  Running database migrations..."
	docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/01-init.sql
	@echo "🔧 Setting up models schema..."
	docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/03-models-schema.sql
	@echo "✅ Migrations complete"

db-seed: ## Seed development data (OrbStack)
	@echo "🌱 Seeding development data..."
	docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/02-dev-seed.sql
	@echo "📊 Seeding models data..."
	docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/04-models-seed.sql
	@echo "🤖 Seeding agent configurations..."
	docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/05-agent-configs-seed.sql
	@echo "✅ Development data seeded"

db-init: ## Initialize database from scratch (OrbStack)
	@echo "🔧 Initializing database from scratch..."
	@$(MAKE) db-reset
	@echo "⏳ Waiting for database to be ready..."
	@sleep 15
	@$(MAKE) db-migrate
	@$(MAKE) db-seed
	@echo "✅ Database initialization complete"

db-apply-new-scripts: ## Apply only the new SQL scripts (models and agent configs)
	@echo "🔧 Applying new SQL scripts..."
	@echo "📊 Setting up models schema..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/03-models-schema.sql 2>/dev/null || echo "⚠️  Models schema may have already been applied"
	@echo "📊 Seeding models data..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/04-models-seed.sql 2>/dev/null || echo "⚠️  Models seed data may have already been applied"
	@echo "🤖 Seeding agent configurations..."
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -f /docker-entrypoint-initdb.d/05-agent-configs-seed.sql 2>/dev/null || echo "⚠️  Agent configs may have already been applied"
	@echo "✅ New scripts applied successfully!"

db-status: ## Check database status and tables (OrbStack)
	@echo "📊 Database Status:"
	@echo "=================="
	@docker-compose -f docker-compose.orbstack.yml exec postgres pg_isready -U aery -d aery && echo "✅ Database: Healthy" || echo "❌ Database: Unhealthy"
	@echo ""
	@echo "📋 Tables:"
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -c "\\dt" 2>/dev/null || echo "❌ No tables found or connection failed"
	@echo ""
	@echo "👥 Users:"
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -c "SELECT email, plan FROM users;" 2>/dev/null || echo "❌ Cannot fetch users"
	@echo ""
	@echo "🤖 Models:"
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -c "SELECT COUNT(*) as total_models FROM models;" 2>/dev/null || echo "❌ Cannot fetch models"
	@echo ""
	@echo "⚙️  Agent Configs:"
	@docker-compose -f docker-compose.orbstack.yml exec postgres psql -U aery -d aery -c "SELECT COUNT(*) as total_agent_configs FROM agent_configs;" 2>/dev/null || echo "❌ Cannot fetch agent configs"

# Quick Development Shortcuts
dev-logs: ## View logs from all development services
	docker-compose -f docker-compose.dev.yml logs -f

orbstack: ## Alias for dev-orbstack
	@$(MAKE) dev-orbstack

orbstack-logs: ## Alias for dev-orbstack-logs
	@$(MAKE) dev-orbstack-logs

orbstack-restart: ## Alias for dev-orbstack-restart
	@$(MAKE) dev-orbstack-restart

orbstack-stop: ## Alias for dev-orbstack-stop
	@$(MAKE) dev-orbstack-stop

orbstack-rebuild: ## Alias for dev-orbstack-rebuild
	@$(MAKE) dev-orbstack-rebuild

orbstack-debug: ## Alias for dev-orbstack-debug
	@$(MAKE) dev-orbstack-debug

docker: ## Alias for dev
	@$(MAKE) dev

# Monitoring
monitor: ## Start monitoring stack (Prometheus + Grafana)
	@echo "📊 Starting monitoring stack..."
	docker-compose -f docker-compose.dev.yml up -d prometheus grafana
	@echo "✅ Monitoring available at:"
	@echo "   Prometheus: http://localhost:9090"
	@echo "   Grafana:    http://localhost:3001"

# Cleanup Commands
stop: ## Stop all services
	@echo "🛑 Stopping all services..."
	docker-compose down 2>/dev/null || true
	docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
	docker-compose -f docker-compose.orbstack.yml down 2>/dev/null || true
	@echo "✅ All services stopped!"

restart: ## Restart all services (auto-detect environment)
	@echo "🔄 Restarting services..."
	@if docker-compose -f docker-compose.orbstack.yml ps -q | grep -q .; then \
		echo "Detected OrbStack environment"; \
		$(MAKE) dev-orbstack-restart; \
	elif docker-compose -f docker-compose.dev.yml ps -q | grep -q .; then \
		echo "Detected development environment"; \
		docker-compose -f docker-compose.dev.yml restart; \
	elif docker-compose ps -q | grep -q .; then \
		echo "Detected production environment"; \
		docker-compose restart; \
	else \
		echo "No active environment detected"; \
	fi

clean: ## Stop and remove containers, networks, volumes
	@echo "🧹 Cleaning up..."
	docker-compose down -v --remove-orphans 2>/dev/null || true
	docker-compose -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true
	docker-compose -f docker-compose.orbstack.yml down -v --remove-orphans 2>/dev/null || true
	docker system prune -f
	@echo "✅ Cleanup completed!"

reset: clean ## Complete reset (clean + remove images)
	@echo "🔄 Resetting everything..."
	docker-compose down -v --rmi all --remove-orphans
	docker-compose -f docker-compose.dev.yml down -v --rmi all --remove-orphans
	docker-compose -f docker-compose.orbstack.yml down -v --rmi all --remove-orphans
	docker system prune -af
	@echo "✅ Complete reset completed!"

# OrbStack specific commands
orbstack-setup: ## Setup OrbStack environment
	@echo "🔧 Setting up OrbStack environment..."
	@echo "Adding local domain aliases..."
	@echo "127.0.0.1 client.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 gateway.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 api.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 pgadmin.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 redis-ui.orb.local" | sudo tee -a /etc/hosts
	@echo "127.0.0.1 mail.orb.local" | sudo tee -a /etc/hosts
	@echo "✅ OrbStack environment setup completed!"

orbstack-cleanup: ## Cleanup OrbStack environment
	@echo "🧹 Cleaning up OrbStack environment..."
	@echo "Removing local domain aliases..."
	sudo sed -i '' '/orb.local/d' /etc/hosts
	@echo "✅ OrbStack environment cleanup completed!"
