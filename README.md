# AERY - Browser Automation API

🚀 **AERY** is a powerful browser automation API that combines AI-driven task execution with pre-built automation scripts. Built with Deno, Fresh, PostgreSQL, and Redis for high performance and scalability.

## ✨ Features

- 🤖 **AI-Powered Automation**: Execute complex browser tasks using natural language instructions
- 📝 **Pre-Scripts**: Reusable automation templates for common tasks
- 🔄 **Auto-Healing**: Intelligent error recovery and task retry mechanisms
- 📊 **Real-time Monitoring**: Comprehensive metrics and performance tracking
- 🔐 **Secure Authentication**: JWT and API key-based authentication
- 🚀 **High Performance**: Built on Deno with Redis queuing for optimal speed
- 📈 **Scalable Architecture**: Microservices design with Docker support
- 🌐 **RESTful API**: Clean, well-documented API endpoints

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Apps      │    │   Mobile Apps   │    │  Third-party    │
│                 │    │                 │    │  Integration    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Gateway (Deno)       │
                    │   - Authentication        │
                    │   - Rate Limiting         │
                    │   - API Routing           │
                    │   - Request Validation    │
                    └─────────────┬─────────────┘
                                  │
              ┌───────────────────┼───────────────────┐
              │                   │                   │
    ┌─────────┴─────────┐ ┌───────┴───────┐ ┌─────────┴─────────┐
    │   PostgreSQL      │ │     Redis     │ │   Python Workers │
    │   - User Data     │ │   - Queues    │ │   - Playwright    │
    │   - Task History  │ │   - Cache     │ │   - AI Agents     │
    │   - Pre-scripts   │ │   - Sessions  │ │   - Auto-healing  │
    │   - Metrics       │ │   - Locks     │ │   - Multi-agent   │
    └───────────────────┘ └───────────────┘ └───────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- [Deno](https://deno.land/) 1.40+
- [Docker](https://www.docker.com/) & Docker Compose (or [OrbStack](https://orbstack.dev/) for macOS)
- [Python](https://python.org/) 3.11+ (for workers)

### 🚀 Super Quick Development Setup

```bash
# Option 1: One-line setup with Makefile
git clone https://github.com/your-org/aery.git && cd aery && make dev-setup

# Option 2: Interactive setup script
git clone https://github.com/your-org/aery.git && cd aery && ./scripts/quick-start.sh
```

### Manual Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/aery.git
   cd aery
   ```

2. **Quick development setup**
   ```bash
   make dev-setup    # Sets up everything for development
   make dev          # Start all services in development mode
   ```

3. **Or step by step:**
   ```bash
   # Copy environment file
   cp .env.example .env

   # Start with Docker Compose (choose your environment)
   make orbstack     # For OrbStack users (macOS)
   make docker       # For standard Docker
   make dev          # For development with hot reload
   ```

4. **Access the services:**
   - 🔌 **API Gateway**: http://localhost:8000
   - 📊 **PgAdmin**: http://localhost:8080
   - 🔴 **Redis Commander**: http://localhost:8081
   - 📧 **MailHog**: http://localhost:8025

### First API Call

```bash
# Health check
curl http://localhost:8000/health

# Register a new user
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "securepassword",
    "plan": "free"
  }'

# Execute a browser automation task
curl -X POST http://localhost:8000/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "instruction": "Go to google.com and search for 'Deno framework'",
    "url": "https://google.com"
  }'
```

## 📚 API Documentation

### Authentication

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "plan": "free" // free, pro, enterprise
}
```

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### Task Execution

#### Execute Task
```http
POST /execute
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "instruction": "Navigate to the website and fill out the contact form",
  "url": "https://example.com",
  "prescript_id": "optional-prescript-id",
  "options": {
    "timeout": 30000,
    "headless": true,
    "viewport": { "width": 1920, "height": 1080 }
  }
}
```

#### Get Task Status
```http
GET /tasks/{task_id}
Authorization: Bearer JWT_TOKEN
```

#### List Tasks
```http
GET /tasks?status=completed&limit=10&offset=0
Authorization: Bearer JWT_TOKEN
```

### Pre-scripts

#### List Pre-scripts
```http
GET /prescripts?category=ecommerce&active=true
Authorization: Bearer JWT_TOKEN
```

#### Create Pre-script
```http
POST /prescripts
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "name": "Login to Dashboard",
  "description": "Automated login flow for admin dashboard",
  "category": "authentication",
  "actions": [
    {
      "type": "navigate",
      "url": "https://admin.example.com/login"
    },
    {
      "type": "fill",
      "selector": "#email",
      "value": "{{email}}"
    },
    {
      "type": "fill",
      "selector": "#password",
      "value": "{{password}}"
    },
    {
      "type": "click",
      "selector": "button[type=submit]"
    }
  ],
  "variables": [
    { "name": "email", "type": "string", "required": true },
    { "name": "password", "type": "string", "required": true }
  ]
}
```

### Metrics

#### Get System Metrics
```http
GET /metrics?type=performance&from=2024-01-01&to=2024-01-31
Authorization: Bearer JWT_TOKEN
```

## 🛠️ Development

### Project Structure

```
aery/
├── server/
│   ├── gateway/              # API Gateway (Deno + Fresh)
│   │   ├── routes/           # API routes
│   │   ├── src/lib/          # Core libraries
│   │   └── main.ts           # Gateway entry point
│   └── workers/              # Python Workers
│       ├── src/              # Worker source code
│       ├── automation/       # Browser automation
│       └── main.py           # Workers entry point
├── shared/                   # Shared resources
│   ├── types/                # TypeScript definitions
│   ├── config/               # Shared configuration
│   ├── database/             # Database schemas & migrations
│   └── utils/                # Shared utilities
├── infrastructure/           # Infrastructure configuration
│   ├── docker/               # Docker configurations
│   ├── monitoring/           # Monitoring configs
│   └── nginx/                # Nginx configurations
├── docs/                     # Documentation
├── scripts/                  # Utility scripts
├── docker-compose*.yml       # Docker configurations
├── Makefile                  # Development commands
└── README.md
```

### Available Scripts

```bash
# Quick Development Setup
make dev-setup         # Complete development environment setup
make dev               # Start all services in development mode
make dev-logs          # View logs from all development services

# Service Management
make orbstack          # Start with OrbStack optimization (macOS)
make docker            # Start with standard Docker
make stop              # Stop all services
make clean             # Clean up containers and volumes

# Individual Services
make gateway           # Start only gateway service
make workers           # Start only workers service

# Development Tools
make test              # Run all tests
make test-gateway      # Test gateway
make test-workers      # Test workers
make lint              # Lint all code
make format            # Format all code

# Database
make db-reset          # Reset database
make db-migrate        # Run migrations
make db-seed           # Seed development data

# Monitoring
make monitor           # Start monitoring stack
make logs              # View all logs
make health            # Check service health
```

### Environment Variables

See `.env.example` for all available configuration options.

### Database Migrations

The database schema is automatically initialized when the application starts. The initial schema is defined in `shared/database/init.sql`.

### Development Workflow

1. **Setup**: `make dev-setup` - One-time setup
2. **Develop**: `make dev` - Start all services with hot reload
3. **Test**: `make test` - Run all tests
4. **Debug**: Services expose debug ports for IDE integration
5. **Monitor**: Access monitoring tools via web interfaces

## 🔧 Configuration

### Plans & Rate Limits

| Plan       | Requests/15min | Features                    |
|------------|----------------|-----------------------------||
| Free       | 10             | Basic automation            |
| Pro        | 100            | + Pre-scripts, Metrics      |
| Enterprise | 1000           | + Multi-agent, Auto-healing |

### Supported Actions

- `navigate` - Navigate to URL
- `click` - Click element
- `fill` - Fill input field
- `select` - Select dropdown option
- `wait` - Wait for element/time
- `scroll` - Scroll page
- `screenshot` - Take screenshot
- `extract` - Extract data
- `custom` - Custom JavaScript

## 📊 Monitoring

### Health Check

```http
GET /health
```

Returns system health status including database, Redis, and worker connectivity.

### Development Tools

- **API Gateway**: http://localhost:8000 - Backend API
- **PgAdmin**: http://localhost:8080 - Database management
- **Redis Commander**: http://localhost:8081 - Redis management
- **MailHog**: http://localhost:8025 - Email testing
- **Prometheus**: http://localhost:9090 - Metrics (when monitoring enabled)
- **Grafana**: http://localhost:3001 - Dashboards (when monitoring enabled)

### Prometheus Integration

Metrics are exposed in Prometheus format at `/metrics` endpoint.

## 🚀 Deployment

### Docker Production

```bash
# Build and deploy
docker-compose -f docker-compose.prod.yml up -d

# Scale workers
docker-compose -f docker-compose.prod.yml up -d --scale worker=3
```

### Environment Setup

1. Set production environment variables
2. Configure SSL certificates
3. Set up monitoring and logging
4. Configure backup strategies

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines

- Follow TypeScript best practices
- Write tests for new features
- Update documentation
- Follow conventional commit messages
- Ensure code passes `deno task check`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/aery)
- 📖 Documentation: [docs.aery.dev](https://docs.aery.dev)
- 🐛 Issues: [GitHub Issues](https://github.com/your-org/aery/issues)

## 🙏 Acknowledgments

- [Deno](https://deno.land/) - Secure runtime for JavaScript and TypeScript
- [Fresh](https://fresh.deno.dev/) - Modern web framework for Deno
- [Playwright](https://playwright.dev/) - Browser automation library
- [PostgreSQL](https://www.postgresql.org/) - Advanced open source database
- [Redis](https://redis.io/) - In-memory data structure store

---

**Made with ❤️ by the AERY Team**