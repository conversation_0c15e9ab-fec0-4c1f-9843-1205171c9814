// AERY Types - Definiciones de tipos TypeScript

// Tipos base del sistema
export interface User {
  id: string;
  email: string;
  apiKey: string;
  plan: "basic" | "premium" | "enterprise";
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface TaskExecution {
  id: string;
  userId: string;
  taskId: string;
  instruction: string;
  instructionHash: string;
  status: "pending" | "processing" | "completed" | "failed" | "cancelled";
  strategy?: "fast_path" | "full_llm" | "self_healing";
  executionTimeMs?: number;
  costUsd?: number;
  createdAt: Date;
  completedAt?: Date;
  errorMessage?: string;
  artifacts: Record<string, any>;
}

export interface PreScript {
  id: string;
  instructionHash: string;
  instructionText: string;
  actions: PlaywrightAction[];
  successCount: number;
  failureCount: number;
  successRate: number;
  platform: "web" | "mobile" | "desktop";
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface Worker {
  id: string;
  workerId: string;
  hostname: string;
  status: "idle" | "busy" | "offline";
  capabilities: string[];
  currentTaskId?: string;
  lastHeartbeat: Date;
  createdAt: Date;
  totalTasksCompleted: number;
}

// Tipos para acciones de Playwright
export interface PlaywrightAction {
  type:
    | "navigate"
    | "click"
    | "type"
    | "wait"
    | "screenshot"
    | "extract"
    | "scroll"
    | "hover";
  selector?: string;
  value?: string;
  timeout?: number;
  options?: Record<string, any>;
  description: string;
}

// Tipos para requests de API
export interface ExecuteTaskRequest {
  instruction: string;
  options?: {
    platform?: "web" | "mobile" | "desktop";
    timeout?: number;
    headless?: boolean;
    viewport?: {
      width: number;
      height: number;
    };
    userAgent?: string;
  };
}

export interface ExecuteTaskResponse {
  taskId: string;
  status: string;
  strategy: string;
  executionTime?: number;
  costSaving?: number;
  preScriptGenerated?: boolean;
  artifacts: {
    screenshots?: string[];
    extractedData?: Record<string, any>;
    logs?: string[];
  };
  error?: string;
}

// Tipos para agentes IA
export interface AgentResult {
  agentType: "explorer" | "tester" | "validator" | "analyzer";
  confidence: number;
  result: any;
  executionTime: number;
  error?: string;
}

export interface MultiAgentWorkflowResult {
  strategy: "sequential" | "parallel" | "hierarchical";
  agents: AgentResult[];
  finalActions: PlaywrightAction[];
  overallConfidence: number;
  totalExecutionTime: number;
}

// Tipos para colas Redis
export interface QueueTask {
  taskId: string;
  instruction: string;
  instructionHash: string;
  userId: string;
  priority: number;
  options: Record<string, any>;
  createdAt: Date;
  attempts: number;
  maxRetries: number;
}

export interface QueueMessage {
  type: "task" | "heartbeat" | "result" | "error";
  payload: any;
  timestamp: Date;
  workerId?: string;
}

// Tipos para métricas
export interface SystemMetric {
  id: string;
  metricName: string;
  metricValue: number;
  metricType: "counter" | "gauge" | "histogram";
  labels: Record<string, string>;
  timestamp: Date;
}

export interface PerformanceStats {
  hour: Date;
  strategy: string;
  totalExecutions: number;
  avgExecutionTime: number;
  avgCost: number;
  successfulExecutions: number;
  failedExecutions: number;
}

// Tipos para configuración
export interface AeryConfig {
  environment: "development" | "production" | "test";
  server: {
    port: number;
    host: string;
  };
  database: {
    url: string;
  };
  redis: {
    url: string;
    queuePrefix: string;
  };
  openrouter: {
    apiKey: string;
    baseUrl: string;
    defaultModel: string;
  };
  playwright: {
    headless: boolean;
    timeout: number;
    screenshotPath: string;
  };
  prescript: {
    cacheTtl: number;
    successThreshold: number;
  };
  security: {
    apiSecretKey: string;
    jwtSecret: string;
    allowedOrigins: string[];
  };
  rag: {
    cohereApiKey: string;
    embeddingModel: string;
    defaultTopK: number;
    chunkSize: number;
    chunkOverlap: number;
  };
}

// Tipos para errores
export interface AeryError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
  taskId?: string;
}

export class AeryApiError extends Error {
  constructor(
    public code: string,
    message: string,
    public statusCode: number = 500,
    public details?: Record<string, any>,
  ) {
    super(message);
    this.name = "AeryApiError";
  }
}

// Tipos para self-healing
export interface SelfHealingResult {
  success: boolean;
  originalError: string;
  healingStrategy: string;
  correctedActions: PlaywrightAction[];
  confidence: number;
  executionTime: number;
}

// Tipos para PocketFlow integration
export interface PocketFlowAgent {
  name: string;
  type: "explorer" | "tester" | "validator" | "analyzer";
  description: string;
  capabilities: string[];
  model: string;
  temperature: number;
  maxTokens: number;
}

export interface PocketFlowWorkflow {
  name: string;
  description: string;
  agents: PocketFlowAgent[];
  strategy: "sequential" | "parallel" | "hierarchical";
  timeout: number;
}
