-- AERY Database Schema
-- Inicialización de la base de datos PostgreSQL

-- Extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Tabla de usuarios y API keys
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255), -- Para autenticación con contraseña
    name VARCHAR(255), -- Nombre del usuario
    api_key VARCHAR(255) UNIQUE, -- Opcional si usan contraseña
    plan VARCHAR(50) DEFAULT 'basic' CHECK (plan IN ('basic', 'premium', 'enterprise')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Tabla de ejecuciones de tareas
CREATE TABLE task_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    task_id VARCHAR(255) UNIQUE NOT NULL,
    instruction TEXT NOT NULL,
    instruction_hash VARCHAR(64) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    strategy VARCHAR(50) CHECK (strategy IN ('fast_path', 'full_llm', 'self_healing')),
    execution_time_ms INTEGER,
    cost_usd DECIMAL(10, 6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    artifacts JSONB DEFAULT '{}'
);

-- Tabla de pre-scripts generados
CREATE TABLE pre_scripts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instruction_hash VARCHAR(64) UNIQUE NOT NULL,
    instruction_text TEXT NOT NULL,
    actions JSONB NOT NULL,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    success_rate DECIMAL(5, 4) GENERATED ALWAYS AS (
        CASE 
            WHEN (success_count + failure_count) = 0 THEN 0
            ELSE success_count::decimal / (success_count + failure_count)
        END
    ) STORED,
    platform VARCHAR(50) DEFAULT 'web' CHECK (platform IN ('web', 'mobile', 'desktop')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true
);

-- Tabla de workers registrados
CREATE TABLE workers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    worker_id VARCHAR(255) UNIQUE NOT NULL,
    hostname VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'idle' CHECK (status IN ('idle', 'busy', 'offline')),
    capabilities JSONB DEFAULT '[]',
    current_task_id VARCHAR(255),
    last_heartbeat TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_tasks_completed INTEGER DEFAULT 0
);

-- Tabla de métricas del sistema
CREATE TABLE system_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15, 6) NOT NULL,
    metric_type VARCHAR(50) CHECK (metric_type IN ('counter', 'gauge', 'histogram')),
    labels JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de logs de errores
CREATE TABLE error_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id VARCHAR(255),
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    context JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para optimización
CREATE INDEX idx_task_executions_user_id ON task_executions(user_id);
CREATE INDEX idx_task_executions_status ON task_executions(status);
CREATE INDEX idx_task_executions_created_at ON task_executions(created_at);
CREATE INDEX idx_task_executions_instruction_hash ON task_executions(instruction_hash);

CREATE INDEX idx_pre_scripts_instruction_hash ON pre_scripts(instruction_hash);
CREATE INDEX idx_pre_scripts_success_rate ON pre_scripts(success_rate);
CREATE INDEX idx_pre_scripts_platform ON pre_scripts(platform);

CREATE INDEX idx_workers_status ON workers(status);
CREATE INDEX idx_workers_last_heartbeat ON workers(last_heartbeat);

CREATE INDEX idx_system_metrics_metric_name ON system_metrics(metric_name);
CREATE INDEX idx_system_metrics_timestamp ON system_metrics(timestamp);

CREATE INDEX idx_error_logs_task_id ON error_logs(task_id);
CREATE INDEX idx_error_logs_error_type ON error_logs(error_type);
CREATE INDEX idx_error_logs_created_at ON error_logs(created_at);

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pre_scripts_updated_at BEFORE UPDATE ON pre_scripts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Datos de ejemplo para desarrollo
INSERT INTO users (email, password, api_key, plan) VALUES
    ('<EMAIL>', 'aery_dev_key_12345', 'aery_dev_key_12345', 'enterprise'),
    ('<EMAIL>', 'password123', 'aery_test_key_67890', 'premium'),
    ('<EMAIL>', 'password123', 'aery_demo_key_abcde', 'basic');

-- Vista para estadísticas de rendimiento
CREATE VIEW performance_stats AS
SELECT 
    DATE_TRUNC('hour', created_at) as hour,
    strategy,
    COUNT(*) as total_executions,
    AVG(execution_time_ms) as avg_execution_time,
    AVG(cost_usd) as avg_cost,
    COUNT(*) FILTER (WHERE status = 'completed') as successful_executions,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_executions
FROM task_executions 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', created_at), strategy
ORDER BY hour DESC;

-- Vista para métricas de pre-scripts
CREATE VIEW prescript_metrics AS
SELECT 
    platform,
    COUNT(*) as total_scripts,
    AVG(success_rate) as avg_success_rate,
    COUNT(*) FILTER (WHERE success_rate >= 0.7) as reliable_scripts,
    COUNT(*) FILTER (WHERE is_active = true) as active_scripts
FROM pre_scripts
GROUP BY platform;

COMMIT;