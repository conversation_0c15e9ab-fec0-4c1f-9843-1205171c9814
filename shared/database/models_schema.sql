-- AERY Models Schema Extension
-- Esquema para gestión dinámica de modelos de IA

-- Tabla de proveedores de IA
CREATE TABLE model_providers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL, -- openai, anthropic, etc.
    display_name VARCHAR(255) NOT NULL,
    api_base_url VARCHAR(500),
    auth_type VARCHAR(50) DEFAULT 'api_key' CHECK (auth_type IN ('api_key', 'oauth', 'bearer')),
    config JSONB DEFAULT '{}', -- Configuración específica del proveedor
    enabled BOOLEAN DEFAULT true,
    rate_limit_rpm INTEGER DEFAULT 1000, -- Requests per minute
    rate_limit_tpm INTEGER DEFAULT 100000, -- Tokens per minute
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de categorías de modelos
CREATE TABLE model_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL, -- chat, embedding, completion, image
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(50), -- Para la UI
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla principal de modelos
CREATE TABLE ai_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id VARCHAR(255) UNIQUE NOT NULL, -- gpt-4, claude-3.5-sonnet, etc.
    name VARCHAR(255) NOT NULL,
    provider_id UUID REFERENCES model_providers(id),
    category_id UUID REFERENCES model_categories(id),
    
    -- Configuración del modelo
    context_length INTEGER NOT NULL DEFAULT 4096,
    max_tokens INTEGER,
    supports_streaming BOOLEAN DEFAULT true,
    supports_function_calling BOOLEAN DEFAULT false,
    supports_vision BOOLEAN DEFAULT false,
    
    -- Precios (por 1M tokens)
    input_cost_per_1m DECIMAL(10, 6) NOT NULL DEFAULT 0,
    output_cost_per_1m DECIMAL(10, 6) NOT NULL DEFAULT 0,
    
    -- Parámetros por defecto
    default_temperature DECIMAL(3, 2) DEFAULT 0.1,
    default_top_p DECIMAL(3, 2) DEFAULT 0.9,
    default_top_k INTEGER,
    default_max_tokens INTEGER,
    
    -- Estado y metadatos
    enabled BOOLEAN DEFAULT true,
    is_legacy BOOLEAN DEFAULT false,
    deprecated_at TIMESTAMP WITH TIME ZONE,
    replacement_model_id UUID REFERENCES ai_models(id),
    
    -- Capacidades y características
    features JSONB DEFAULT '[]', -- Array de features soportadas
    metadata JSONB DEFAULT '{}', -- Metadatos adicionales
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de configuraciones de agentes
CREATE TABLE agent_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_type VARCHAR(100) NOT NULL, -- instruction_analyzer, action_planner, etc.
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Modelo principal y fallback
    primary_model_id UUID REFERENCES ai_models(id),
    fallback_model_id UUID REFERENCES ai_models(id),
    
    -- Configuración específica del agente
    temperature DECIMAL(3, 2),
    max_tokens INTEGER,
    top_p DECIMAL(3, 2),
    system_prompt TEXT,
    
    -- Estado
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(agent_type)
);

-- Tabla de estadísticas de uso de modelos
CREATE TABLE model_usage_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    model_id UUID REFERENCES ai_models(id),
    user_id UUID REFERENCES users(id),
    agent_type VARCHAR(100),
    
    -- Métricas de uso
    total_requests INTEGER DEFAULT 0,
    total_tokens_input BIGINT DEFAULT 0,
    total_tokens_output BIGINT DEFAULT 0,
    total_cost DECIMAL(12, 6) DEFAULT 0,
    
    -- Métricas de rendimiento
    avg_response_time_ms INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    
    -- Período de tiempo
    date DATE NOT NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(model_id, user_id, agent_type, date)
);

-- Tabla de configuraciones globales del sistema
CREATE TABLE system_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para optimización
CREATE INDEX idx_ai_models_provider_id ON ai_models(provider_id);
CREATE INDEX idx_ai_models_category_id ON ai_models(category_id);
CREATE INDEX idx_ai_models_enabled ON ai_models(enabled);
CREATE INDEX idx_ai_models_model_id ON ai_models(model_id);

CREATE INDEX idx_agent_configurations_agent_type ON agent_configurations(agent_type);
CREATE INDEX idx_agent_configurations_enabled ON agent_configurations(enabled);

CREATE INDEX idx_model_usage_stats_model_id ON model_usage_stats(model_id);
CREATE INDEX idx_model_usage_stats_user_id ON model_usage_stats(user_id);
CREATE INDEX idx_model_usage_stats_date ON model_usage_stats(date);
CREATE INDEX idx_model_usage_stats_agent_type ON model_usage_stats(agent_type);

CREATE INDEX idx_system_configurations_key ON system_configurations(key);

-- Triggers para updated_at
CREATE TRIGGER update_model_providers_updated_at BEFORE UPDATE ON model_providers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_models_updated_at BEFORE UPDATE ON ai_models
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_configurations_updated_at BEFORE UPDATE ON agent_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_model_usage_stats_updated_at BEFORE UPDATE ON model_usage_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configurations_updated_at BEFORE UPDATE ON system_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Datos iniciales de proveedores
INSERT INTO model_providers (name, display_name, api_base_url, config) VALUES
('openai', 'OpenAI', 'https://api.openai.com/v1', '{"api_key_header": "Authorization", "api_key_prefix": "Bearer"}'),
('anthropic', 'Anthropic', 'https://api.anthropic.com', '{"api_key_header": "x-api-key", "version": "2023-06-01"}'),
('openrouter', 'OpenRouter', 'https://openrouter.ai/api/v1', '{"api_key_header": "Authorization", "api_key_prefix": "Bearer"}'),
('cohere', 'Cohere', 'https://api.cohere.ai/v1', '{"api_key_header": "Authorization", "api_key_prefix": "Bearer"}');

-- Datos iniciales de categorías
INSERT INTO model_categories (name, display_name, description, icon) VALUES
('chat', 'Chat Models', 'Modelos conversacionales para chat y diálogo', '💬'),
('completion', 'Completion Models', 'Modelos para completar texto', '📝'),
('embedding', 'Embedding Models', 'Modelos para generar embeddings vectoriales', '🔗'),
('image', 'Image Models', 'Modelos para generación y análisis de imágenes', '🖼️'),
('code', 'Code Models', 'Modelos especializados en código', '💻');

-- Vistas útiles
CREATE VIEW model_summary AS
SELECT 
    m.id,
    m.model_id,
    m.name,
    p.display_name as provider_name,
    c.display_name as category_name,
    m.enabled,
    m.input_cost_per_1m,
    m.output_cost_per_1m,
    m.context_length,
    m.created_at
FROM ai_models m
LEFT JOIN model_providers p ON m.provider_id = p.id
LEFT JOIN model_categories c ON m.category_id = c.id;

CREATE VIEW usage_summary AS
SELECT 
    m.model_id,
    m.name,
    SUM(s.total_requests) as total_requests,
    SUM(s.total_tokens_input) as total_tokens_input,
    SUM(s.total_tokens_output) as total_tokens_output,
    SUM(s.total_cost) as total_cost,
    AVG(s.avg_response_time_ms) as avg_response_time_ms,
    SUM(s.success_count) as success_count,
    SUM(s.error_count) as error_count
FROM model_usage_stats s
JOIN ai_models m ON s.model_id = m.id
WHERE s.date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY m.id, m.model_id, m.name;

COMMIT;
