-- Datos iniciales para la gestión de modelos de IA

-- Insertar modelos de OpenAI
INSERT INTO ai_models (model_id, name, provider_id, category_id, context_length, supports_streaming, supports_function_calling, supports_vision, input_cost_per_1m, output_cost_per_1m, default_temperature, default_top_p, features, metadata) VALUES
(
    'gpt-4o',
    'GPT-4 Omni',
    (SELECT id FROM model_providers WHERE name = 'openai'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    128000,
    true,
    true,
    true,
    5.00,
    15.00,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "reasoning"]',
    '{"release_date": "2024-05-13", "description": "Modelo más capaz y eficiente de OpenAI"}'
),
(
    'gpt-4o-mini',
    'GPT-4 Omni Mini',
    (SELECT id FROM model_providers WHERE name = 'openai'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    128000,
    true,
    true,
    true,
    0.15,
    0.60,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "fast_responses"]',
    '{"release_date": "2024-07-18", "description": "Versión eficiente y económica de GPT-4o"}'
),
(
    'gpt-4-turbo',
    'GPT-4 Turbo',
    (SELECT id FROM model_providers WHERE name = 'openai'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    128000,
    true,
    true,
    true,
    10.00,
    30.00,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "large_context"]',
    '{"release_date": "2024-04-09", "description": "Modelo GPT-4 con mayor velocidad y contexto"}'
),
(
    'gpt-3.5-turbo',
    'GPT-3.5 Turbo',
    (SELECT id FROM model_providers WHERE name = 'openai'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    16385,
    true,
    true,
    false,
    0.50,
    1.50,
    0.1,
    0.9,
    '["streaming", "function_calling", "fast_responses"]',
    '{"release_date": "2023-03-01", "description": "Modelo eficiente para tareas conversacionales"}'
),
(
    'text-embedding-3-large',
    'Text Embedding 3 Large',
    (SELECT id FROM model_providers WHERE name = 'openai'),
    (SELECT id FROM model_categories WHERE name = 'embedding'),
    8192,
    false,
    false,
    false,
    0.13,
    0.00,
    0.0,
    1.0,
    '["embeddings", "semantic_search"]',
    '{"dimensions": 3072, "description": "Modelo de embeddings más poderoso"}'
),
(
    'text-embedding-3-small',
    'Text Embedding 3 Small',
    (SELECT id FROM model_providers WHERE name = 'openai'),
    (SELECT id FROM model_categories WHERE name = 'embedding'),
    8192,
    false,
    false,
    false,
    0.02,
    0.00,
    0.0,
    1.0,
    '["embeddings", "semantic_search", "fast_responses"]',
    '{"dimensions": 1536, "description": "Modelo de embeddings eficiente"}'
);

-- Insertar modelos de Anthropic
INSERT INTO ai_models (model_id, name, provider_id, category_id, context_length, supports_streaming, supports_function_calling, supports_vision, input_cost_per_1m, output_cost_per_1m, default_temperature, default_top_p, features, metadata) VALUES
(
    'claude-3-5-sonnet-20241022',
    'Claude 3.5 Sonnet',
    (SELECT id FROM model_providers WHERE name = 'anthropic'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    200000,
    true,
    true,
    true,
    3.00,
    15.00,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "reasoning", "coding"]',
    '{"release_date": "2024-10-22", "description": "Modelo más avanzado de Anthropic con excelentes capacidades de razonamiento"}'
),
(
    'claude-3-haiku-20240307',
    'Claude 3 Haiku',
    (SELECT id FROM model_providers WHERE name = 'anthropic'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    200000,
    true,
    true,
    true,
    0.25,
    1.25,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "fast_responses"]',
    '{"release_date": "2024-03-07", "description": "Modelo rápido y eficiente de Anthropic"}'
),
(
    'claude-3-opus-20240229',
    'Claude 3 Opus',
    (SELECT id FROM model_providers WHERE name = 'anthropic'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    200000,
    true,
    true,
    true,
    15.00,
    75.00,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "reasoning", "creative_writing"]',
    '{"release_date": "2024-02-29", "description": "Modelo premium de Anthropic para tareas complejas"}'
);

-- Insertar algunos modelos de OpenRouter (populares)
INSERT INTO ai_models (model_id, name, provider_id, category_id, context_length, supports_streaming, supports_function_calling, supports_vision, input_cost_per_1m, output_cost_per_1m, default_temperature, default_top_p, features, metadata) VALUES
(
    'meta-llama/llama-3.2-90b-vision-instruct',
    'Llama 3.2 90B Vision',
    (SELECT id FROM model_providers WHERE name = 'openrouter'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    131072,
    true,
    false,
    true,
    0.90,
    0.90,
    0.1,
    0.9,
    '["streaming", "vision", "reasoning"]',
    '{"provider": "Meta", "description": "Modelo Llama con capacidades de visión"}'
),
(
    'anthropic/claude-3.5-sonnet',
    'Claude 3.5 Sonnet (OpenRouter)',
    (SELECT id FROM model_providers WHERE name = 'openrouter'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    200000,
    true,
    true,
    true,
    3.00,
    15.00,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "reasoning"]',
    '{"provider": "Anthropic", "via": "OpenRouter", "description": "Claude 3.5 Sonnet via OpenRouter"}'
),
(
    'google/gemini-pro-1.5',
    'Gemini Pro 1.5',
    (SELECT id FROM model_providers WHERE name = 'openrouter'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    2000000,
    true,
    true,
    true,
    2.50,
    7.50,
    0.1,
    0.9,
    '["streaming", "function_calling", "vision", "large_context"]',
    '{"provider": "Google", "description": "Modelo Gemini con contexto extremo"}'
);

-- Insertar modelos de Cohere
INSERT INTO ai_models (model_id, name, provider_id, category_id, context_length, supports_streaming, supports_function_calling, supports_vision, input_cost_per_1m, output_cost_per_1m, default_temperature, default_top_p, features, metadata) VALUES
(
    'command-r-plus',
    'Command R+',
    (SELECT id FROM model_providers WHERE name = 'cohere'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    128000,
    true,
    true,
    false,
    3.00,
    15.00,
    0.1,
    0.9,
    '["streaming", "function_calling", "multilingual"]',
    '{"description": "Modelo avanzado de Cohere para tareas complejas"}'
),
(
    'command-r',
    'Command R',
    (SELECT id FROM model_providers WHERE name = 'cohere'),
    (SELECT id FROM model_categories WHERE name = 'chat'),
    128000,
    true,
    true,
    false,
    0.50,
    1.50,
    0.1,
    0.9,
    '["streaming", "function_calling", "multilingual", "fast_responses"]',
    '{"description": "Modelo eficiente de Cohere"}'
),
(
    'embed-english-v3.0',
    'Embed English v3.0',
    (SELECT id FROM model_providers WHERE name = 'cohere'),
    (SELECT id FROM model_categories WHERE name = 'embedding'),
    512,
    false,
    false,
    false,
    0.10,
    0.00,
    0.0,
    1.0,
    '["embeddings", "semantic_search"]',
    '{"dimensions": 1024, "description": "Modelo de embeddings en inglés"}'
);

-- Insertar configuraciones de agentes
INSERT INTO agent_configurations (agent_type, display_name, description, primary_model_id, fallback_model_id, temperature, max_tokens, top_p, system_prompt, enabled) VALUES
(
    'instruction_analyzer',
    'Instruction Analyzer',
    'Agente para analizar y estructurar instrucciones del usuario',
    (SELECT id FROM ai_models WHERE model_id = 'gpt-4o-mini'),
    (SELECT id FROM ai_models WHERE model_id = 'claude-3-haiku-20240307'),
    0.1,
    2048,
    0.9,
    'Eres un agente especializado en analizar instrucciones del usuario y extraer la información clave para ejecutar tareas de automatización web.',
    true
),
(
    'action_planner',
    'Action Planner',
    'Agente para planificar secuencias de acciones',
    (SELECT id FROM ai_models WHERE model_id = 'claude-3-5-sonnet-20241022'),
    (SELECT id FROM ai_models WHERE model_id = 'gpt-4o-mini'),
    0.1,
    4096,
    0.9,
    'Eres un agente especializado en planificar secuencias de acciones para automatización web, optimizando rutas y estrategias.',
    true
),
(
    'element_selector',
    'Element Selector',
    'Agente para seleccionar elementos en páginas web',
    (SELECT id FROM ai_models WHERE model_id = 'gpt-4o'),
    (SELECT id FROM ai_models WHERE model_id = 'claude-3-5-sonnet-20241022'),
    0.1,
    2048,
    0.9,
    'Eres un agente especializado en identificar y seleccionar elementos de páginas web usando selectores CSS, XPath y técnicas de computer vision.',
    true
),
(
    'validator',
    'Validator',
    'Agente para validar resultados de ejecución',
    (SELECT id FROM ai_models WHERE model_id = 'gpt-4o-mini'),
    (SELECT id FROM ai_models WHERE model_id = 'claude-3-haiku-20240307'),
    0.1,
    2048,
    0.9,
    'Eres un agente especializado en validar que las acciones ejecutadas han cumplido con los objetivos esperados.',
    true
),
(
    'self_healer',
    'Self Healer',
    'Agente para auto-reparación de errores',
    (SELECT id FROM ai_models WHERE model_id = 'claude-3-5-sonnet-20241022'),
    (SELECT id FROM ai_models WHERE model_id = 'gpt-4o'),
    0.2,
    4096,
    0.9,
    'Eres un agente especializado en diagnosticar errores y proponer soluciones alternativas para auto-reparar procesos de automatización.',
    true
);

-- Insertar configuraciones del sistema
INSERT INTO system_configurations (key, value, description) VALUES
('default_model_timeout', '30', 'Timeout por defecto para llamadas a modelos (segundos)'),
('max_retries', '3', 'Número máximo de reintentos para llamadas fallidas'),
('cache_ttl_seconds', '300', 'TTL del cache Redis para modelos (segundos)'),
('rate_limit_enabled', 'true', 'Habilitar limitación de velocidad para modelos'),
('cost_tracking_enabled', 'true', 'Habilitar seguimiento de costos de modelos'),
('auto_fallback_enabled', 'true', 'Habilitar fallback automático a modelos alternativos');

COMMIT;
