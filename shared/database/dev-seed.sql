-- AERY Development Seed Data
-- Datos de desarrollo y prueba para la base de datos

BEGIN;

-- Limpiar datos existentes si existen
TRUNCATE TABLE task_executions, pre_scripts, workers, system_metrics, error_logs RESTART IDENTITY CASCADE;

-- Usuarios de desarrollo (ya incluidos en init.sql, solo agregamos más si es necesario)
INSERT INTO users (email, password, name, api_key, plan) VALUES
    ('<EMAIL>', '$2b$10$8K1p/a0dFUgNBQN2.0T4uO7Yl8lKvHTGGU5Z4QJ0Z4QJ0Z4QJ0Z4Q', 'Developer User', 'dev_api_key_12345', 'enterprise'),
    ('<EMAIL>', '$2b$10$8K1p/a0dFUgNBQN2.0T4uO7Yl8lKvHTGGU5Z4QJ0Z4QJ0Z4QJ0Z4Q', 'Test User', 'test_api_key_67890', 'premium'),
    ('<EMAIL>', '$2b$10$8K1p/a0dFUgNBQN2.0T4uO7Yl8lKvHTGGU5Z4QJ0Z4QJ0Z4QJ0Z4Q', 'Demo User', 'demo_api_key_abcde', 'basic')
ON CONFLICT (email) DO NOTHING;

-- Pre-scripts de ejemplo para diferentes plataformas
INSERT INTO pre_scripts (instruction_hash, instruction_text, actions, success_count, failure_count, platform, is_active) VALUES
    (
        'hash_click_button_web', 
        'Click the login button',
        '[{"type": "click", "selector": "button[type=\"submit\"]", "description": "Click login button"}]',
        15, 2, 'web', true
    ),
    (
        'hash_fill_form_web',
        'Fill the registration form with email and password',
        '[{"type": "type", "selector": "input[name=\"email\"]", "text": "{email}", "description": "Enter email"}, {"type": "type", "selector": "input[name=\"password\"]", "text": "{password}", "description": "Enter password"}]',
        12, 1, 'web', true
    ),
    (
        'hash_navigate_page',
        'Navigate to the dashboard page',
        '[{"type": "click", "selector": "a[href=\"/dashboard\"]", "description": "Click dashboard link"}]',
        8, 0, 'web', true
    ),
    (
        'hash_mobile_tap',
        'Tap the menu button on mobile',
        '[{"type": "tap", "selector": "button.menu-toggle", "description": "Tap mobile menu"}]',
        5, 1, 'mobile', true
    );

-- Workers de ejemplo
INSERT INTO workers (worker_id, hostname, status, capabilities, current_task_id, total_tasks_completed) VALUES
    ('worker-001', 'localhost', 'idle', '["web_automation", "mobile_automation"]', NULL, 47),
    ('worker-002', 'localhost', 'idle', '["web_automation", "api_testing"]', NULL, 23),
    ('worker-dev', 'orbstack-dev', 'idle', '["web_automation", "mobile_automation", "desktop_automation"]', NULL, 0);

-- Ejecuciones de tareas de ejemplo
DO $$
DECLARE
    user_uuid UUID;
BEGIN
    -- Obtener el UUID del usuario de desarrollo
    SELECT id INTO user_uuid FROM users WHERE email = '<EMAIL>' LIMIT 1;
    
    IF user_uuid IS NOT NULL THEN
        INSERT INTO task_executions (user_id, task_id, instruction, instruction_hash, status, strategy, execution_time_ms, cost_usd, completed_at) VALUES
            (user_uuid, 'task_001', 'Login to the application', 'hash_click_button_web', 'completed', 'fast_path', 1250, 0.002, NOW() - INTERVAL '1 hour'),
            (user_uuid, 'task_002', 'Fill registration form', 'hash_fill_form_web', 'completed', 'fast_path', 2100, 0.003, NOW() - INTERVAL '30 minutes'),
            (user_uuid, 'task_003', 'Navigate to dashboard', 'hash_navigate_page', 'completed', 'fast_path', 800, 0.001, NOW() - INTERVAL '15 minutes'),
            (user_uuid, 'task_004', 'Complex workflow automation', 'hash_complex_workflow', 'processing', 'full_llm', NULL, NULL, NULL),
            (user_uuid, 'task_005', 'Failed automation attempt', 'hash_failed_attempt', 'failed', 'self_healing', 5000, 0.008, NOW() - INTERVAL '2 hours');
    END IF;
END $$;

-- Métricas del sistema de ejemplo
INSERT INTO system_metrics (metric_name, metric_value, metric_type, labels) VALUES
    ('tasks_completed_total', 120, 'counter', '{"worker": "worker-001"}'),
    ('tasks_failed_total', 8, 'counter', '{"worker": "worker-001"}'),
    ('execution_time_avg_ms', 1850, 'gauge', '{"strategy": "fast_path"}'),
    ('execution_time_avg_ms', 4200, 'gauge', '{"strategy": "full_llm"}'),
    ('cost_per_task_avg_usd', 0.0025, 'gauge', '{"strategy": "fast_path"}'),
    ('cost_per_task_avg_usd', 0.012, 'gauge', '{"strategy": "full_llm"}'),
    ('workers_active', 2, 'gauge', '{}'),
    ('queue_size', 0, 'gauge', '{}');

-- Logs de errores de ejemplo
INSERT INTO error_logs (task_id, error_type, error_message, stack_trace, context) VALUES
    ('task_005', 'SelectorNotFoundError', 'Could not find element with selector: button.submit-btn', 'Error at automation/browser_engine.py:145', '{"url": "https://example.com/form", "viewport": "1920x1080"}'),
    ('task_error_001', 'TimeoutError', 'Page load timeout after 30 seconds', 'Error at automation/browser_engine.py:89', '{"url": "https://slow-site.com", "timeout": 30000}'),
    ('task_error_002', 'ValidationError', 'Invalid instruction format provided', 'Error at agents/coordinator.py:67', '{"instruction": "invalid format", "expected": "structured format"}');

COMMIT;

-- Mostrar estadísticas después del seed
SELECT 'Users created:' as info, COUNT(*) as count FROM users
UNION ALL
SELECT 'Pre-scripts created:', COUNT(*) FROM pre_scripts
UNION ALL  
SELECT 'Workers registered:', COUNT(*) FROM workers
UNION ALL
SELECT 'Task executions:', COUNT(*) FROM task_executions
UNION ALL
SELECT 'System metrics:', COUNT(*) FROM system_metrics
UNION ALL
SELECT 'Error logs:', COUNT(*) FROM error_logs;

-- Mostrar información de usuarios para desarrollo
SELECT 
    'Development users available:' as info,
    email,
    api_key,
    plan
FROM users 
WHERE email LIKE '%aery.dev'
ORDER BY email;
