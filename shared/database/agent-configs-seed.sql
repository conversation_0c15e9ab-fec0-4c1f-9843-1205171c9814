-- Configuración inicial de agentes del sistema
-- Ejecutar después de la creación de las tablas de modelos

-- Insertar configuraciones de agentes por defecto
INSERT INTO agent_configurations (
  agent_type, 
  display_name, 
  description, 
  primary_model_id, 
  fallback_model_id,
  temperature,
  max_tokens,
  top_p,
  system_prompt,
  enabled,
  created_at,
  updated_at
) VALUES 
-- Instruction Analyzer Agent
(
  'instruction_analyzer',
  'Instruction Analyzer',
  'Analiza las instrucciones del usuario y las convierte en tareas ejecutables',
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o-mini' LIMIT 1),
  0.1,
  2048,
  0.9,
  'You are an expert instruction analyzer. Your role is to break down user instructions into clear, actionable tasks. Focus on understanding intent, identifying key elements, and structuring tasks logically.',
  true,
  NOW(),
  NOW()
),

-- Action Planner Agent  
(
  'action_planner',
  'Action Planner',
  'Planifica la secuencia de acciones necesarias para completar una tarea',
  (SELECT id FROM ai_models WHERE name = 'claude-3-5-sonnet-20241022' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  0.1,
  3072,
  0.9,
  'You are a strategic action planner. Your role is to create detailed, step-by-step action plans to accomplish tasks efficiently. Consider dependencies, resource requirements, and potential obstacles.',
  true,
  NOW(),
  NOW()
),

-- Element Selector Agent
(
  'element_selector',
  'Element Selector',
  'Selecciona elementos de la interfaz web de manera precisa y confiable',
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'claude-3-haiku-20240307' LIMIT 1),
  0.05,
  1024,
  0.8,
  'You are a precise web element selector. Your role is to identify and select web elements accurately using various strategies (CSS selectors, XPath, text content). Prioritize reliability and specificity.',
  true,
  NOW(),
  NOW()
),

-- Validator Agent
(
  'validator',
  'Validator',
  'Valida que las acciones se hayan ejecutado correctamente',
  (SELECT id FROM ai_models WHERE name = 'gpt-4o-mini' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'claude-3-haiku-20240307' LIMIT 1),
  0.05,
  1024,
  0.8,
  'You are a thorough validator. Your role is to verify that actions have been completed successfully by analyzing page state, element properties, and expected outcomes. Be precise and reliable in your assessments.',
  true,
  NOW(),
  NOW()
),

-- Self Healer Agent
(
  'self_healer',
  'Self Healer',
  'Diagnostica y repara automáticamente errores durante la ejecución',
  (SELECT id FROM ai_models WHERE name = 'claude-3-5-sonnet-20241022' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  0.2,
  2048,
  0.9,
  'You are an intelligent error recovery agent. Your role is to diagnose issues, understand their root causes, and implement effective solutions. Be creative and adaptive in your problem-solving approach.',
  true,
  NOW(),
  NOW()
),

-- RAG Research Agent
(
  'rag_researcher',
  'RAG Researcher',
  'Realiza investigación y análisis de documentos para RAG',
  (SELECT id FROM ai_models WHERE name = 'claude-3-5-sonnet-20241022' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  0.1,
  4096,
  0.9,
  'You are a research specialist for RAG systems. Your role is to analyze documents, extract key information, generate meaningful embeddings, and provide context-aware responses. Focus on accuracy and relevance.',
  true,
  NOW(),
  NOW()
),

-- Code Generator Agent
(
  'code_generator',
  'Code Generator',
  'Genera código automáticamente basado en especificaciones',
  (SELECT id FROM ai_models WHERE name = 'claude-3-5-sonnet-20241022' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  0.1,
  4096,
  0.9,
  'You are an expert code generator. Your role is to write clean, efficient, and well-documented code based on specifications. Follow best practices, consider edge cases, and ensure code quality.',
  true,
  NOW(),
  NOW()
),

-- Test Generator Agent
(
  'test_generator',
  'Test Generator',
  'Genera casos de prueba automatizados para validación',
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o-mini' LIMIT 1),
  0.1,
  3072,
  0.9,
  'You are a test automation specialist. Your role is to create comprehensive test cases, identify edge cases, and ensure thorough coverage. Focus on reliability and maintainability of tests.',
  true,
  NOW(),
  NOW()
);

-- Insertar configuraciones específicas por funcionalidad
INSERT INTO agent_configurations (
  agent_type, 
  display_name, 
  description, 
  primary_model_id, 
  fallback_model_id,
  temperature,
  max_tokens,
  top_p,
  system_prompt,
  enabled,
  created_at,
  updated_at
) VALUES 
-- Creative Writing
(
  'creative_writer',
  'Creative Writer',
  'Genera contenido creativo y narrativo',
  (SELECT id FROM ai_models WHERE name = 'claude-3-5-sonnet-20241022' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  0.7,
  4096,
  0.9,
  'You are a creative writing assistant. Your role is to generate engaging, original content with creativity and flair. Adapt your style to match the requested tone and audience.',
  true,
  NOW(),
  NOW()
),

-- Data Analyzer
(
  'data_analyzer',
  'Data Analyzer',
  'Analiza datos y genera insights',
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'claude-3-5-sonnet-20241022' LIMIT 1),
  0.1,
  3072,
  0.9,
  'You are a data analysis expert. Your role is to interpret data, identify patterns, generate insights, and provide actionable recommendations. Be thorough and precise in your analysis.',
  true,
  NOW(),
  NOW()
),

-- API Designer
(
  'api_designer',
  'API Designer',
  'Diseña APIs RESTful y GraphQL',
  (SELECT id FROM ai_models WHERE name = 'claude-3-5-sonnet-20241022' LIMIT 1),
  (SELECT id FROM ai_models WHERE name = 'gpt-4o' LIMIT 1),
  0.1,
  3072,
  0.9,
  'You are an API design specialist. Your role is to create well-structured, RESTful APIs and GraphQL schemas. Focus on best practices, security, performance, and developer experience.',
  true,
  NOW(),
  NOW()
);

-- Índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_agent_configurations_agent_type ON agent_configurations(agent_type);
CREATE INDEX IF NOT EXISTS idx_agent_configurations_enabled ON agent_configurations(enabled);
CREATE INDEX IF NOT EXISTS idx_agent_configurations_primary_model ON agent_configurations(primary_model_id);

-- Vista para obtener configuraciones con información de modelos
CREATE OR REPLACE VIEW agent_configurations_with_models AS
SELECT 
  ac.*,
  pm.name as primary_model_name,
  pm.provider_id as primary_provider_id,
  pp.display_name as primary_provider_name,
  fm.name as fallback_model_name,
  fm.provider_id as fallback_provider_id,
  fp.display_name as fallback_provider_name
FROM agent_configurations ac
LEFT JOIN ai_models pm ON ac.primary_model_id = pm.id
LEFT JOIN model_providers pp ON pm.provider_id = pp.id
LEFT JOIN ai_models fm ON ac.fallback_model_id = fm.id
LEFT JOIN model_providers fp ON fm.provider_id = fp.id
ORDER BY ac.display_name;
