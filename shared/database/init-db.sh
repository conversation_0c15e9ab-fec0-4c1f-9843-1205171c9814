#!/bin/bash
# Script de inicialización automática para PostgreSQL en OrbStack
# Este script se ejecuta después de que PostgreSQL inicia

set -e

echo "🔧 Starting AERY database initialization..."

# Esperar a que PostgreSQL esté listo
until pg_isready -h localhost -p 5432 -U aery; do
  echo "⏳ Waiting for PostgreSQL to be ready..."
  sleep 2
done

echo "✅ PostgreSQL is ready!"

# Verificar si las tablas ya existen
TABLES_EXIST=$(psql -h localhost -U aery -d aery -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users';" 2>/dev/null || echo "0")

if [ "$TABLES_EXIST" = "0" ] || [ -z "$TABLES_EXIST" ]; then
    echo "🗄️  Tables don't exist, running migrations..."
    
    # Ejecutar migrations
    echo "📋 Running init.sql..."
    psql -h localhost -U aery -d aery -f /docker-entrypoint-initdb.d/init.sql
    
    # Ejecutar seed data
    echo "🌱 Running dev-seed.sql..."
    psql -h localhost -U aery -d aery -f /docker-entrypoint-initdb.d/dev-seed.sql
    
    echo "✅ Database initialization completed!"
else
    echo "✅ Tables already exist, skipping initialization"
fi

echo "🚀 Database is ready for use!"
