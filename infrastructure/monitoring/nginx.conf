# Configuración de Nginx para AERY
# Proxy reverso y balanceador de carga

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

# Configuración de eventos
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# Configuración HTTP
http {
    # Configuración básica
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Configuración de logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Configuración de performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Configuración de compresión
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Configuración de buffers
    client_body_buffer_size 128k;
    client_max_body_size 100m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Configuración de timeouts
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;
    
    # Configuración de rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    # Configuración de conexiones
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # Upstream para el gateway de Deno
    upstream aery_gateway {
        least_conn;
        server gateway:8000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    # Upstream para métricas (Prometheus)
    upstream prometheus {
        server prometheus:9090;
    }
    
    # Upstream para dashboards (Grafana)
    upstream grafana {
        server grafana:3000;
    }
    
    # Configuración de cache
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=aery_cache:10m max_size=1g inactive=60m use_temp_path=off;
    
    # Servidor principal (HTTP)
    server {
        listen 80;
        server_name localhost aery.local;
        
        # Redirección a HTTPS en producción
        # return 301 https://$server_name$request_uri;
        
        # Configuración de seguridad
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
        
        # Health check
        location /nginx-health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # API Gateway (Deno)
        location / {
            # Rate limiting
            limit_req zone=api burst=20 nodelay;
            limit_conn conn_limit_per_ip 10;
            
            # Proxy configuration
            proxy_pass http://aery_gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
            
            # Buffering
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }
        
        # API específica con rate limiting más estricto
        location /api/ {
            limit_req zone=api burst=10 nodelay;
            limit_conn conn_limit_per_ip 5;
            
            proxy_pass http://aery_gateway;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Cache para respuestas GET
            proxy_cache aery_cache;
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404 1m;
            proxy_cache_use_stale error timeout invalid_header updating http_500 http_502 http_503 http_504;
            proxy_cache_lock on;
            
            # Headers de cache
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # WebSocket support
        location /ws {
            proxy_pass http://aery_gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts para WebSocket
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
        }
        
        # Archivos estáticos
        location /static/ {
            proxy_pass http://aery_gateway;
            
            # Cache agresivo para estáticos
            proxy_cache aery_cache;
            proxy_cache_valid 200 1h;
            proxy_cache_use_stale error timeout invalid_header updating;
            
            # Headers de cache
            expires 1h;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # Métricas (Prometheus) - Solo acceso interno
        location /metrics {
            allow **********/16;  # Red Docker
            allow 127.0.0.1;
            deny all;
            
            proxy_pass http://prometheus;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        # Dashboards (Grafana)
        location /grafana/ {
            proxy_pass http://grafana/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Bloquear acceso a archivos sensibles
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }
        
        location ~ /(config|logs|temp|artifacts)/ {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
    
    # Servidor HTTPS (para producción)
    # server {
    #     listen 443 ssl http2;
    #     server_name localhost aery.local;
    #     
    #     # Certificados SSL
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     # Configuración SSL
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     ssl_session_cache shared:SSL:10m;
    #     ssl_session_timeout 10m;
    #     
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    #     
    #     # Resto de configuración igual al servidor HTTP
    #     # ...
    # }
    
    # Servidor para monitoreo interno
    server {
        listen 8080;
        server_name localhost;
        
        # Solo acceso desde red interna
        allow **********/16;
        allow 127.0.0.1;
        deny all;
        
        # Status de Nginx
        location /nginx_status {
            stub_status on;
            access_log off;
        }
        
        # Métricas de Nginx para Prometheus
        location /metrics {
            # Aquí iría el módulo nginx-prometheus-exporter
            # return 200 "# Nginx metrics\n";
            access_log off;
        }
    }
}