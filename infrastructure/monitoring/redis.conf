# Configuración de Redis para AERY
# Optimizada para colas de tareas y cache

# =============================================================================
# CONFIGURACIÓN GENERAL
# =============================================================================

# Puerto de Redis
port 6379

# Bind a todas las interfaces (para Docker)
bind 0.0.0.0

# Protección de modo protegido
protected-mode no

# Timeout de conexiones inactivas (0 = sin timeout)
timeout 300

# TCP keepalive
tcp-keepalive 300

# =============================================================================
# CONFIGURACIÓN DE MEMORIA
# =============================================================================

# Política de eliminación cuando se alcanza el límite de memoria
maxmemory-policy allkeys-lru

# Límite de memoria (ajustar según necesidades)
# maxmemory 2gb

# Samples para LRU
maxmemory-samples 5

# =============================================================================
# CONFIGURACIÓN DE PERSISTENCIA
# =============================================================================

# Guardar snapshot cada 900 segundos si al menos 1 clave cambió
save 900 1
# Guardar snapshot cada 300 segundos si al menos 10 claves cambiaron
save 300 10
# Guardar snapshot cada 60 segundos si al menos 10000 claves cambiaron
save 60 10000

# Parar escrituras si el último snapshot falló
stop-writes-on-bgsave-error yes

# Comprimir snapshots
rdbcompression yes

# Checksum en snapshots
rdbchecksum yes

# Nombre del archivo de snapshot
dbfilename dump.rdb

# Directorio de trabajo
dir /data

# =============================================================================
# CONFIGURACIÓN DE LOGGING
# =============================================================================

# Nivel de log: debug, verbose, notice, warning
loglevel notice

# Archivo de log (vacío = stdout)
logfile ""

# =============================================================================
# CONFIGURACIÓN DE CLIENTES
# =============================================================================

# Máximo número de clientes conectados
maxclients 10000

# =============================================================================
# CONFIGURACIÓN DE SEGURIDAD
# =============================================================================

# Deshabilitar comandos peligrosos
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_AERY
rename-command DEBUG ""
rename-command EVAL ""

# =============================================================================
# CONFIGURACIÓN DE PERFORMANCE
# =============================================================================

# Deshabilitar transparent huge pages
# echo never > /sys/kernel/mm/transparent_hugepage/enabled

# TCP backlog
tcp-backlog 511

# Configuración de hash tables
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# Configuración de listas
list-max-ziplist-size -2
list-compress-depth 0

# Configuración de sets
set-max-intset-entries 512

# Configuración de sorted sets
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Configuración de HyperLogLog
hll-sparse-max-bytes 3000

# =============================================================================
# CONFIGURACIÓN DE STREAMS (Para colas)
# =============================================================================

# Configuración para Redis Streams
stream-node-max-bytes 4096
stream-node-max-entries 100

# =============================================================================
# CONFIGURACIÓN DE SLOW LOG
# =============================================================================

# Log de consultas lentas (microsegundos)
slowlog-log-slower-than 10000

# Máximo número de entradas en slow log
slowlog-max-len 128

# =============================================================================
# CONFIGURACIÓN DE LATENCIA
# =============================================================================

# Monitor de latencia
latency-monitor-threshold 100

# =============================================================================
# CONFIGURACIÓN DE NOTIFICACIONES
# =============================================================================

# Notificaciones de eventos de keyspace
notify-keyspace-events "Ex"

# =============================================================================
# CONFIGURACIÓN AVANZADA
# =============================================================================

# Frecuencia de background tasks
hz 10

# Configuración de AOF (Append Only File)
# appendonly yes
# appendfilename "appendonly.aof"
# appendfsync everysec
# no-appendfsync-on-rewrite no
# auto-aof-rewrite-percentage 100
# auto-aof-rewrite-min-size 64mb

# Configuración de replicación
# replica-serve-stale-data yes
# replica-read-only yes
# repl-diskless-sync no
# repl-diskless-sync-delay 5

# =============================================================================
# CONFIGURACIÓN ESPECÍFICA PARA AERY
# =============================================================================

# Configuración optimizada para colas de trabajo
# Las colas de AERY usan listas de Redis para FIFO
# Configuración para mejor rendimiento con listas
list-max-ziplist-size -2
list-compress-depth 0

# Configuración para TTL de tareas
# Las tareas tienen TTL automático para limpieza
lazyfree-lazy-eviction yes
lazyfree-lazy-expire yes
lazyfree-lazy-server-del yes

# Configuración para pub/sub (notificaciones de tareas)
client-output-buffer-limit pubsub 32mb 8mb 60

# =============================================================================
# CONFIGURACIÓN DE MÓDULOS
# =============================================================================

# Si necesitas módulos específicos, descomenta:
# loadmodule /path/to/module.so

# =============================================================================
# CONFIGURACIÓN DE CLUSTERING (Para escalabilidad futura)
# =============================================================================

# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-replica-validity-factor 10
# cluster-migration-barrier 1
# cluster-require-full-coverage yes