# Configuración de Prometheus para AERY
# Monitoreo y métricas del sistema

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'aery-monitor'
    environment: 'production'

# Configuración de reglas de alertas
rule_files:
  - "alert_rules.yml"

# Configuración de Alertmanager
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Configuración de scraping
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Deno Gateway metrics
  - job_name: 'aery-gateway'
    static_configs:
      - targets: ['gateway:8000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics
    # Nota: Requiere redis_exporter

  # PostgreSQL metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    # Nota: Requiere postgres_exporter

  # Python Workers metrics
  - job_name: 'aery-workers'
    static_configs:
      - targets: 
        - 'worker-1:8001'
        - 'worker-2:8001'
        - 'worker-3:8001'
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true

  # Nginx metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    # Nota: Requiere nginx-prometheus-exporter

  # Node Exporter (métricas del sistema)
  - job_name: 'node-exporter'
    static_configs:
      - targets: 
        # - 'node-exporter:9100'
    scrape_interval: 30s
    metrics_path: /metrics

  # cAdvisor (métricas de contenedores)
  - job_name: 'cadvisor'
    static_configs:
      - targets:
        # - 'cadvisor:8080'
    scrape_interval: 30s
    metrics_path: /metrics

  # Métricas personalizadas de AERY desde Redis
  - job_name: 'aery-custom-metrics'
    static_configs:
      - targets: ['gateway:8000']
    scrape_interval: 30s
    metrics_path: /api/v1/metrics
    scrape_timeout: 10s
    params:
      format: ['prometheus']

# Configuración de almacenamiento
# Nota: Las configuraciones de almacenamiento (path, retention, etc.) 
# deben configurarse como flags de línea de comandos en Prometheus:
# --storage.tsdb.path=/prometheus
# --storage.tsdb.retention.time=15d
# --storage.tsdb.retention.size=10GB
# --storage.tsdb.wal-compression

# Configuración de remote write (para escalabilidad)
# remote_write:
#   - url: "https://your-remote-prometheus/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Configuración de remote read
# remote_read:
#   - url: "https://your-remote-prometheus/api/v1/read"
#     basic_auth:
#       username: "username"
#       password: "password"