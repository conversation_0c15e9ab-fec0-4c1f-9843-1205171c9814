version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: aery_postgres
    environment:
      POSTGRES_DB: aery_db
      POSTGRES_USER: aery_user
      POSTGRES_PASSWORD: aery_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - aery_network

  # Redis for Queues and Cache
  redis:
    image: redis:7-alpine
    container_name: aery_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - aery_network
    command: redis-server --appendonly yes

  # Deno Gateway
  gateway:
    build:
      context: .
      dockerfile: gateway/Dockerfile
    container_name: aery_gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************************/aery_db
      - REDIS_URL=redis://redis:6379
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - API_SECRET_KEY=${API_SECRET_KEY}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - aery_network
    volumes:
      - ./artifacts:/app/artifacts

  # Python Workers
  worker:
    build:
      context: .
      dockerfile: workers/Dockerfile
    container_name: aery_worker
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=**************************************************/aery_db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - PLAYWRIGHT_HEADLESS=true
    depends_on:
      - redis
      - postgres
    networks:
      - aery_network
    volumes:
      - ./artifacts:/app/artifacts
      - /dev/shm:/dev/shm
    deploy:
      replicas: 2

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: aery_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - aery_network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=15d'
      - '--storage.tsdb.retention.size=10GB'
      - '--storage.tsdb.wal-compression'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: aery_grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - aery_network
    depends_on:
      - prometheus

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  aery_network:
    driver: bridge