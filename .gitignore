# AERY - Archivos y directorios a ignorar

# =============================================================================
# CONFIGURACIÓN Y SECRETOS
# =============================================================================
.env
.env.local
.env.production
.env.staging
*.key
*.pem
*.p12
*.pfx
secrets/
config/secrets/

# =============================================================================
# LOGS
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# =============================================================================
# DEPENDENCIAS
# =============================================================================
node_modules/
.npm
.pnpm-store/
.yarn/
.pnp.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
PIPFILE.lock

# =============================================================================
# ENTORNOS VIRTUALES
# =============================================================================
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# =============================================================================
# DENO
# =============================================================================
.deno/
deno.lock

# =============================================================================
# ARTEFACTOS Y ARCHIVOS TEMPORALES
# =============================================================================
artifacts/
temp/
tmp/
*.tmp
*.temp
.cache/
.temp/

# Screenshots y descargas
screenshots/
downloads/
uploads/

# =============================================================================
# DOCKER
# =============================================================================
.dockerignore
docker-compose.override.yml
.docker/

# =============================================================================
# BASES DE DATOS
# =============================================================================
*.db
*.sqlite
*.sqlite3
*.db-journal
data/
postgres_data/
redis_data/

# =============================================================================
# EDITORES E IDEs
# =============================================================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# JetBrains
.idea/
*.swp
*.swo

# Vim
*~
*.swp
*.swo
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# =============================================================================
# SISTEMAS OPERATIVOS
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# TESTING
# =============================================================================
coverage/
.coverage
.coverage.*
.cache
.pytest_cache/
cover/
*.cover
*.py,cover
.hypothesis/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# =============================================================================
# MONITOREO Y MÉTRICAS
# =============================================================================
prometheus_data/
grafana_data/
metrics/
*.metrics

# =============================================================================
# CERTIFICADOS Y CLAVES
# =============================================================================
*.crt
*.key
*.pem
*.p12
*.pfx
*.jks
*.keystore
*.truststore
ssl/
certs/

# =============================================================================
# BACKUPS
# =============================================================================
*.backup
*.bak
*.old
*.orig
*.save
backups/

# =============================================================================
# ARCHIVOS DE CONFIGURACIÓN LOCALES
# =============================================================================
.local
.config
local.json
local.yml
local.yaml

# =============================================================================
# PLAYWRIGHT
# =============================================================================
test-results/
playwright-report/
playwright/.cache/

# =============================================================================
# JUPYTER NOTEBOOKS
# =============================================================================
.ipynb_checkpoints
*/.ipynb_checkpoints/*
profile_default/
ipython_config.py

# =============================================================================
# OTROS
# =============================================================================
*.pid
*.seed
*.pid.lock
.grunt
lib-cov
.lock-wscript
.wafpickle-*
.node_repl_history
*.tgz
.yarn-integrity
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.optional
.pnp.js
.pnp.cjs
.zero-installs

# Parcel
.parcel-cache

# Next.js
.next
out

# Nuxt.js
.nuxt
dist

# Gatsby
.cache/
public

# Storybook
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# BUCK
buck-out/
.buckconfig.local
.buckd/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*