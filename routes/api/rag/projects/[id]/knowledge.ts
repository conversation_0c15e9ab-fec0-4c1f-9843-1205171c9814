// RAG Knowledge API Route - Gestión del knowledge base por proyecto
import { Handlers } from "$fresh/server.ts";
import { CorsMiddleware } from "../../../../../src/middleware/cors.ts";
import { projectManager } from "../../projects.ts";
import type { 
  AddKnowledgeRequest, 
  AddKnowledgeResponse 
} from "../../../../../src/rag/types/index.ts";

const knowledgeHandler: Handlers = {
  /**
   * GET /api/rag/projects/:id/knowledge - Obtener estadísticas del knowledge base
   */
  async GET(req, ctx) {
    const projectId = ctx.params.id;
    
    try {
      console.log(`📊 GET /api/rag/projects/${projectId}/knowledge - Getting knowledge stats`);
      
      // Verificar que el proyecto existe
      if (!projectManager.projectExists(projectId)) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Project not found",
            message: `Project '${projectId}' does not exist`
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      // Obtener estadísticas del proyecto
      const stats = await projectManager.getProjectStats(projectId);
      
      return new Response(
        JSON.stringify({
          success: true,
          projectId,
          knowledge: {
            totalDocuments: stats.vectorStore.totalDocuments,
            lastUpdated: stats.vectorStore.lastUpdated,
            projectInfo: {
              name: stats.project.name,
              description: stats.project.description,
              createdAt: stats.project.createdAt,
              updatedAt: stats.project.updatedAt
            }
          },
          ragEngine: stats.ragEngine
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error(`❌ Error getting knowledge stats for project ${projectId}:`, error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to get knowledge stats",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * POST /api/rag/projects/:id/knowledge - Añadir documentos al knowledge base
   */
  async POST(req, ctx) {
    const projectId = ctx.params.id;
    
    try {
      console.log(`📚 POST /api/rag/projects/${projectId}/knowledge - Adding knowledge`);
      
      // Verificar que el proyecto existe
      if (!projectManager.projectExists(projectId)) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Project not found",
            message: `Project '${projectId}' does not exist`
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      const body = await req.json() as AddKnowledgeRequest;
      
      // Validar entrada
      const validation = validateAddKnowledgeRequest(body);
      if (!validation.valid) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Invalid request",
            details: validation.errors
          }),
          {
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      // Añadir documentos al knowledge base
      await projectManager.addKnowledge(projectId, body.documents);
      
      const response: AddKnowledgeResponse = {
        documentsAdded: body.documents.length,
        message: `Successfully added ${body.documents.length} documents to knowledge base`
      };

      console.log(`✅ Added ${body.documents.length} documents to project ${projectId}`);

      // Obtener estadísticas actualizadas
      const updatedStats = await projectManager.getProjectStats(projectId);

      return new Response(
        JSON.stringify({
          success: true,
          ...response,
          updatedStats: {
            totalDocuments: updatedStats.vectorStore.totalDocuments,
            lastUpdated: updatedStats.vectorStore.lastUpdated
          }
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error(`❌ Error adding knowledge to project ${projectId}:`, error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to add knowledge",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  },

  /**
   * DELETE /api/rag/projects/:id/knowledge - Eliminar proyecto completo
   */
  async DELETE(req, ctx) {
    const projectId = ctx.params.id;
    
    try {
      console.log(`🗑️ DELETE /api/rag/projects/${projectId}/knowledge - Deleting project`);
      
      // Verificar que el proyecto existe
      if (!projectManager.projectExists(projectId)) {
        return new Response(
          JSON.stringify({
            success: false,
            error: "Project not found",
            message: `Project '${projectId}' does not exist`
          }),
          {
            status: 404,
            headers: { "Content-Type": "application/json" }
          }
        );
      }

      // Eliminar el proyecto completo
      await projectManager.deleteProject(projectId);

      console.log(`✅ Project ${projectId} deleted successfully`);

      return new Response(
        JSON.stringify({
          success: true,
          message: `Project '${projectId}' deleted successfully`
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" }
        }
      );
      
    } catch (error) {
      console.error(`❌ Error deleting project ${projectId}:`, error);
      
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to delete project",
          message: error.message
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
  }
};

/**
 * Validar request de añadir conocimiento
 */
function validateAddKnowledgeRequest(body: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!body) {
    errors.push("Request body is required");
    return { valid: false, errors };
  }

  if (!body.documents || !Array.isArray(body.documents)) {
    errors.push("Documents array is required");
    return { valid: false, errors };
  }

  if (body.documents.length === 0) {
    errors.push("At least one document is required");
  }

  if (body.documents.length > 50) {
    errors.push("Too many documents in single request (max 50)");
  }

  // Validar cada documento
  for (let i = 0; i < body.documents.length; i++) {
    const doc = body.documents[i];
    
    if (!doc.content || typeof doc.content !== 'string') {
      errors.push(`Document ${i + 1}: content is required and must be a string`);
    }
    
    if (doc.content && doc.content.length === 0) {
      errors.push(`Document ${i + 1}: content cannot be empty`);
    }
    
    if (doc.content && doc.content.length > 10000) {
      errors.push(`Document ${i + 1}: content is too long (max 10000 characters)`);
    }
    
    if (!doc.type || !['story', 'requirement', 'test', 'documentation'].includes(doc.type)) {
      errors.push(`Document ${i + 1}: type must be one of: story, requirement, test, documentation`);
    }

    if (doc.source && typeof doc.source !== 'string') {
      errors.push(`Document ${i + 1}: source must be a string`);
    }

    if (doc.metadata && typeof doc.metadata !== 'object') {
      errors.push(`Document ${i + 1}: metadata must be an object`);
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

// Export handler with CORS
export const handler = CorsMiddleware.wrap(knowledgeHandler);
