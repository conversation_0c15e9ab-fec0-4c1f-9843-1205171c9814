@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Force light mode theme */
:root {
  color-scheme: light;
}

html {
  color-scheme: light !important;
}

/* Disable dark mode styles */
html.dark {
  color-scheme: light !important;
}

/* Hide dark mode toggle buttons */
[data-testid="color-mode-button"],
.dark-mode-toggle {
  display: none !important;
}

/* Custom styles for AERY Dashboard */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status indicators */
.status-healthy {
  @apply bg-green-100 text-green-800 border-green-200;
}

.status-warning {
  @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

.status-error {
  @apply bg-red-100 text-red-800 border-red-200;
}

.status-info {
  @apply bg-blue-100 text-blue-800 border-blue-200;
}
