#!/usr/bin/env bash

# AERY Dashboard Startup Script
# This script helps you start the admin dashboard with proper configuration

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 AERY Admin Dashboard Startup${NC}"
echo "=================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed. Please install Node.js 18+ first.${NC}"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo -e "${RED}❌ Node.js version 18+ is required. Current version: $(node -v)${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js version: $(node -v)${NC}"

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ package.json not found. Make sure you're in the client directory.${NC}"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing dependencies...${NC}"
    npm install
fi

# Check if API is running
API_BASE=${NUXT_PUBLIC_API_BASE:-"http://localhost:8000"}
echo -e "${YELLOW}🔍 Checking API availability at $API_BASE...${NC}"

if curl -s -f "$API_BASE/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ API is running and healthy${NC}"
else
    echo -e "${YELLOW}⚠️  API is not responding at $API_BASE${NC}"
    echo -e "${YELLOW}   Make sure the AERY API server is running first${NC}"
    echo -e "${YELLOW}   You can still start the dashboard, but some features won't work${NC}"
    
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${RED}❌ Startup cancelled${NC}"
        exit 1
    fi
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}📝 Creating .env file...${NC}"
    cat > .env << EOF
# AERY Dashboard Configuration
NUXT_PUBLIC_API_BASE=http://localhost:8000
NUXT_PUBLIC_APP_NAME=AERY Dashboard
EOF
    echo -e "${GREEN}✅ Created .env file with default configuration${NC}"
fi

echo -e "${GREEN}🎉 Starting AERY Admin Dashboard...${NC}"
echo -e "${GREEN}   Dashboard will be available at: http://localhost:3000${NC}"
echo -e "${GREEN}   API endpoint: $API_BASE${NC}"
echo ""
echo -e "${YELLOW}💡 Quick Start Guide:${NC}"
echo -e "${YELLOW}   1. Register a new admin account${NC}"
echo -e "${YELLOW}   2. Run API tests to verify connectivity${NC}"
echo -e "${YELLOW}   3. Execute tasks with natural language${NC}"
echo -e "${YELLOW}   4. Create reusable pre-scripts${NC}"
echo ""

# Start the development server
npm run dev
