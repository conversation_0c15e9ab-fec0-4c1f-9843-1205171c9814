// Gestor de tareas del sistema
import { useEffect, useState } from "preact/hooks";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Clock,
  Eye,
  Play,
  Plus,
  RotateCcw,
  Search,
  Square,
  Trash2,
  XCircle,
} from "lucide-react";

interface Task {
  id: string;
  instruction: string;
  status: "pending" | "running" | "completed" | "failed" | "cancelled";
  agent_id: string;
  agent_name: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration?: number;
  progress: number;
  result?: {
    success: boolean;
    data?: Record<string, unknown>;
    error?: string;
    screenshots?: string[];
    logs?: string[];
  };
  options: {
    timeout: number;
    retries: number;
    priority: "low" | "normal" | "high";
    tags: string[];
  };
  metadata: {
    user_id?: string;
    source: string;
    retry_count: number;
  };
}

const MOCK_TASKS: Task[] = [
  {
    id: "task_001",
    instruction:
      "Navigate to example.com and fill the contact form with test data",
    status: "completed",
    agent_id: "agent_web_001",
    agent_name: "Web Navigator Pro",
    created_at: new Date(Date.now() - 3600000).toISOString(),
    started_at: new Date(Date.now() - 3500000).toISOString(),
    completed_at: new Date(Date.now() - 3300000).toISOString(),
    duration: 45000,
    progress: 100,
    result: {
      success: true,
      data: { form_submitted: true, confirmation_id: "CF123456" },
      screenshots: ["screenshot1.png", "screenshot2.png"],
    },
    options: {
      timeout: 60000,
      retries: 3,
      priority: "normal",
      tags: ["form-filling", "testing"],
    },
    metadata: {
      user_id: "user_123",
      source: "api",
      retry_count: 0,
    },
  },
  {
    id: "task_002",
    instruction: "Extract product information from e-commerce site catalog",
    status: "running",
    agent_id: "agent_scraper_001",
    agent_name: "Data Scraper Elite",
    created_at: new Date(Date.now() - 1800000).toISOString(),
    started_at: new Date(Date.now() - 1200000).toISOString(),
    duration: undefined,
    progress: 65,
    options: {
      timeout: 300000,
      retries: 2,
      priority: "high",
      tags: ["scraping", "e-commerce"],
    },
    metadata: {
      user_id: "user_456",
      source: "dashboard",
      retry_count: 0,
    },
  },
  {
    id: "task_003",
    instruction: "Perform automated testing on login functionality",
    status: "failed",
    agent_id: "agent_test_001",
    agent_name: "QA Automation Bot",
    created_at: new Date(Date.now() - 7200000).toISOString(),
    started_at: new Date(Date.now() - 7000000).toISOString(),
    completed_at: new Date(Date.now() - 6800000).toISOString(),
    duration: 15000,
    progress: 100,
    result: {
      success: false,
      error: "Login button not found after 30 seconds",
      logs: [
        "Navigating to login page...",
        "Waiting for login button...",
        "Timeout: Element not found",
      ],
    },
    options: {
      timeout: 120000,
      retries: 3,
      priority: "high",
      tags: ["testing", "authentication"],
    },
    metadata: {
      user_id: "user_789",
      source: "scheduler",
      retry_count: 2,
    },
  },
  {
    id: "task_004",
    instruction: "Monitor competitor pricing and update database",
    status: "pending",
    agent_id: "agent_monitor_001",
    agent_name: "Price Monitor",
    created_at: new Date(Date.now() - 300000).toISOString(),
    duration: undefined,
    progress: 0,
    options: {
      timeout: 600000,
      retries: 1,
      priority: "low",
      tags: ["monitoring", "pricing"],
    },
    metadata: {
      source: "cron",
      retry_count: 0,
    },
  },
];

export default function TasksManager() {
  const [tasks, setTasks] = useState<Task[]>(MOCK_TASKS);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>(MOCK_TASKS);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newTask, setNewTask] = useState({
    instruction: "",
    agent_id: "agent_web_001",
    timeout: 60000,
    retries: 3,
    priority: "normal" as const,
    tags: "",
  });

  // Simular actualizaciones de tareas en tiempo real
  useEffect(() => {
    const interval = setInterval(() => {
      setTasks((prev) =>
        prev.map((task) => {
          if (task.status === "running") {
            const newProgress = Math.min(
              100,
              task.progress + Math.random() * 10,
            );
            if (newProgress >= 100) {
              return {
                ...task,
                status: Math.random() > 0.2 ? "completed" : "failed",
                progress: 100,
                completed_at: new Date().toISOString(),
                duration: Date.now() - new Date(task.started_at!).getTime(),
                result: {
                  success: Math.random() > 0.2,
                  data: Math.random() > 0.2
                    ? { result: "Task completed successfully" }
                    : undefined,
                  error: Math.random() > 0.2
                    ? undefined
                    : "Task failed due to timeout",
                },
              };
            }
            return { ...task, progress: newProgress };
          }
          return task;
        })
      );
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Filtrar tareas
  useEffect(() => {
    let filtered = tasks;

    if (searchTerm) {
      filtered = filtered.filter((task) =>
        task.instruction.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.agent_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.options.tags.some((tag) =>
          tag.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((task) => task.status === statusFilter);
    }

    if (priorityFilter !== "all") {
      filtered = filtered.filter((task) =>
        task.options.priority === priorityFilter
      );
    }

    setFilteredTasks(filtered);
  }, [tasks, searchTerm, statusFilter, priorityFilter]);

  const getStatusIcon = (status: Task["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "running":
        return <Play className="w-4 h-4 text-blue-500" />;
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "failed":
        return <XCircle className="w-4 h-4 text-red-500" />;
      case "cancelled":
        return <Square className="w-4 h-4 text-gray-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: Task["status"]) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "running":
        return "text-blue-600 bg-blue-100";
      case "completed":
        return "text-green-600 bg-green-100";
      case "failed":
        return "text-red-600 bg-red-100";
      case "cancelled":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getPriorityColor = (priority: Task["options"]["priority"]) => {
    switch (priority) {
      case "high":
        return "text-red-600 bg-red-100";
      case "normal":
        return "text-blue-600 bg-blue-100";
      case "low":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const handleTaskAction = (
    taskId: string,
    action: "start" | "pause" | "cancel" | "retry" | "delete",
  ) => {
    setTasks((prev) => {
      switch (action) {
        case "start":
          return prev.map((task) =>
            task.id === taskId
              ? {
                ...task,
                status: "running",
                started_at: new Date().toISOString(),
                progress: 0,
              }
              : task
          );
        case "pause":
          return prev.map((task) =>
            task.id === taskId ? { ...task, status: "pending" } : task
          );
        case "cancel":
          return prev.map((task) =>
            task.id === taskId
              ? {
                ...task,
                status: "cancelled",
                completed_at: new Date().toISOString(),
              }
              : task
          );
        case "retry":
          return prev.map((task) =>
            task.id === taskId
              ? {
                ...task,
                status: "pending",
                progress: 0,
                result: undefined,
                metadata: {
                  ...task.metadata,
                  retry_count: task.metadata.retry_count + 1,
                },
              }
              : task
          );
        case "delete":
          return prev.filter((task) => task.id !== taskId);
        default:
          return prev;
      }
    });
  };

  const createTask = () => {
    if (!newTask.instruction.trim()) return;

    const task: Task = {
      id: `task_${Date.now()}`,
      instruction: newTask.instruction,
      status: "pending",
      agent_id: newTask.agent_id,
      agent_name: "Web Navigator Pro", // Simplificado para demo
      created_at: new Date().toISOString(),
      progress: 0,
      options: {
        timeout: newTask.timeout,
        retries: newTask.retries,
        priority: newTask.priority,
        tags: newTask.tags.split(",").map((tag) => tag.trim()).filter(Boolean),
      },
      metadata: {
        source: "dashboard",
        retry_count: 0,
      },
    };

    setTasks((prev) => [task, ...prev]);
    setNewTask({
      instruction: "",
      agent_id: "agent_web_001",
      timeout: 60000,
      retries: 3,
      priority: "normal",
      tags: "",
    });
    setShowCreateModal(false);
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return "N/A";
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Gestión de Tareas</h1>
        <button
          type="button"
          onClick={() => setShowCreateModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Nueva Tarea
        </button>
      </div>

      {/* Estadísticas rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[
          {
            label: "Total",
            value: tasks.length,
            color: "bg-gray-100 text-gray-800",
          },
          {
            label: "Pendientes",
            value: tasks.filter((t) => t.status === "pending").length,
            color: "bg-yellow-100 text-yellow-800",
          },
          {
            label: "En Ejecución",
            value: tasks.filter((t) => t.status === "running").length,
            color: "bg-blue-100 text-blue-800",
          },
          {
            label: "Completadas",
            value: tasks.filter((t) => t.status === "completed").length,
            color: "bg-green-100 text-green-800",
          },
          {
            label: "Fallidas",
            value: tasks.filter((t) => t.status === "failed").length,
            color: "bg-red-100 text-red-800",
          },
        ].map((stat) => (
          <div
            key={stat.label}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4"
          >
            <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
            <div
              className={`text-sm px-2 py-1 rounded-full inline-block ${stat.color}`}
            >
              {stat.label}
            </div>
          </div>
        ))}
      </div>

      {/* Filtros */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Buscar tareas..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Todos los estados</option>
            <option value="pending">Pendientes</option>
            <option value="running">En ejecución</option>
            <option value="completed">Completadas</option>
            <option value="failed">Fallidas</option>
            <option value="cancelled">Canceladas</option>
          </select>
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Todas las prioridades</option>
            <option value="high">Alta</option>
            <option value="normal">Normal</option>
            <option value="low">Baja</option>
          </select>
        </div>
      </div>

      {/* Lista de tareas */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tarea
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Agente
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Progreso
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duración
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Prioridad
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTasks.map((task) => (
                <tr key={task.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="max-w-xs">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {task.instruction}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(task.created_at).toLocaleString()}
                      </div>
                      {task.options.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {task.options.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(task.status)}
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          getStatusColor(task.status)
                        }`}
                      >
                        {task.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {task.agent_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {task.progress}%
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDuration(task.duration)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        getPriorityColor(task.options.priority)
                      }`}
                    >
                      {task.options.priority}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() =>
                          setSelectedTask(task)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {task.status === "pending" && (
                        <button
                          type="button"
                          onClick={() => handleTaskAction(task.id, "start")}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Play className="w-4 h-4" />
                        </button>
                      )}
                      {task.status === "running" && (
                        <button
                          type="button"
                          onClick={() => handleTaskAction(task.id, "cancel")}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Square className="w-4 h-4" />
                        </button>
                      )}
                      {task.status === "failed" && (
                        <button
                          type="button"
                          onClick={() => handleTaskAction(task.id, "retry")}
                          className="text-yellow-600 hover:text-yellow-900"
                        >
                          <RotateCcw className="w-4 h-4" />
                        </button>
                      )}
                      <button
                        type="button"
                        onClick={() =>
                          handleTaskAction(task.id, "delete")}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de crear tarea */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Crear Nueva Tarea
                </h3>
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Instrucción de la Tarea
                  </label>
                  <textarea
                    value={newTask.instruction}
                    onChange={(e) =>
                      setNewTask((prev) => ({
                        ...prev,
                        instruction: e.target.value,
                      }))}
                    placeholder="Describe qué debe hacer el agente..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Timeout (ms)
                    </label>
                    <input
                      type="number"
                      value={newTask.timeout}
                      onChange={(e) =>
                        setNewTask((prev) => ({
                          ...prev,
                          timeout: parseInt(e.target.value) || 60000,
                        }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reintentos
                    </label>
                    <input
                      type="number"
                      value={newTask.retries}
                      onChange={(e) =>
                        setNewTask((prev) => ({
                          ...prev,
                          retries: parseInt(e.target.value) || 3,
                        }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Prioridad
                  </label>
                  <select
                    value={newTask.priority}
                    onChange={(e) =>
                      setNewTask((prev) => ({
                        ...prev,
                        priority: e.target.value as "low" | "normal" | "high",
                      }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="low">Baja</option>
                    <option value="normal">Normal</option>
                    <option value="high">Alta</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags (separados por comas)
                  </label>
                  <input
                    type="text"
                    value={newTask.tags}
                    onChange={(e) =>
                      setNewTask((prev) => ({ ...prev, tags: e.target.value }))}
                    placeholder="testing, automation, form-filling"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="button"
                  onClick={createTask}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Crear Tarea
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de detalles de tarea */}
      {selectedTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-96 overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Detalles de la Tarea
                </h3>
                <button
                  type="button"
                  onClick={() => setSelectedTask(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">
                    Información General
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>ID:</strong> {selectedTask.id}
                    </div>
                    <div>
                      <strong>Instrucción:</strong> {selectedTask.instruction}
                    </div>
                    <div>
                      <strong>Estado:</strong>
                      <span
                        className={`ml-2 px-2 py-1 text-xs rounded-full ${
                          getStatusColor(selectedTask.status)
                        }`}
                      >
                        {selectedTask.status}
                      </span>
                    </div>
                    <div>
                      <strong>Agente:</strong> {selectedTask.agent_name}
                    </div>
                    <div>
                      <strong>Progreso:</strong> {selectedTask.progress}%
                    </div>
                    <div>
                      <strong>Duración:</strong>{" "}
                      {formatDuration(selectedTask.duration)}
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">
                    Configuración
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>Timeout:</strong> {selectedTask.options.timeout}ms
                    </div>
                    <div>
                      <strong>Reintentos:</strong>{" "}
                      {selectedTask.options.retries}
                    </div>
                    <div>
                      <strong>Prioridad:</strong>
                      <span
                        className={`ml-2 px-2 py-1 text-xs rounded-full ${
                          getPriorityColor(selectedTask.options.priority)
                        }`}
                      >
                        {selectedTask.options.priority}
                      </span>
                    </div>
                    <div>
                      <strong>Tags:</strong>{" "}
                      {selectedTask.options.tags.join(", ") || "Ninguno"}
                    </div>
                    <div>
                      <strong>Fuente:</strong> {selectedTask.metadata.source}
                    </div>
                    <div>
                      <strong>Reintentos realizados:</strong>{" "}
                      {selectedTask.metadata.retry_count}
                    </div>
                  </div>
                </div>
              </div>

              {selectedTask.result && (
                <div className="mt-6">
                  <h4 className="text-md font-medium text-gray-900 mb-3">
                    Resultado
                  </h4>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                      {JSON.stringify(selectedTask.result, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
