// Navegación lateral del dashboard administrativo
import { useState } from "preact/hooks";

interface NavItem {
  name: string;
  href: string;
  icon: string;
  current?: boolean;
}

const navigation: NavItem[] = [
  { name: "Dashboard", href: "/admin", icon: "📊", current: true },
  { name: "Agentes IA", href: "/admin/agents", icon: "🤖" },
  { name: "Gateway API", href: "/admin/gateway", icon: "🔌" },
  { name: "Servicios", href: "/admin/services", icon: "⚙️" },
  { name: "Tarea<PERSON>", href: "/admin/tasks", icon: "📋" },
  { name: "Pre-scripts", href: "/admin/prescripts", icon: "📝" },
  { name: "Pruebas API", href: "/admin/testing", icon: "🧪" },
  { name: "Logs", href: "/admin/logs", icon: "📄" },
  { name: "<PERSON>é<PERSON><PERSON>", href: "/admin/metrics", icon: "📈" },
];

export default function AdminSidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div class="fixed inset-0 flex z-40 lg:hidden">
          <div class="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)}></div>
          <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <div class="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setSidebarOpen(false)}
              >
                <span class="sr-only">Cerrar sidebar</span>
                <span class="text-white text-xl">✕</span>
              </button>
            </div>
            <SidebarContent />
          </div>
        </div>
      )}

      {/* Static sidebar for desktop */}
      <div class="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
        <SidebarContent />
      </div>

      {/* Mobile menu button */}
      <div class="lg:hidden">
        <div class="flex items-center justify-between bg-white border-b border-gray-200 px-4 py-1.5">
          <div>
            <h1 class="text-lg font-semibold text-gray-900">AERY Admin</h1>
          </div>
          <button
            type="button"
            class="-mr-3 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900"
            onClick={() => setSidebarOpen(true)}
          >
            <span class="sr-only">Abrir sidebar</span>
            <span class="text-xl">☰</span>
          </button>
        </div>
      </div>
    </>
  );
}

function SidebarContent() {
  return (
    <div class="flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200">
      <div class="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
        <div class="flex items-center flex-shrink-0 px-4">
          <h1 class="text-xl font-bold text-gray-900">AERY</h1>
          <span class="ml-2 text-sm text-gray-500 bg-blue-100 px-2 py-1 rounded">Admin</span>
        </div>
        <nav class="mt-5 flex-1 px-2 space-y-1">
          {navigation.map((item) => (
            <a
              key={item.name}
              href={item.href}
              class={`${
                item.current
                  ? "bg-blue-50 border-blue-500 text-blue-700"
                  : "border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              } group flex items-center px-2 py-2 text-sm font-medium border-l-4 transition-colors duration-200`}
            >
              <span class="mr-3 text-lg">{item.icon}</span>
              {item.name}
            </a>
          ))}
        </nav>
      </div>
      
      {/* Status indicator */}
      <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="h-3 w-3 bg-green-400 rounded-full"></div>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-700">Sistema Activo</p>
            <p class="text-xs text-gray-500">Todos los servicios funcionando</p>
          </div>
        </div>
      </div>
    </div>
  );
}