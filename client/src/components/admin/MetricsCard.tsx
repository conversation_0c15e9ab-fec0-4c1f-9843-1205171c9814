// Componente de tarjeta de métricas reutilizable
interface MetricsCardProps {
  title: string;
  value: string;
  icon: string;
  trend?: string;
  trendUp?: boolean;
}

export default function MetricsCard({ title, value, icon, trend, trendUp }: MetricsCardProps) {
  return (
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <span class="text-2xl">{icon}</span>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                {title}
              </dt>
              <dd class="flex items-baseline">
                <div class="text-2xl font-semibold text-gray-900">
                  {value}
                </div>
                {trend && (
                  <div class={`ml-2 flex items-baseline text-sm font-semibold ${
                    trendUp ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <span class="sr-only">
                      {trendUp ? 'Aumentó' : 'Disminuyó'} en
                    </span>
                    <span class="mr-1">
                      {trendUp ? '↗️' : '↘️'}
                    </span>
                    {trend}
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-5 py-3">
        <div class="text-sm">
          <a href="#" class="font-medium text-blue-700 hover:text-blue-900">
            Ver detalles
          </a>
        </div>
      </div>
    </div>
  );
}