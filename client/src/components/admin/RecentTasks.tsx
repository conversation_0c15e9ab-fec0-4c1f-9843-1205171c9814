// Componente para mostrar las tareas recientes del sistema
import { useState } from "preact/hooks";

interface Task {
  id: string;
  instruction: string;
  status: 'completed' | 'running' | 'failed' | 'queued';
  duration: string;
  timestamp: string;
  agent: string;
  prescript?: boolean;
}

export default function RecentTasks() {
  const [tasks] = useState<Task[]>([
    {
      id: "task_001",
      instruction: "Navegar a google.com y buscar 'Deno framework'",
      status: "completed",
      duration: "2.3s",
      timestamp: "Hace 5 min",
      agent: "Explorer",
      prescript: true
    },
    {
      id: "task_002",
      instruction: "Completar formulario de contacto en ejemplo.com",
      status: "running",
      duration: "15.2s",
      timestamp: "Hace 2 min",
      agent: "Tester"
    },
    {
      id: "task_003",
      instruction: "Extraer precios de productos en mercadolibre.com",
      status: "completed",
      duration: "8.7s",
      timestamp: "Hace 8 min",
      agent: "Analyzer",
      prescript: false
    },
    {
      id: "task_004",
      instruction: "Login automático en dashboard admin",
      status: "failed",
      duration: "12.1s",
      timestamp: "Hace 12 min",
      agent: "Validator"
    },
    {
      id: "task_005",
      instruction: "Monitorear cambios en página de productos",
      status: "queued",
      duration: "-",
      timestamp: "Hace 1 min",
      agent: "Explorer"
    }
  ]);

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'running':
        return 'text-blue-600 bg-blue-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'queued':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return 'Completada';
      case 'running':
        return 'Ejecutando';
      case 'failed':
        return 'Falló';
      case 'queued':
        return 'En cola';
      default:
        return 'Desconocido';
    }
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return '✅';
      case 'running':
        return '🔄';
      case 'failed':
        return '❌';
      case 'queued':
        return '⏳';
      default:
        return '❓';
    }
  };

  return (
    <div class="overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tarea
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Estado
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Agente
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Duración
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tiempo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Acciones
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {tasks.map((task) => (
              <tr key={task.id} class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-gray-900 max-w-xs truncate">
                        {task.instruction}
                      </div>
                      <div class="text-sm text-gray-500 flex items-center space-x-2">
                        <span>ID: {task.id}</span>
                        {task.prescript !== undefined && (
                          <span class={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                            task.prescript ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {task.prescript ? '📝 Pre-script' : '🤖 IA'}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    getStatusColor(task.status)
                  }`}>
                    <span class="mr-1">{getStatusIcon(task.status)}</span>
                    {getStatusText(task.status)}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800">
                    🤖 {task.agent}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {task.duration}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {task.timestamp}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <button class="text-blue-600 hover:text-blue-900">Ver</button>
                    <button class="text-gray-600 hover:text-gray-900">Logs</button>
                    {task.status === 'failed' && (
                      <button class="text-green-600 hover:text-green-900">Reintentar</button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div class="mt-4 flex justify-between items-center">
        <div class="text-sm text-gray-500">
          Mostrando 5 de 1,247 tareas
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
            Anterior
          </button>
          <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50">
            Siguiente
          </button>
        </div>
      </div>
    </div>
  );
}