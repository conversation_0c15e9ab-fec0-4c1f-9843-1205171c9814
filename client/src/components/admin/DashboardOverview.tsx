// Componente principal del dashboard con métricas y estado del sistema
import { useState, useEffect } from "preact/hooks";
import MetricsCard from "./MetricsCard.tsx";
import ServiceStatus from "./ServiceStatus.tsx";
import RecentTasks from "./RecentTasks.tsx";
import SystemChart from "./SystemChart.tsx";

interface SystemMetrics {
  totalTasks: number;
  activeTasks: number;
  successRate: number;
  avgExecutionTime: number;
  activeAgents: number;
  prescriptsGenerated: number;
}

export default function DashboardOverview() {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    totalTasks: 1247,
    activeTasks: 8,
    successRate: 94.2,
    avgExecutionTime: 3.4,
    activeAgents: 4,
    prescriptsGenerated: 156
  });

  const [isLoading, setIsLoading] = useState(false);

  // Simular actualización de métricas en tiempo real
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        activeTasks: Math.floor(Math.random() * 15) + 1,
        successRate: 90 + Math.random() * 8,
        avgExecutionTime: 2 + Math.random() * 3
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const refreshMetrics = async () => {
    setIsLoading(true);
    // Simular llamada a API
    await new Promise(resolve => setTimeout(resolve, 1000));
    setMetrics(prev => ({
      ...prev,
      totalTasks: prev.totalTasks + Math.floor(Math.random() * 5),
      prescriptsGenerated: prev.prescriptsGenerated + Math.floor(Math.random() * 2)
    }));
    setIsLoading(false);
  };

  return (
    <div class="space-y-6">
      {/* Header */}
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard Administrativo
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            Monitoreo en tiempo real del sistema AERY
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            onClick={refreshMetrics}
            disabled={isLoading}
          >
            <span class="mr-2">{isLoading ? "🔄" : "🔄"}</span>
            {isLoading ? "Actualizando..." : "Actualizar"}
          </button>
        </div>
      </div>

      {/* Métricas principales */}
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <MetricsCard
          title="Tareas Totales"
          value={metrics.totalTasks.toLocaleString()}
          icon="📊"
          trend="+12%"
          trendUp={true}
        />
        <MetricsCard
          title="Tareas Activas"
          value={metrics.activeTasks.toString()}
          icon="⚡"
          trend="En tiempo real"
          trendUp={true}
        />
        <MetricsCard
          title="Tasa de Éxito"
          value={`${metrics.successRate.toFixed(1)}%`}
          icon="✅"
          trend="+2.1%"
          trendUp={true}
        />
        <MetricsCard
          title="Tiempo Promedio"
          value={`${metrics.avgExecutionTime.toFixed(1)}s`}
          icon="⏱️"
          trend="-0.8s"
          trendUp={true}
        />
        <MetricsCard
          title="Agentes Activos"
          value={metrics.activeAgents.toString()}
          icon="🤖"
          trend="4/4 online"
          trendUp={true}
        />
        <MetricsCard
          title="Pre-scripts"
          value={metrics.prescriptsGenerated.toString()}
          icon="📝"
          trend="+8 hoy"
          trendUp={true}
        />
      </div>

      {/* Gráficos y estado de servicios */}
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              📈 Rendimiento del Sistema
            </h3>
            <SystemChart />
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              ⚙️ Estado de Servicios
            </h3>
            <ServiceStatus />
          </div>
        </div>
      </div>

      {/* Tareas recientes */}
      <div class="bg-white shadow rounded-lg">
        <div class="p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            📋 Tareas Recientes
          </h3>
          <RecentTasks />
        </div>
      </div>
    </div>
  );
}