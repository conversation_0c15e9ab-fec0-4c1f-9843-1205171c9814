// Layout principal para el dashboard administrativo
import { ComponentChildren } from "preact";
import AdminSidebar from "../components/admin/AdminSidebar.tsx";
import AdminHeader from "../components/admin/AdminHeader.tsx";

interface AdminLayoutProps {
  children: ComponentChildren;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div class="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <AdminSidebar />
      
      {/* Main content */}
      <div class="lg:pl-64">
        {/* Header */}
        <AdminHeader />
        
        {/* Page content */}
        <main class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}