// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  compatibilityDate: '2025-08-01',
  modules: [
    '@nuxt/ui',
    '@pinia/nuxt',
    '@vueuse/nuxt'
  ],
  css: ['~/assets/css/main.css'],
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:8000',
      appName: 'AERY Admin Dashboard'
    }
  },
  app: {
    head: {
      title: 'AERY Admin Dashboard',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'AERY Browser Automation API Admin Dashboard' }
      ]
    }
  },
  ssr: false, // SPA mode for easier API integration
  ui: {
    global: true,
    icons: ['heroicons'],
    primary: 'blue',
    gray: 'slate'
  },
  colorMode: {
    preference: 'light',
    fallback: 'light',
    storageKey: 'nuxt-color-mode',
    classSuffix: ''
  }
})
