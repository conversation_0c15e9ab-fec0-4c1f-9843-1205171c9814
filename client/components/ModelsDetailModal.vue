<template>
  <UModal v-model="isOpen" :ui="{ width: 'sm:max-w-4xl' }">
    <div class="p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Model Details</h3>
          <p class="text-sm text-gray-500">{{ model?.name }} ({{ model?.id }})</p>
        </div>
        <UButton
          @click="isOpen = false"
          variant="ghost"
          icon="i-heroicons-x-mark"
          size="sm"
        />
      </div>

      <div v-if="model" class="space-y-6">
        <!-- Model Overview -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
          <div class="flex items-start justify-between">
            <div>
              <h4 class="text-xl font-semibold text-gray-900 mb-2">{{ model.name }}</h4>
              <div class="flex items-center space-x-4 text-sm">
                <UBadge 
                  :color="getProviderColor(model.provider_id)"
                  variant="soft"
                >
                  {{ getProviderName(model.provider_id) }}
                </UBadge>
                <UBadge 
                  :color="getCategoryColor(model.category_id)"
                  variant="soft"
                >
                  {{ getCategoryName(model.category_id) }}
                </UBadge>
                <UBadge 
                  :color="model.enabled ? 'green' : 'red'"
                  variant="soft"
                >
                  {{ model.enabled ? 'Enabled' : 'Disabled' }}
                </UBadge>
              </div>
            </div>
            
            <!-- Actions -->
            <div class="flex items-center space-x-2">
              <UButton
                @click="editModel"
                variant="outline"
                icon="i-heroicons-pencil"
                size="sm"
              >
                Edit
              </UButton>
              <UButton
                @click="testModel"
                variant="outline"
                icon="i-heroicons-beaker"
                size="sm"
              >
                Test
              </UButton>
            </div>
          </div>
        </div>

        <!-- Technical Specifications -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Core Specs -->
          <UCard>
            <template #header>
              <h4 class="text-base font-medium text-gray-900">Technical Specifications</h4>
            </template>
            
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Model ID:</span>
                <span class="text-sm font-mono text-gray-900">{{ model.model_id }}</span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Context Length:</span>
                <span class="text-sm font-medium text-gray-900">{{ formatTokens(model.context_length) }}</span>
              </div>
              
              <div class="flex justify-between" v-if="model.max_tokens">
                <span class="text-sm text-gray-600">Max Tokens:</span>
                <span class="text-sm font-medium text-gray-900">{{ formatTokens(model.max_tokens) }}</span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Streaming:</span>
                <UIcon 
                  :name="model.supports_streaming ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle'"
                  :class="model.supports_streaming ? 'text-green-500' : 'text-red-500'"
                />
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Function Calling:</span>
                <UIcon 
                  :name="model.supports_function_calling ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle'"
                  :class="model.supports_function_calling ? 'text-green-500' : 'text-red-500'"
                />
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Vision:</span>
                <UIcon 
                  :name="model.supports_vision ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle'"
                  :class="model.supports_vision ? 'text-green-500' : 'text-red-500'"
                />
              </div>
            </div>
          </UCard>

          <!-- Pricing -->
          <UCard>
            <template #header>
              <h4 class="text-base font-medium text-gray-900">Pricing (per 1M tokens)</h4>
            </template>
            
            <div class="space-y-4">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Input Cost:</span>
                <span class="text-sm font-medium text-gray-900">${{ formatPrice(model.input_cost_per_1m) }}</span>
              </div>
              
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Output Cost:</span>
                <span class="text-sm font-medium text-gray-900">${{ formatPrice(model.output_cost_per_1m) }}</span>
              </div>
              
              <!-- Cost comparison -->
              <div class="pt-2 border-t border-gray-200">
                <div class="text-xs text-gray-500 mb-2">Cost Efficiency</div>
                <div class="flex items-center space-x-2">
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-green-500 h-2 rounded-full"
                      :style="{ width: getCostEfficiencyWidth(model) }"
                    ></div>
                  </div>
                  <span class="text-xs text-gray-600">{{ getCostEfficiencyLabel(model) }}</span>
                </div>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Default Parameters -->
        <UCard>
          <template #header>
            <h4 class="text-base font-medium text-gray-900">Default Parameters</h4>
          </template>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <span class="text-sm text-gray-600 block mb-1">Temperature</span>
              <span class="text-sm font-medium text-gray-900">{{ model.default_temperature }}</span>
            </div>
            
            <div>
              <span class="text-sm text-gray-600 block mb-1">Top P</span>
              <span class="text-sm font-medium text-gray-900">{{ model.default_top_p }}</span>
            </div>
            
            <div v-if="model.default_top_k">
              <span class="text-sm text-gray-600 block mb-1">Top K</span>
              <span class="text-sm font-medium text-gray-900">{{ model.default_top_k }}</span>
            </div>
            
            <div v-if="model.default_max_tokens">
              <span class="text-sm text-gray-600 block mb-1">Max Tokens</span>
              <span class="text-sm font-medium text-gray-900">{{ formatTokens(model.default_max_tokens) }}</span>
            </div>
          </div>
        </UCard>

        <!-- Features -->
        <UCard v-if="model.features && model.features.length > 0">
          <template #header>
            <h4 class="text-base font-medium text-gray-900">Features & Capabilities</h4>
          </template>
          
          <div class="flex flex-wrap gap-2">
            <UBadge 
              v-for="feature in model.features" 
              :key="feature"
              variant="soft"
              color="blue"
            >
              {{ feature }}
            </UBadge>
          </div>
        </UCard>

        <!-- Metadata -->
        <UCard v-if="model.metadata && Object.keys(model.metadata).length > 0">
          <template #header>
            <h4 class="text-base font-medium text-gray-900">Additional Metadata</h4>
          </template>
          
          <div class="space-y-2">
            <div 
              v-for="(value, key) in model.metadata" 
              :key="key"
              class="flex justify-between"
            >
              <span class="text-sm text-gray-600 capitalize">{{ String(key).replace(/_/g, ' ') }}:</span>
              <span class="text-sm font-medium text-gray-900">{{ formatMetadataValue(value) }}</span>
            </div>
          </div>
        </UCard>

        <!-- Usage Statistics (if available) -->
        <UCard v-if="usageStats">
          <template #header>
            <h4 class="text-base font-medium text-gray-900">Usage Statistics (Last 30 days)</h4>
          </template>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ usageStats.requests || 0 }}</div>
              <div class="text-xs text-gray-500">Requests</div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ formatTokens(usageStats.tokens || 0) }}</div>
              <div class="text-xs text-gray-500">Tokens</div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">${{ formatPrice(usageStats.cost || 0) }}</div>
              <div class="text-xs text-gray-500">Cost</div>
            </div>
            
            <div class="text-center">
              <div class="text-2xl font-bold text-yellow-600">{{ (usageStats.avg_response_time || 0).toFixed(1) }}s</div>
              <div class="text-xs text-gray-500">Avg Response</div>
            </div>
          </div>
        </UCard>
      </div>
    </div>
  </UModal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// import { useModelsStore } from '~/stores/models'
// import type { ModelConfig } from '~/types/models'

// Props
interface Props {
  modelValue: boolean
  model: any | null // Using any temporarily to avoid import issues
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'edit': [model: any]
  'test': [model: any]
}>()

// Computed
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Mock usage stats (in a real app, this would come from an API)
const usageStats = computed(() => {
  if (!props.model) return null
  
  // This would be fetched from your API
  return {
    requests: Math.floor(Math.random() * 1000),
    tokens: Math.floor(Math.random() * 1000000),
    cost: Math.random() * 100,
    avg_response_time: Math.random() * 5
  }
})

// Helper functions
const formatTokens = (tokens: number): string => {
  if (tokens >= 1000000) {
    return `${(tokens / 1000000).toFixed(1)}M`
  } else if (tokens >= 1000) {
    return `${(tokens / 1000).toFixed(1)}K`
  }
  return tokens.toString()
}

const formatPrice = (price: number): string => {
  return price.toFixed(2)
}

const formatMetadataValue = (value: any): string => {
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  return String(value)
}

const getProviderName = (providerId: string): string => {
  const providers: Record<string, string> = {
    'openai': 'OpenAI',
    'anthropic': 'Anthropic',
    'cohere': 'Cohere',
    'mistral': 'Mistral',
    'local': 'Local',
    'openrouter': 'OpenRouter'
  }
  return providers[providerId] || providerId
}

const getProviderColor = (providerId: string): string => {
  // Try to use store method if available, otherwise fallback
  const colors: Record<string, string> = {
    'openai': 'green',
    'anthropic': 'orange',
    'cohere': 'blue',
    'mistral': 'purple',
    'local': 'gray',
    'openrouter': 'red'
  }
  return colors[providerId] || 'gray'
}

const getCategoryName = (categoryId: string): string => {
  const categories: Record<string, string> = {
    'chat': 'Chat',
    'completion': 'Completion',
    'embedding': 'Embedding',
    'image': 'Image Generation',
    'audio': 'Audio',
    'multimodal': 'Multimodal'
  }
  return categories[categoryId] || categoryId
}

const getCategoryColor = (categoryId: string): string => {
  const colors: Record<string, string> = {
    'chat': 'blue',
    'completion': 'green',
    'embedding': 'purple',
    'image': 'pink',
    'audio': 'yellow',
    'multimodal': 'indigo'
  }
  return colors[categoryId] || 'gray'
}

const getCostEfficiencyWidth = (model: any): string => {
  const totalCost = model.input_cost_per_1m + model.output_cost_per_1m
  const maxCost = 100 // Assume max cost for comparison
  const efficiency = Math.max(0, 100 - (totalCost / maxCost * 100))
  return `${efficiency}%`
}

const getCostEfficiencyLabel = (model: any): string => {
  const totalCost = model.input_cost_per_1m + model.output_cost_per_1m
  if (totalCost < 10) return 'Excellent'
  if (totalCost < 25) return 'Good'
  if (totalCost < 50) return 'Average'
  return 'Expensive'
}

// Actions
const editModel = () => {
  if (props.model) {
    emit('edit', props.model)
    isOpen.value = false
  }
}

const testModel = () => {
  if (props.model) {
    emit('test', props.model)
    isOpen.value = false
  }
}
</script>
