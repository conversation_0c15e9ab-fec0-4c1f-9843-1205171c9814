<template>
  <div class="bg-white rounded-lg shadow-sm border">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Knowledge Base</h3>
      <p class="text-sm text-gray-500 mt-1">
        {{ knowledgeStats.totalDocuments }} documentos • 
        Actualizado {{ formatDate(knowledgeStats.lastUpdated) }}
      </p>
    </div>
    
    <div class="p-6">
      <div v-if="knowledgeStats.documents.length === 0" class="text-center py-8">
        <DocumentIcon class="w-12 h-12 mx-auto text-gray-400 mb-4" />
        <h4 class="text-lg font-medium text-gray-900 mb-2">No hay documentos</h4>
        <p class="text-gray-500">Agrega documentos para construir el knowledge base del proyecto.</p>
      </div>
      
      <div v-else class="space-y-4">
        <div 
          v-for="document in knowledgeStats.documents" 
          :key="document.id"
          class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors"
        >
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center space-x-2">
              <span 
                :class="getTypeColor(document.metadata.type)"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
              >
                {{ getTypeLabel(document.metadata.type) }}
              </span>
              <span v-if="document.metadata.source" class="text-xs text-gray-500">
                desde {{ document.metadata.source }}
              </span>
            </div>
            <span class="text-xs text-gray-400">
              {{ formatDate(document.metadata.timestamp) }}
            </span>
          </div>
          
          <div class="text-sm text-gray-900 leading-relaxed">
            {{ document.content }}
          </div>
          
          <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
            <span class="text-xs text-gray-500">
              ID: {{ document.id.split('-').slice(-2).join('-') }}
            </span>
            <button 
              @click="deleteDocument(document.id)"
              class="text-xs text-red-600 hover:text-red-700 focus:outline-none"
            >
              Eliminar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DocumentIcon } from '@heroicons/vue/24/outline'
import type { KnowledgeStats } from '../stores/rag'

interface Props {
  knowledgeStats: KnowledgeStats
}

const props = defineProps<Props>()

const emit = defineEmits<{
  documentDeleted: [documentId: string]
}>()

function getTypeLabel(type: string): string {
  switch (type) {
    case 'story': return 'Historia'
    case 'requirement': return 'Requerimiento'
    case 'test': return 'Test'
    case 'documentation': return 'Documentación'
    default: return type
  }
}

function getTypeColor(type: string): string {
  switch (type) {
    case 'story': return 'bg-blue-100 text-blue-800'
    case 'requirement': return 'bg-green-100 text-green-800'
    case 'test': return 'bg-purple-100 text-purple-800'
    case 'documentation': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMins < 1) return 'hace un momento'
  if (diffMins < 60) return `hace ${diffMins} minutos`
  if (diffHours < 24) return `hace ${diffHours} horas`
  if (diffDays < 7) return `hace ${diffDays} días`
  
  return date.toLocaleDateString('es-ES', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

function deleteDocument(documentId: string) {
  if (confirm('¿Estás seguro de que quieres eliminar este documento?')) {
    emit('documentDeleted', documentId)
  }
}
</script>
