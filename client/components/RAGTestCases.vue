<template>
  <div class="bg-white rounded-lg shadow-sm border">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Test Cases</h3>
        <div class="flex items-center space-x-2">
          <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">
            {{ testCases.length }} tests
          </span>
          <span class="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
            {{ edgeCases.length }} edge cases
          </span>
        </div>
      </div>
    </div>
    
    <div class="p-6">
      <!-- Test Cases -->
      <div class="space-y-6">
        <div 
          v-for="(testCase, index) in testCases" 
          :key="index"
          class="border border-gray-200 rounded-lg p-4"
        >
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">{{ testCase.title }}</h4>
              <p class="text-sm text-gray-600 mt-1">{{ testCase.description }}</p>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <span 
                :class="['px-2 py-1 rounded-full text-xs font-medium', getTypeColorClasses(testCase.type)]"
              >
                {{ getTypeLabel(testCase.type) }}
              </span>
              <span 
                :class="['px-2 py-1 rounded-full text-xs font-medium', getPriorityColorClasses(testCase.priority)]"
              >
                {{ getPriorityLabel(testCase.priority) }}
              </span>
            </div>
          </div>

          <!-- Steps -->
          <div class="space-y-2">
            <h5 class="text-sm font-medium text-gray-700">Pasos:</h5>
            <ol class="space-y-2">
              <li 
                v-for="step in testCase.steps" 
                :key="step.step"
                class="flex items-start space-x-3"
              >
                <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-blue-600">{{ step.step }}</span>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900">{{ step.action }}</p>
                  <p v-if="step.data" class="text-xs text-gray-500 mt-1">
                    <strong>Datos:</strong> {{ step.data }}
                  </p>
                  <p v-if="step.expectedBehavior" class="text-xs text-green-600 mt-1">
                    <strong>Esperado:</strong> {{ step.expectedBehavior }}
                  </p>
                </div>
              </li>
            </ol>
          </div>

          <!-- Expected Result -->
          <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <h5 class="text-sm font-medium text-green-800">Resultado Esperado:</h5>
            <p class="text-sm text-green-700 mt-1">{{ testCase.expectedResult }}</p>
          </div>
        </div>
      </div>

      <!-- Edge Cases -->
      <div v-if="edgeCases.length > 0" class="mt-8">
        <h4 class="text-lg font-medium text-gray-900 mb-4">Edge Cases</h4>
        <div class="space-y-4">
          <div 
            v-for="(edgeCase, index) in edgeCases" 
            :key="index"
            class="border border-orange-200 rounded-lg p-4 bg-orange-50"
          >
            <div class="flex items-start justify-between mb-2">
              <h5 class="font-medium text-gray-900">{{ edgeCase.scenario }}</h5>
              <span 
                :class="['px-2 py-1 rounded-full text-xs font-medium', getRiskColorClasses(edgeCase.riskLevel)]"
              >
                Riesgo {{ getRiskLabel(edgeCase.riskLevel) }}
              </span>
            </div>
            
            <p class="text-sm text-gray-700 mb-3">{{ edgeCase.description }}</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <h6 class="text-xs font-medium text-gray-600 uppercase tracking-wide">Manejo Sugerido</h6>
                <p class="text-sm text-gray-700 mt-1">{{ edgeCase.suggestedHandling }}</p>
              </div>
              <div>
                <h6 class="text-xs font-medium text-gray-600 uppercase tracking-wide">Enfoque de Testing</h6>
                <p class="text-sm text-gray-700 mt-1">{{ edgeCase.testApproach }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between pt-6 border-t border-gray-200 mt-6">
        <div class="flex items-center space-x-4 text-xs text-gray-500">
          <span>{{ testCases.length }} test cases</span>
          <span>•</span>
          <span>{{ edgeCases.length }} edge cases</span>
          <span>•</span>
          <span>{{ getTestTypeStats() }}</span>
        </div>
        
        <div class="flex items-center space-x-2">
          <button
            @click="copyTestCases"
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
          >
            <ClipboardIcon class="w-3 h-3 mr-1" />
            Copiar
          </button>
          
          <button
            @click="exportTestCases"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-purple-600 hover:bg-purple-700"
          >
            <ArrowDownTrayIcon class="w-3 h-3 mr-1" />
            Exportar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TestCase, EdgeCase } from '~/stores/rag'
import { ClipboardIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/outline'

// Props
interface Props {
  testCases: TestCase[]
  edgeCases: EdgeCase[]
}

const props = defineProps<Props>()

// Methods
const getTypeLabel = (type: string) => {
  switch (type) {
    case 'happy_path': return 'Happy Path'
    case 'negative': return 'Negativo'
    case 'boundary': return 'Límite'
    case 'integration': return 'Integración'
    default: return type
  }
}

const getTypeColorClasses = (type: string) => {
  switch (type) {
    case 'happy_path': return 'bg-green-100 text-green-800'
    case 'negative': return 'bg-red-100 text-red-800'
    case 'boundary': return 'bg-yellow-100 text-yellow-800'
    case 'integration': return 'bg-blue-100 text-blue-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getPriorityLabel = (priority: string) => {
  switch (priority) {
    case 'high': return 'Alta'
    case 'medium': return 'Media'
    case 'low': return 'Baja'
    default: return priority
  }
}

const getPriorityColorClasses = (priority: string) => {
  switch (priority) {
    case 'high': return 'bg-red-100 text-red-800'
    case 'medium': return 'bg-yellow-100 text-yellow-800'
    case 'low': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getRiskLabel = (risk: string) => {
  switch (risk) {
    case 'high': return 'Alto'
    case 'medium': return 'Medio'
    case 'low': return 'Bajo'
    default: return risk
  }
}

const getRiskColorClasses = (risk: string) => {
  switch (risk) {
    case 'high': return 'bg-red-100 text-red-800'
    case 'medium': return 'bg-yellow-100 text-yellow-800'
    case 'low': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getTestTypeStats = () => {
  const types = props.testCases.reduce((acc, test) => {
    acc[test.type] = (acc[test.type] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  return Object.entries(types)
    .map(([type, count]) => `${count} ${getTypeLabel(type).toLowerCase()}`)
    .join(', ')
}

const formatTestCasesForExport = () => {
  let content = '# Test Cases\n\n'
  
  // Test Cases
  props.testCases.forEach((testCase, index) => {
    content += `## ${index + 1}. ${testCase.title}\n\n`
    content += `**Descripción:** ${testCase.description}\n`
    content += `**Tipo:** ${getTypeLabel(testCase.type)}\n`
    content += `**Prioridad:** ${getPriorityLabel(testCase.priority)}\n\n`
    
    content += `### Pasos:\n`
    testCase.steps.forEach(step => {
      content += `${step.step}. ${step.action}\n`
      if (step.data) content += `   - Datos: ${step.data}\n`
      if (step.expectedBehavior) content += `   - Esperado: ${step.expectedBehavior}\n`
    })
    
    content += `\n**Resultado Esperado:** ${testCase.expectedResult}\n\n---\n\n`
  })
  
  // Edge Cases
  if (props.edgeCases.length > 0) {
    content += '# Edge Cases\n\n'
    props.edgeCases.forEach((edgeCase, index) => {
      content += `## ${index + 1}. ${edgeCase.scenario}\n\n`
      content += `**Descripción:** ${edgeCase.description}\n`
      content += `**Nivel de Riesgo:** ${getRiskLabel(edgeCase.riskLevel)}\n`
      content += `**Manejo Sugerido:** ${edgeCase.suggestedHandling}\n`
      content += `**Enfoque de Testing:** ${edgeCase.testApproach}\n\n---\n\n`
    })
  }
  
  return content
}

const copyTestCases = async () => {
  const text = formatTestCasesForExport()
  
  try {
    await navigator.clipboard.writeText(text)
    console.log('Test cases copied to clipboard')
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
  }
}

const exportTestCases = () => {
  const text = formatTestCasesForExport()
  const blob = new Blob([text], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  
  const a = document.createElement('a')
  a.href = url
  a.download = `test-cases-${Date.now()}.md`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}
</script>