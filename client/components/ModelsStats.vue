<template>
  <div class="py-6 space-y-6">
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <span class="text-blue-600 text-xl">📊</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900">Average Cost</h3>
          <p class="text-sm text-gray-600 mb-2">Per Request</p>
          <p class="text-2xl font-bold text-blue-600">
            {{ modelsStore.formatCost(modelsStore.avgCostPerRequest) }}
          </p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <span class="text-green-600 text-xl">🎯</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900">Most Used</h3>
          <p class="text-sm text-gray-600 mb-2">Model</p>
          <p class="text-lg font-bold text-green-600">
            {{ mostUsedModel?.model_id?.split('/').pop() || 'N/A' }}
          </p>
          <p class="text-xs text-gray-500">
            {{ modelsStore.formatTokens(mostUsedModel?.total_requests || 0) }} requests
          </p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <span class="text-yellow-600 text-xl">💸</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900">Most Expensive</h3>
          <p class="text-sm text-gray-600 mb-2">Model</p>
          <p class="text-lg font-bold text-yellow-600">
            {{ mostExpensiveModel?.model_id?.split('/').pop() || 'N/A' }}
          </p>
          <p class="text-xs text-gray-500">
            {{ modelsStore.formatCost(mostExpensiveModel?.total_cost || 0) }}
          </p>
        </div>
      </UCard>
    </div>

    <!-- Usage Statistics Table -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">Model Usage Statistics</h3>
          <UButton
            @click="refreshStats"
            :loading="modelsStore.loading.stats"
            variant="outline"
            size="sm"
            icon="i-heroicons-arrow-path"
          >
            Refresh
          </UButton>
        </div>
      </template>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Model
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Requests
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tokens In/Out
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total Cost
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Avg Response
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Success Rate
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Used
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr 
              v-for="stat in sortedStats" 
              :key="stat.model_id"
              class="hover:bg-gray-50"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <span class="text-lg">{{ getModelIcon(stat.model_id) }}</span>
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">
                      {{ getModelName(stat.model_id) }}
                    </div>
                    <div class="text-sm text-gray-500">{{ stat.model_id }}</div>
                  </div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ modelsStore.formatTokens(stat.total_requests) }}
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>
                  <div>In: {{ modelsStore.formatTokens(stat.total_tokens_input) }}</div>
                  <div class="text-gray-500">Out: {{ modelsStore.formatTokens(stat.total_tokens_output) }}</div>
                </div>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ modelsStore.formatCost(stat.total_cost) }}
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ Math.round(stat.avg_response_time) }}ms
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap">
                <UBadge 
                  :color="stat.success_rate >= 95 ? 'green' : stat.success_rate >= 85 ? 'yellow' : 'red'"
                  variant="soft"
                >
                  {{ Math.round(stat.success_rate) }}%
                </UBadge>
              </td>
              
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatLastUsed(stat.last_used) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty state -->
      <div v-if="modelsStore.usageStats.length === 0" class="text-center py-12">
        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <span class="text-gray-400 text-2xl">📊</span>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No usage data</h3>
        <p class="text-gray-500">Statistics will appear here after models are used.</p>
      </div>
    </UCard>

    <!-- Cost Breakdown -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Cost by Provider -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900">Cost by Provider</h3>
        </template>

        <div class="space-y-4">
          <div 
            v-for="provider in costByProvider" 
            :key="provider.name"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <UBadge 
                :color="modelsStore.getProviderColor(provider.name)"
                variant="soft"
              >
                {{ provider.name }}
              </UBadge>
            </div>
            
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">
                {{ modelsStore.formatCost(provider.cost) }}
              </div>
              <div class="text-xs text-gray-500">
                {{ Math.round(provider.percentage) }}%
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Usage by Category -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-gray-900">Usage by Category</h3>
        </template>

        <div class="space-y-4">
          <div 
            v-for="category in usageByCategory" 
            :key="category.name"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <span class="text-lg">{{ modelsStore.getCategoryIcon(category.name) }}</span>
              <span class="text-sm font-medium text-gray-900">
                {{ category.name.charAt(0).toUpperCase() + category.name.slice(1) }}
              </span>
            </div>
            
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900">
                {{ modelsStore.formatTokens(category.requests) }}
              </div>
              <div class="text-xs text-gray-500">
                {{ Math.round(category.percentage) }}%
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useModelsStore } from '~/stores/models'

const modelsStore = useModelsStore()
const toast = useToast()

// Computed
const mostUsedModel = computed(() => modelsStore.mostUsedModel)
const mostExpensiveModel = computed(() => modelsStore.mostExpensiveModel)

const sortedStats = computed(() => 
  [...modelsStore.usageStats].sort((a, b) => b.total_requests - a.total_requests)
)

const costByProvider = computed(() => {
  const providerCosts = new Map<string, number>()
  
  modelsStore.usageStats.forEach(stat => {
    const model = modelsStore.models.find(m => m.id === stat.model_id)
    if (model) {
      const current = providerCosts.get(model.provider) || 0
      providerCosts.set(model.provider, current + stat.total_cost)
    }
  })
  
  const total = Array.from(providerCosts.values()).reduce((sum, cost) => sum + cost, 0)
  
  return Array.from(providerCosts.entries())
    .map(([name, cost]) => ({
      name,
      cost,
      percentage: total > 0 ? (cost / total) * 100 : 0
    }))
    .sort((a, b) => b.cost - a.cost)
})

const usageByCategory = computed(() => {
  const categoryUsage = new Map<string, number>()
  
  modelsStore.usageStats.forEach(stat => {
    const model = modelsStore.models.find(m => m.id === stat.model_id)
    if (model) {
      const current = categoryUsage.get(model.category) || 0
      categoryUsage.set(model.category, current + stat.total_requests)
    }
  })
  
  const total = Array.from(categoryUsage.values()).reduce((sum, requests) => sum + requests, 0)
  
  return Array.from(categoryUsage.entries())
    .map(([name, requests]) => ({
      name,
      requests,
      percentage: total > 0 ? (requests / total) * 100 : 0
    }))
    .sort((a, b) => b.requests - a.requests)
})

// Methods
const refreshStats = async () => {
  try {
    await modelsStore.fetchUsageStats()
    toast.add({
      title: 'Statistics refreshed',
      color: 'green'
    })
  } catch (error) {
    toast.add({
      title: 'Error refreshing statistics',
      description: error.message,
      color: 'red'
    })
  }
}

const getModelName = (modelId: string) => {
  const model = modelsStore.models.find(m => m.id === modelId)
  return model?.name || modelId.split('/').pop() || modelId
}

const getModelIcon = (modelId: string) => {
  const model = modelsStore.models.find(m => m.id === modelId)
  return model ? modelsStore.getCategoryIcon(model.category) : '🤖'
}

const formatLastUsed = (dateString: string) => {
  if (!dateString) return 'Never'
  
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMins < 60) {
    return `${diffMins}m ago`
  } else if (diffHours < 24) {
    return `${diffHours}h ago`
  } else if (diffDays < 7) {
    return `${diffDays}d ago`
  } else {
    return date.toLocaleDateString()
  }
}
</script>