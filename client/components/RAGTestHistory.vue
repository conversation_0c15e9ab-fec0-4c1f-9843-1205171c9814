<template>
  <div class="bg-white rounded-lg shadow-sm border">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Historial de Tests</h3>
    </div>
    
    <div class="p-6">
      <div class="space-y-4">
        <div 
          v-for="(item, index) in history" 
          :key="index"
          class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors cursor-pointer"
          @click="selectTests(item)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ item.story }}
              </p>
              <p class="text-xs text-gray-500 mt-1">
                {{ formatDate(item.generatedAt) }}
              </p>
            </div>
          </div>
          
          <div class="mt-3 flex items-center justify-between">
            <div class="flex items-center space-x-4 text-xs text-gray-500">
              <div class="flex items-center space-x-1">
                <BeakerIcon class="w-3 h-3" />
                <span>{{ item.testCases.length }} tests</span>
              </div>
              <div class="flex items-center space-x-1">
                <ExclamationTriangleIcon class="w-3 h-3" />
                <span>{{ item.edgeCases.length }} edge cases</span>
              </div>
            </div>
            <button
              @click.stop="useTests(item)"
              class="text-purple-600 hover:text-purple-800 font-medium text-xs"
            >
              Ver detalles
            </button>
          </div>

          <!-- Test Types Preview -->
          <div class="mt-2 flex flex-wrap gap-1">
            <span 
              v-for="type in getUniqueTestTypes(item.testCases)"
              :key="type"
              :class="['px-2 py-0.5 rounded text-xs', getTypeColorClasses(type)]"
            >
              {{ getTypeLabel(type) }}
            </span>
          </div>
        </div>
      </div>
      
      <div v-if="history.length === 0" class="text-center py-8">
        <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p class="text-sm text-gray-500">No hay historial de tests</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRAGStore } from '~/stores/rag'
import { ClockIcon, BeakerIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/outline'

// Props
interface Props {
  history: Array<{
    story: string
    testCases: any[]
    edgeCases: any[]
    generatedAt: string
  }>
}

const props = defineProps<Props>()

// Store
const ragStore = useRAGStore()

// Methods
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('es-ES', {
    day: 'numeric',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getUniqueTestTypes = (testCases: any[]) => {
  return [...new Set(testCases.map(test => test.type))]
}

const getTypeLabel = (type: string) => {
  switch (type) {
    case 'happy_path': return 'Happy Path'
    case 'negative': return 'Negativo'
    case 'boundary': return 'Límite'
    case 'integration': return 'Integración'
    default: return type
  }
}

const getTypeColorClasses = (type: string) => {
  switch (type) {
    case 'happy_path': return 'bg-green-100 text-green-800'
    case 'negative': return 'bg-red-100 text-red-800'
    case 'boundary': return 'bg-yellow-100 text-yellow-800'
    case 'integration': return 'bg-blue-100 text-blue-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const selectTests = (item: any) => {
  ragStore.currentTestCases = item.testCases
  ragStore.currentEdgeCases = item.edgeCases
}

const useTests = (item: any) => {
  ragStore.setCurrentStory(item.story)
  ragStore.currentTestCases = item.testCases
  ragStore.currentEdgeCases = item.edgeCases
}
</script>