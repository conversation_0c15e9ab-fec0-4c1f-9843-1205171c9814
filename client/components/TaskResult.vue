<template>
  <UModal v-model="isOpen">
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Task Result</h3>
          <UButton variant="ghost" icon="i-heroicons-x-mark" @click="isOpen = false" />
        </div>
      </template>
      
      <div v-if="task" class="space-y-4">
        <!-- Task Info -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h4 class="font-medium text-gray-900 mb-2">Task Details</h4>
          <p class="text-sm text-gray-600 mb-1"><strong>ID:</strong> {{ task.id }}</p>
          <p class="text-sm text-gray-600 mb-1"><strong>Instruction:</strong> {{ task.instruction }}</p>
          <p class="text-sm text-gray-600 mb-1"><strong>URL:</strong> {{ task.url }}</p>
          <p class="text-sm text-gray-600"><strong>Status:</strong> 
            <UBadge :color="getStatusColor(task.status)" variant="subtle" size="xs">
              {{ task.status }}
            </UBadge>
          </p>
        </div>
        
        <!-- Screenshots -->
        <div v-if="task.screenshots && task.screenshots.length > 0">
          <h4 class="font-medium text-gray-900 mb-2">Screenshots</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="(screenshot, index) in task.screenshots" :key="index" class="border rounded-lg overflow-hidden">
              <img 
                :src="screenshot.url || `data:image/png;base64,${screenshot.data}`" 
                :alt="`Screenshot ${index + 1}`"
                class="w-full h-auto"
              />
              <div class="p-2 bg-gray-50">
                <p class="text-xs text-gray-600">{{ screenshot.timestamp || `Screenshot ${index + 1}` }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Result Data -->
        <div v-if="task.result">
          <h4 class="font-medium text-gray-900 mb-2">Execution Result</h4>
          <div class="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono overflow-auto max-h-64">
            <pre>{{ JSON.stringify(task.result, null, 2) }}</pre>
          </div>
        </div>
        
        <!-- Actions -->
        <div v-if="task.result && task.result.actions">
          <h4 class="font-medium text-gray-900 mb-2">Actions Performed</h4>
          <div class="space-y-2">
            <div v-for="(action, index) in task.result.actions" :key="index" class="border-l-4 border-blue-500 pl-3 py-2">
              <p class="text-sm font-medium">{{ action.type || action.action }}</p>
              <p class="text-xs text-gray-600">{{ action.description || action.details }}</p>
            </div>
          </div>
        </div>
        
        <!-- Error Info -->
        <div v-if="task.status === 'failed' && task.result && task.result.error">
          <h4 class="font-medium text-red-600 mb-2">Error Details</h4>
          <div class="bg-red-50 border border-red-200 p-4 rounded-lg">
            <p class="text-sm text-red-800">{{ task.result.error.message || task.result.error }}</p>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <UButton variant="outline" @click="isOpen = false">Close</UButton>
          <UButton v-if="task" @click="retryTask" :loading="loading">Retry Task</UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup>
defineProps({
  task: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

defineEmits(['retry'])

const isOpen = defineModel('modelValue', { type: Boolean, default: false })

const getStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'green'
    case 'failed': return 'red'
    case 'pending': return 'yellow'
    case 'running': return 'blue'
    default: return 'gray'
  }
}

const retryTask = () => {
  emit('retry', task.value)
  isOpen.value = false
}
</script>
