<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white" @click.stop>
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Crear Nuevo Proyecto</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Project Name -->
          <div>
            <label for="project-name" class="block text-sm font-medium text-gray-700">
              Nombre del Proyecto *
            </label>
            <input
              id="project-name"
              v-model="form.name"
              type="text"
              required
              placeholder="Ej: E-commerce Platform"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Project Description -->
          <div>
            <label for="project-description" class="block text-sm font-medium text-gray-700">
              Descripción
            </label>
            <textarea
              id="project-description"
              v-model="form.description"
              rows="3"
              placeholder="Describe brevemente el proyecto y su contexto..."
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <!-- Initial Documents -->
          <div>
            <div class="flex items-center justify-between mb-3">
              <label class="block text-sm font-medium text-gray-700">
                Documentos Iniciales (opcional)
              </label>
              <button
                type="button"
                @click="addDocument"
                class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200"
              >
                <PlusIcon class="w-3 h-3 mr-1" />
                Añadir documento
              </button>
            </div>
            
            <div v-if="form.initialDocuments.length === 0" class="text-sm text-gray-500 italic">
              Los documentos iniciales ayudan a crear el contexto base del proyecto
            </div>

            <div v-else class="space-y-3">
              <div 
                v-for="(doc, index) in form.initialDocuments" 
                :key="index"
                class="border border-gray-200 rounded-lg p-3"
              >
                <div class="flex items-center justify-between mb-2">
                  <select
                    v-model="doc.type"
                    class="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="story">User Story</option>
                    <option value="requirement">Requerimiento</option>
                    <option value="test">Test Case</option>
                    <option value="documentation">Documentación</option>
                  </select>
                  <button
                    type="button"
                    @click="removeDocument(index)"
                    class="text-red-400 hover:text-red-600"
                  >
                    <XMarkIcon class="w-4 h-4" />
                  </button>
                </div>
                <textarea
                  v-model="doc.content"
                  rows="3"
                  placeholder="Contenido del documento..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancelar
            </button>
            <button
              type="submit"
              :disabled="!canSubmit || loading"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <template v-if="loading">
                <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creando...
              </template>
              <template v-else>
                <FolderPlusIcon class="w-4 h-4 mr-2" />
                Crear Proyecto
              </template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRAGStore } from '~/stores/rag'
import { XMarkIcon, PlusIcon, FolderPlusIcon } from '@heroicons/vue/24/outline'

// Emits
const emit = defineEmits<{
  close: []
  created: [projectId: string]
}>()

// Store
const ragStore = useRAGStore()

// Form state
const form = ref({
  name: '',
  description: '',
  initialDocuments: [] as Array<{
    content: string
    type: 'story' | 'requirement' | 'test' | 'documentation'
    source: string
  }>
})

const loading = ref(false)

// Computed
const canSubmit = computed(() => {
  return form.value.name.trim().length > 0 && 
         form.value.initialDocuments.every(doc => doc.content.trim().length > 0)
})

// Methods
const addDocument = () => {
  form.value.initialDocuments.push({
    content: '',
    type: 'story',
    source: 'initial-setup'
  })
}

const removeDocument = (index: number) => {
  form.value.initialDocuments.splice(index, 1)
}

const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  loading.value = true
  
  try {
    const projectId = await ragStore.createProject(
      form.value.name,
      form.value.description || undefined,
      form.value.initialDocuments.length > 0 ? form.value.initialDocuments : undefined
    )
    
    emit('created', projectId)
  } catch (error) {
    console.error('Failed to create project:', error)
  } finally {
    loading.value = false
  }
}
</script>