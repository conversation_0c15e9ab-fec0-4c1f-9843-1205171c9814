<template>
  <div v-if="result" class="mt-3 p-3 rounded-lg border" :class="resultClasses">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <Icon 
          :name="result.success ? 'i-heroicons-check-circle' : 'i-heroicons-x-circle'" 
          :class="result.success ? 'text-green-500' : 'text-red-500'"
          class="w-4 h-4"
        />
        <span class="text-sm font-medium">
          {{ result.success ? 'PASS' : 'FAIL' }}
        </span>
      </div>
      
      <span class="text-xs text-gray-500">
        {{ result.duration }}ms
      </span>
    </div>
    
    <div class="mt-2 text-sm">
      <p class="text-gray-700">{{ result.status }}</p>
      
      <p v-if="result.error" class="text-red-600 mt-1">
        <strong>Error:</strong> {{ result.error }}
      </p>
      
      <p v-if="result.details" class="text-gray-600 mt-1">
        <strong>Details:</strong> {{ result.details }}
      </p>
      
      <p v-if="result.timestamp" class="text-xs text-gray-500 mt-2">
        Tested at {{ formatTime(result.timestamp) }}
      </p>
    </div>
  </div>
  
  <div v-else class="mt-3 p-3 rounded-lg border border-gray-200 bg-gray-50">
    <div class="flex items-center space-x-2">
      <Icon name="i-heroicons-clock" class="w-4 h-4 text-gray-400" />
      <span class="text-sm text-gray-500">Not tested yet</span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  result: {
    type: Object,
    default: null
  }
})

const resultClasses = computed(() => {
  if (!props.result) return ''
  
  return props.result.success 
    ? 'border-green-200 bg-green-50' 
    : 'border-red-200 bg-red-50'
})

const formatTime = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}
</script>
