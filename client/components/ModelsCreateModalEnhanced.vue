<template>
  <UModal v-model="isOpen" :ui="{ width: 'sm:max-w-2xl' }">
    <form @submit.prevent="createModel" class="p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Add New Model</h3>
        <UButton
          @click="isOpen = false"
          variant="ghost"
          icon="i-heroicons-x-mark"
          size="sm"
        />
      </div>

      <div class="space-y-6">
        <!-- Basic Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="Model ID" required>
            <UInput
              v-model="form.model_id"
              placeholder="e.g., gpt-4o-mini"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>

          <UFormGroup label="Model Name" required>
            <UInput
              v-model="form.name"
              placeholder="e.g., GPT-4 Omni Mini"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="Provider" required>
            <USelectMenu
              v-model="form.provider_name"
              :options="providerOptions"
              placeholder="Select provider"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>

          <UFormGroup label="Category" required>
            <USelectMenu
              v-model="form.category_name"
              :options="categoryOptions"
              placeholder="Select category"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>
        </div>

        <!-- Technical Configuration -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Technical Configuration</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="Context Length" required>
              <UInput
                v-model.number="form.context_length"
                type="number"
                placeholder="e.g., 128000"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>

            <UFormGroup label="Max Output Tokens">
              <UInput
                v-model.number="form.max_tokens"
                type="number"
                placeholder="e.g., 4096"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Capabilities -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Capabilities</h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <UCheckbox
              v-model="form.supports_streaming"
              label="Streaming"
              :disabled="modelsStore.loading.create"
            />
            <UCheckbox
              v-model="form.supports_function_calling"
              label="Function Calling"
              :disabled="modelsStore.loading.create"
            />
            <UCheckbox
              v-model="form.supports_vision"
              label="Vision"
              :disabled="modelsStore.loading.create"
            />
            <UCheckbox
              v-model="form.enabled"
              label="Enabled"
              :disabled="modelsStore.loading.create"
            />
          </div>
        </div>

        <!-- Pricing -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Pricing (per 1M tokens)</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="Input Cost ($)" required>
              <UInput
                v-model.number="form.input_cost_per_1m"
                type="number"
                step="0.01"
                placeholder="e.g., 0.15"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>

            <UFormGroup label="Output Cost ($)" required>
              <UInput
                v-model.number="form.output_cost_per_1m"
                type="number"
                step="0.01"
                placeholder="e.g., 0.60"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Default Parameters -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Default Parameters</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <UFormGroup label="Temperature">
              <UInput
                v-model.number="form.default_temperature"
                type="number"
                step="0.1"
                min="0"
                max="2"
                placeholder="e.g., 0.1"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>

            <UFormGroup label="Top P">
              <UInput
                v-model.number="form.default_top_p"
                type="number"
                step="0.1"
                min="0"
                max="1"
                placeholder="e.g., 0.9"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>

            <UFormGroup label="Default Max Tokens">
              <UInput
                v-model.number="form.default_max_tokens"
                type="number"
                placeholder="e.g., 2048"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Features -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Features</h4>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="(feature, index) in form.features"
              :key="index"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {{ feature }}
              <button
                @click="removeFeature(index)"
                type="button"
                class="ml-1 text-blue-400 hover:text-blue-600"
              >
                <Icon name="i-heroicons-x-mark" class="w-3 h-3" />
              </button>
            </span>
          </div>
          <div class="mt-2 flex items-center gap-2">
            <UInput
              v-model="newFeature"
              placeholder="Add feature..."
              @keyup.enter="addFeature"
              size="sm"
              class="flex-1"
            />
            <UButton
              @click="addFeature"
              size="sm"
              variant="outline"
              icon="i-heroicons-plus"
            >
              Add
            </UButton>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
        <UButton
          @click="isOpen = false"
          variant="outline"
          :disabled="modelsStore.loading.create"
        >
          Cancel
        </UButton>
        <UButton
          type="submit"
          :loading="modelsStore.loading.create"
          icon="i-heroicons-plus"
        >
          Create Model
        </UButton>
      </div>
    </form>
  </UModal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
// @ts-ignore
import { useModelsEnhancedStore } from '../stores/models-enhanced'
// @ts-ignore  
import type { CreateModelRequest } from '../types/models'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'created': []
}>()

// Stores
const modelsStore = useModelsStore()
// @ts-ignore
const toast = useToast()

// State
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = ref<CreateModelRequest>({
  model_id: '',
  name: '',
  provider_name: '',
  category_name: '',
  context_length: 0,
  max_tokens: undefined,
  supports_streaming: true,
  supports_function_calling: false,
  supports_vision: false,
  input_cost_per_1m: 0,
  output_cost_per_1m: 0,
  default_temperature: 0.1,
  default_top_p: 0.9,
  default_max_tokens: undefined,
  enabled: true,
  features: [],
  metadata: {}
})

const newFeature = ref('')

// Computed
const providerOptions = computed(() => 
  modelsStore.providers.map(p => ({
    label: p.display_name,
    value: p.name
  }))
)

const categoryOptions = computed(() => 
  modelsStore.categories.map(c => ({
    label: c.display_name,
    value: c.name
  }))
)

// Methods
const addFeature = () => {
  if (newFeature.value.trim() && form.value.features && !form.value.features.includes(newFeature.value.trim())) {
    form.value.features.push(newFeature.value.trim())
    newFeature.value = ''
  }
}

const removeFeature = (index: number) => {
  if (form.value.features) {
    form.value.features.splice(index, 1)
  }
}

const resetForm = () => {
  form.value = {
    model_id: '',
    name: '',
    provider_name: '',
    category_name: '',
    context_length: 0,
    max_tokens: undefined,
    supports_streaming: true,
    supports_function_calling: false,
    supports_vision: false,
    input_cost_per_1m: 0,
    output_cost_per_1m: 0,
    default_temperature: 0.1,
    default_top_p: 0.9,
    default_max_tokens: undefined,
    enabled: true,
    features: [],
    metadata: {}
  }
  newFeature.value = ''
}

const createModel = async () => {
  try {
    // Validación básica
    if (!form.value.model_id || !form.value.name || !form.value.provider_name || 
        !form.value.category_name || !form.value.context_length) {
      toast.add({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        color: 'red'
      })
      return
    }

    await modelsStore.createModel(form.value)
    
    toast.add({
      title: 'Success',
      description: `Model ${form.value.model_id} created successfully`,
      color: 'green'
    })
    
    emit('created')
    isOpen.value = false
    resetForm()
    
  } catch (error: any) {
    toast.add({
      title: 'Error',
      description: error.message || 'Failed to create model',
      color: 'red'
    })
  }
}

// Watchers
watch(isOpen, (newValue) => {
  if (newValue) {
    // Cargar datos necesarios cuando se abre el modal
    if (modelsStore.providers.length === 0) {
      modelsStore.fetchProviders(true)
    }
    if (modelsStore.categories.length === 0) {
      modelsStore.fetchCategories(true)
    }
  } else {
    resetForm()
  }
})
</script>
