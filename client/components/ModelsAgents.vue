<template>
  <div class="py-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">Agent Model Configuration</h3>
        <p class="text-sm text-gray-600">Configura qué modelo usa cada tipo de agente IA</p>
      </div>
      
      <UButton
        @click="refreshAgents"
        :loading="modelsStore.loading.agents"
        variant="outline"
        icon="i-heroicons-arrow-path"
      >
        Refresh
      </UButton>
    </div>

    <!-- Agent Configurations -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard 
        v-for="selection in modelsStore.agentSelections" 
        :key="selection.agent_type"
        class="hover:shadow-md transition-shadow"
      >
        <div class="space-y-4">
          <!-- Agent Header -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <span class="text-blue-600 text-lg">{{ getAgentIcon(selection.agent_type) }}</span>
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">{{ getAgentName(selection.agent_type) }}</h4>
                <p class="text-sm text-gray-500">{{ getAgentDescription(selection.agent_type) }}</p>
              </div>
            </div>
            
            <UBadge 
              :color="selection.enabled ? 'green' : 'red'"
              variant="soft"
            >
              {{ selection.enabled ? 'Active' : 'Inactive' }}
            </UBadge>
          </div>

          <!-- Current Model -->
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Primary Model
              </label>
              <USelectMenu
                :model-value="selection.model_id"
                :options="getModelOptions(selection.agent_type)"
                @update:model-value="(value) => updateAgentModel(selection.agent_type, value, selection.fallback_model_id)"
                placeholder="Select model"
              >
                <template #label>
                  <div v-if="selection.model_id" class="flex items-center space-x-2">
                    <span>{{ getModelIcon(selection.model_id) }}</span>
                    <span>{{ getModelDisplayName(selection.model_id) }}</span>
                  </div>
                  <span v-else>Select model</span>
                </template>
              </USelectMenu>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                Fallback Model (Optional)
              </label>
              <USelectMenu
                :model-value="selection.fallback_model_id || ''"
                :options="getFallbackModelOptions(selection.agent_type, selection.model_id)"
                @update:model-value="(value) => updateAgentModel(selection.agent_type, selection.model_id, value || undefined)"
                placeholder="Select fallback model"
              >
                <template #label>
                  <div v-if="selection.fallback_model_id" class="flex items-center space-x-2">
                    <span>{{ getModelIcon(selection.fallback_model_id) }}</span>
                    <span>{{ getModelDisplayName(selection.fallback_model_id) }}</span>
                  </div>
                  <span v-else>No fallback</span>
                </template>
              </USelectMenu>
            </div>
          </div>

          <!-- Model Info -->
          <div v-if="selection.model_id" class="bg-gray-50 rounded-lg p-3">
            <div class="text-xs text-gray-600 space-y-1">
              <div class="flex justify-between">
                <span>Provider:</span>
                <UBadge 
                  :color="getModelProvider(selection.model_id) ? modelsStore.getProviderColor(getModelProvider(selection.model_id)) : 'gray'"
                  variant="soft"
                  size="xs"
                >
                  {{ getModelProvider(selection.model_id) }}
                </UBadge>
              </div>
              
              <div class="flex justify-between">
                <span>Context:</span>
                <span>{{ formatContextLength(selection.model_id) }}</span>
              </div>
              
              <div class="flex justify-between">
                <span>Cost (in/out):</span>
                <span>${{ getModelPricing(selection.model_id) }}</span>
              </div>
            </div>
          </div>

          <!-- Test Button -->
          <div class="flex justify-end">
            <UButton
              @click="testAgentModel(selection)"
              :loading="testingAgent === selection.agent_type"
              size="sm"
              variant="outline"
              icon="i-heroicons-beaker"
            >
              Test Configuration
            </UButton>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Empty state -->
    <div v-if="modelsStore.agentSelections.length === 0" class="text-center py-12">
      <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
        <span class="text-gray-400 text-2xl">🤖</span>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No agent configurations</h3>
      <p class="text-gray-500 mb-4">Agent configurations will appear here automatically.</p>
      <UButton @click="refreshAgents" icon="i-heroicons-arrow-path">
        Refresh
      </UButton>
    </div>

    <!-- Bulk Actions -->
    <UCard v-if="modelsStore.agentSelections.length > 0">
      <template #header>
        <h3 class="text-lg font-semibold text-gray-900">Bulk Actions</h3>
      </template>

      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">Set all to:</label>
          <USelectMenu
            v-model="bulkModel"
            :options="chatModelOptions"
            placeholder="Select model"
            class="w-48"
          />
          <UButton
            @click="applyBulkModel"
            :disabled="!bulkModel"
            size="sm"
            variant="outline"
          >
            Apply to All
          </UButton>
        </div>

        <UButton
          @click="resetToDefaults"
          size="sm"
          variant="outline"
          icon="i-heroicons-arrow-path"
        >
          Reset to Defaults
        </UButton>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import { useModelsStore } from '~/stores/models'
import type { ModelSelectionConfig } from '~/types/models'

const modelsStore = useModelsStore()
const toast = useToast()

// State
const testingAgent = ref<string | null>(null)
const bulkModel = ref('')

// Agent metadata
const agentMetadata = {
  instruction_analyzer: {
    name: 'Instruction Analyzer',
    description: 'Analiza y comprende instrucciones de usuario',
    icon: '🔍',
    category: 'chat'
  },
  action_planner: {
    name: 'Action Planner',
    description: 'Planifica secuencias de acciones',
    icon: '📋',
    category: 'chat'
  },
  element_selector: {
    name: 'Element Selector',
    description: 'Identifica elementos en páginas web',
    icon: '🎯',
    category: 'chat'
  },
  validator: {
    name: 'Validator',
    description: 'Valida resultados y acciones',
    icon: '✅',
    category: 'chat'
  },
  self_healer: {
    name: 'Self Healer',
    description: 'Corrige errores automáticamente',
    icon: '🔧',
    category: 'chat'
  },
  rag_embeddings: {
    name: 'RAG Embeddings',
    description: 'Genera embeddings para RAG',
    icon: '🔗',
    category: 'embedding'
  },
  rag_chat: {
    name: 'RAG Chat',
    description: 'Genera respuestas basadas en contexto',
    icon: '💬',
    category: 'chat'
  }
}

// Computed
const chatModelOptions = computed(() => 
  modelsStore.chatModels
    .filter(m => m.enabled)
    .map(m => ({ 
      label: m.name, 
      value: m.id,
      icon: modelsStore.getCategoryIcon(m.category)
    }))
)

const embeddingModelOptions = computed(() => 
  modelsStore.embeddingModels
    .filter(m => m.enabled)
    .map(m => ({ 
      label: m.name, 
      value: m.id,
      icon: modelsStore.getCategoryIcon(m.category)
    }))
)

// Methods
const refreshAgents = async () => {
  try {
    await modelsStore.fetchAgentSelections()
    toast.add({
      title: 'Agent configurations refreshed',
      color: 'green'
    })
  } catch (error) {
    toast.add({
      title: 'Error refreshing agent configurations',
      description: error.message,
      color: 'red'
    })
  }
}

const getAgentName = (agentType: string) => {
  return agentMetadata[agentType as keyof typeof agentMetadata]?.name || agentType
}

const getAgentDescription = (agentType: string) => {
  return agentMetadata[agentType as keyof typeof agentMetadata]?.description || ''
}

const getAgentIcon = (agentType: string) => {
  return agentMetadata[agentType as keyof typeof agentMetadata]?.icon || '🤖'
}

const getAgentCategory = (agentType: string) => {
  return agentMetadata[agentType as keyof typeof agentMetadata]?.category || 'chat'
}

const getModelOptions = (agentType: string) => {
  const category = getAgentCategory(agentType)
  return category === 'embedding' ? embeddingModelOptions.value : chatModelOptions.value
}

const getFallbackModelOptions = (agentType: string, primaryModelId: string) => {
  const options = getModelOptions(agentType)
  return [
    { label: 'No fallback', value: '' },
    ...options.filter(opt => opt.value !== primaryModelId)
  ]
}

const getModelDisplayName = (modelId: string) => {
  const model = modelsStore.models.find(m => m.id === modelId)
  return model?.name || modelId.split('/').pop() || modelId
}

const getModelIcon = (modelId: string) => {
  const model = modelsStore.models.find(m => m.id === modelId)
  return model ? modelsStore.getCategoryIcon(model.category) : '🤖'
}

const getModelProvider = (modelId: string) => {
  const model = modelsStore.models.find(m => m.id === modelId)
  return model?.provider || ''
}

const formatContextLength = (modelId: string) => {
  const model = modelsStore.models.find(m => m.id === modelId)
  return model ? modelsStore.formatTokens(model.context_length) : 'N/A'
}

const getModelPricing = (modelId: string) => {
  const model = modelsStore.models.find(m => m.id === modelId)
  return model ? `${model.pricing.input}/${model.pricing.output}` : 'N/A'
}

const updateAgentModel = async (agentType: string, modelId: string, fallbackModelId?: string) => {
  try {
    await modelsStore.updateAgentModel(agentType, modelId, fallbackModelId)
    toast.add({
      title: 'Agent model updated',
      description: `${getAgentName(agentType)} now uses ${getModelDisplayName(modelId)}`,
      color: 'green'
    })
  } catch (error) {
    toast.add({
      title: 'Error updating agent model',
      description: error.message,
      color: 'red'
    })
  }
}

const testAgentModel = async (selection: ModelSelectionConfig) => {
  testingAgent.value = selection.agent_type
  
  try {
    const result = await modelsStore.testModel({
      model_id: selection.model_id,
      prompt: `Test prompt for ${getAgentName(selection.agent_type)}: Analyze this sample instruction.`,
      params: {
        temperature: 0.1,
        max_tokens: 100
      }
    })
    
    if (result.success) {
      toast.add({
        title: 'Model test successful',
        description: `${getAgentName(selection.agent_type)} model is working correctly`,
        color: 'green'
      })
    } else {
      toast.add({
        title: 'Model test failed',
        description: result.error || 'Unknown error',
        color: 'red'
      })
    }
  } catch (error) {
    toast.add({
      title: 'Error testing model',
      description: error.message,
      color: 'red'
    })
  } finally {
    testingAgent.value = null
  }
}

const applyBulkModel = async () => {
  if (!bulkModel.value) return
  
  const chatAgents = modelsStore.agentSelections.filter(s => getAgentCategory(s.agent_type) === 'chat')
  
  try {
    await Promise.all(
      chatAgents.map(agent => 
        modelsStore.updateAgentModel(agent.agent_type, bulkModel.value)
      )
    )
    
    toast.add({
      title: 'Bulk update successful',
      description: `Updated ${chatAgents.length} chat agents to use ${getModelDisplayName(bulkModel.value)}`,
      color: 'green'
    })
    
    bulkModel.value = ''
  } catch (error) {
    toast.add({
      title: 'Error applying bulk update',
      description: error.message,
      color: 'red'
    })
  }
}

const resetToDefaults = async () => {
  const defaultSelections = [
    { agent: 'instruction_analyzer', model: 'openai/gpt-4o-mini', fallback: 'anthropic/claude-3.5-sonnet' },
    { agent: 'action_planner', model: 'openai/gpt-4o', fallback: 'anthropic/claude-3.5-sonnet' },
    { agent: 'element_selector', model: 'openai/gpt-4o-mini', fallback: 'openai/gpt-4o' },
    { agent: 'validator', model: 'openai/gpt-4o-mini', fallback: 'anthropic/claude-3.5-sonnet' },
    { agent: 'self_healer', model: 'anthropic/claude-3.5-sonnet', fallback: 'openai/gpt-4o' },
    { agent: 'rag_embeddings', model: 'openai/text-embedding-3-small', fallback: 'cohere/embed-v4.0' },
    { agent: 'rag_chat', model: 'openai/gpt-4o-mini', fallback: 'anthropic/claude-3.5-sonnet' }
  ]
  
  try {
    await Promise.all(
      defaultSelections.map(({ agent, model, fallback }) => 
        modelsStore.updateAgentModel(agent, model, fallback)
      )
    )
    
    toast.add({
      title: 'Reset to defaults successful',
      description: 'All agent models have been reset to default configurations',
      color: 'green'
    })
  } catch (error) {
    toast.add({
      title: 'Error resetting to defaults',
      description: error.message,
      color: 'red'
    })
  }
}
</script>