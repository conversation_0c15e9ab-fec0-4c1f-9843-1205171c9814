<template>
  <UModal v-model="isOpen" :ui="{ width: 'sm:max-w-2xl' }">
    <form @submit.prevent="updateModel" class="p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Edit Model</h3>
          <p class="text-sm text-gray-500">{{ model?.id }}</p>
        </div>
        <UButton
          @click="isOpen = false"
          variant="ghost"
          icon="i-heroicons-x-mark"
          size="sm"
        />
      </div>

      <div v-if="model" class="space-y-6">
        <!-- Basic Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="Model Name">
            <UInput
              v-model="form.name"
              placeholder="e.g., GPT-4 Turbo"
              :disabled="modelsStore.loading.update"
            />
          </UFormGroup>

          <UFormGroup label="Context Length">
            <UInput
              v-model.number="form.context_length"
              type="number"
              placeholder="e.g., 8192"
              :disabled="modelsStore.loading.update"
            />
          </UFormGroup>
        </div>

        <!-- Pricing -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Pricing (per 1M tokens)</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="Input Cost ($)">
              <UInput
                v-model.number="form.pricing.input"
                type="number"
                step="0.01"
                placeholder="e.g., 2.50"
                :disabled="modelsStore.loading.update"
              />
            </UFormGroup>

            <UFormGroup label="Output Cost ($)">
              <UInput
                v-model.number="form.pricing.output"
                type="number"
                step="0.01"
                placeholder="e.g., 10.00"
                :disabled="modelsStore.loading.update"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Default Parameters -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Default Parameters</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <UFormGroup label="Temperature">
              <UInput
                v-model.number="form.default_params.temperature"
                type="number"
                step="0.1"
                min="0"
                max="2"
                placeholder="e.g., 0.1"
                :disabled="modelsStore.loading.update"
              />
            </UFormGroup>

            <UFormGroup label="Max Tokens">
              <UInput
                v-model.number="form.default_params.max_tokens"
                type="number"
                min="1"
                placeholder="e.g., 2000"
                :disabled="modelsStore.loading.update"
              />
            </UFormGroup>

            <UFormGroup label="Top P">
              <UInput
                v-model.number="form.default_params.top_p"
                type="number"
                step="0.1"
                min="0"
                max="1"
                placeholder="e.g., 0.9"
                :disabled="modelsStore.loading.update"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Features -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Features</h4>
          <div class="space-y-2">
            <div 
              v-for="(feature, index) in form.features" 
              :key="index"
              class="flex items-center space-x-3"
            >
              <UInput
                v-model="feature.name"
                placeholder="Feature name"
                class="flex-1"
                :disabled="modelsStore.loading.update"
              />
              
              <UToggle
                v-model="feature.supported"
                :disabled="modelsStore.loading.update"
              />
              
              <UButton
                @click="removeFeature(index)"
                variant="ghost"
                icon="i-heroicons-trash"
                size="sm"
                :disabled="modelsStore.loading.update"
              />
            </div>
            
            <UButton
              @click="addFeature"
              variant="outline"
              icon="i-heroicons-plus"
              size="sm"
              :disabled="modelsStore.loading.update"
            >
              Add Feature
            </UButton>
          </div>
        </div>

        <!-- Status -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <UToggle
              v-model="form.enabled"
              :disabled="modelsStore.loading.update"
            />
            <label class="text-sm font-medium text-gray-900">
              Enable this model
            </label>
          </div>

          <UButton
            @click="confirmDelete"
            variant="soft"
            color="red"
            icon="i-heroicons-trash"
            :disabled="modelsStore.loading.update || modelsStore.loading.delete"
            :loading="modelsStore.loading.delete"
          >
            Delete Model
          </UButton>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
        <UButton
          @click="isOpen = false"
          variant="outline"
          :disabled="modelsStore.loading.update"
        >
          Cancel
        </UButton>
        
        <UButton
          type="submit"
          :loading="modelsStore.loading.update"
          icon="i-heroicons-check"
        >
          Update Model
        </UButton>
      </div>
    </form>

    <!-- Delete Confirmation Modal -->
    <UModal v-model="showDeleteConfirm">
      <div class="p-6">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
            <span class="text-red-600 text-xl">⚠️</span>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Delete Model</h3>
            <p class="text-sm text-gray-600">This action cannot be undone.</p>
          </div>
        </div>

        <p class="text-sm text-gray-700 mb-6">
          Are you sure you want to delete <strong>{{ model?.name }}</strong>? 
          This will remove the model and any agent configurations using it.
        </p>

        <div class="flex justify-end space-x-3">
          <UButton
            @click="showDeleteConfirm = false"
            variant="outline"
            :disabled="modelsStore.loading.delete"
          >
            Cancel
          </UButton>
          
          <UButton
            @click="deleteModel"
            color="red"
            :loading="modelsStore.loading.delete"
            icon="i-heroicons-trash"
          >
            Delete Model
          </UButton>
        </div>
      </div>
    </UModal>
  </UModal>
</template>

<script setup lang="ts">
import { useModelsStore } from '~/stores/models'
import type { ModelConfig, UpdateModelRequest } from '~/types/models'

// Props
interface Props {
  modelValue: boolean
  model: ModelConfig | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'updated': []
}>()

// Stores
const modelsStore = useModelsStore()
const toast = useToast()

// State
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showDeleteConfirm = ref(false)

const form = ref<UpdateModelRequest>({
  name: '',
  pricing: {
    input: 0,
    output: 0
  },
  context_length: 0,
  features: [],
  enabled: true,
  default_params: {}
})

// Methods
const loadModelData = () => {
  if (props.model) {
    form.value = {
      name: props.model.name,
      pricing: { ...props.model.pricing },
      context_length: props.model.context_length,
      features: [...props.model.features],
      enabled: props.model.enabled,
      default_params: { ...props.model.default_params }
    }
  }
}

const addFeature = () => {
  if (!form.value.features) {
    form.value.features = []
  }
  form.value.features.push({
    name: '',
    supported: true,
    description: ''
  })
}

const removeFeature = (index: number) => {
  form.value.features?.splice(index, 1)
}

const validateForm = (): string | null => {
  if (form.value.name && !form.value.name.trim()) {
    return 'Model name cannot be empty'
  }
  
  if (form.value.pricing) {
    if (form.value.pricing.input < 0 || form.value.pricing.output < 0) {
      return 'Pricing must be non-negative'
    }
  }
  
  if (form.value.context_length && form.value.context_length <= 0) {
    return 'Context length must be positive'
  }
  
  // Validate features
  if (form.value.features) {
    for (const feature of form.value.features) {
      if (!feature.name.trim()) {
        return 'All feature names must be provided'
      }
    }
  }
  
  // Validate default params
  if (form.value.default_params?.temperature !== undefined) {
    if (form.value.default_params.temperature < 0 || form.value.default_params.temperature > 2) {
      return 'Temperature must be between 0 and 2'
    }
  }
  
  if (form.value.default_params?.max_tokens !== undefined) {
    if (form.value.default_params.max_tokens <= 0) {
      return 'Max tokens must be positive'
    }
  }
  
  if (form.value.default_params?.top_p !== undefined) {
    if (form.value.default_params.top_p < 0 || form.value.default_params.top_p > 1) {
      return 'Top P must be between 0 and 1'
    }
  }
  
  return null
}

const updateModel = async () => {
  if (!props.model) return
  
  const error = validateForm()
  if (error) {
    toast.add({
      title: 'Validation Error',
      description: error,
      color: 'red'
    })
    return
  }
  
  try {
    await modelsStore.updateModel(props.model.id, form.value)
    emit('updated')
    isOpen.value = false
  } catch (error) {
    toast.add({
      title: 'Error updating model',
      description: error.message,
      color: 'red'
    })
  }
}

const confirmDelete = () => {
  showDeleteConfirm.value = true
}

const deleteModel = async () => {
  if (!props.model) return
  
  try {
    await modelsStore.deleteModel(props.model.id)
    emit('updated')
    showDeleteConfirm.value = false
    isOpen.value = false
    
    toast.add({
      title: 'Model deleted',
      description: `${props.model.name} has been deleted successfully`,
      color: 'green'
    })
  } catch (error) {
    toast.add({
      title: 'Error deleting model',
      description: error.message,
      color: 'red'
    })
  }
}

// Watch for model changes to reload data
watch(() => props.model, () => {
  if (props.model && isOpen.value) {
    loadModelData()
  }
}, { immediate: true })

// Watch for modal open to load data
watch(isOpen, (newValue) => {
  if (newValue && props.model) {
    loadModelData()
  }
})
</script>