<template>
  <div class="bg-white rounded-lg shadow-sm border">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">Story Mejorada</h3>
        <div class="flex items-center space-x-2">
          <span 
            :class="['px-2 py-1 rounded-full text-xs font-medium', complexityColorClasses]"
          >
            {{ complexityLabel }}
          </span>
          <button
            @click="copyToClipboard"
            class="text-gray-400 hover:text-gray-600"
            title="Copiar story mejorada"
          >
            <ClipboardIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
    
    <div class="p-6 space-y-6">
      <!-- Comparison -->
      <div class="grid grid-cols-1 gap-4">
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-2">Original</h4>
          <div class="bg-gray-50 rounded-md p-3 text-sm text-gray-700">
            {{ story.original }}
          </div>
        </div>
        
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-2">Mejorada</h4>
          <div class="bg-green-50 border border-green-200 rounded-md p-3 text-sm text-gray-700">
            {{ story.improved }}
          </div>
        </div>
      </div>

      <!-- Acceptance Criteria -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">Criterios de Aceptación</h4>
        <ul class="space-y-2">
          <li 
            v-for="(criteria, index) in story.acceptanceCriteria" 
            :key="index"
            class="flex items-start space-x-3"
          >
            <div class="flex-shrink-0 w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
              <span class="text-xs font-medium text-blue-600">{{ index + 1 }}</span>
            </div>
            <p class="text-sm text-gray-700">{{ criteria }}</p>
          </li>
        </ul>
      </div>

      <!-- Definition of Done -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">Definition of Done</h4>
        <ul class="space-y-2">
          <li 
            v-for="(item, index) in story.definitionOfDone" 
            :key="index"
            class="flex items-center space-x-3"
          >
            <CheckCircleIcon class="w-4 h-4 text-green-500 flex-shrink-0" />
            <p class="text-sm text-gray-700">{{ item }}</p>
          </li>
        </ul>
      </div>

      <!-- Suggested Test Cases -->
      <div>
        <h4 class="text-sm font-medium text-gray-900 mb-3">Test Cases Sugeridos</h4>
        <ul class="space-y-2">
          <li 
            v-for="(testCase, index) in story.suggestedTestCases" 
            :key="index"
            class="flex items-center space-x-3"
          >
            <BeakerIcon class="w-4 h-4 text-purple-500 flex-shrink-0" />
            <p class="text-sm text-gray-700">{{ testCase }}</p>
          </li>
        </ul>
      </div>

      <!-- Context -->
      <div v-if="story.context">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Contexto</h4>
        <div class="bg-blue-50 border border-blue-200 rounded-md p-3 text-sm text-gray-700">
          {{ story.context }}
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-between pt-4 border-t border-gray-200">
        <div class="flex items-center space-x-4 text-xs text-gray-500">
          <span>Complejidad: {{ complexityLabel }}</span>
          <span>•</span>
          <span>{{ story.acceptanceCriteria.length }} criterios</span>
          <span>•</span>
          <span>{{ story.definitionOfDone.length }} items DoD</span>
        </div>
        
        <div class="flex items-center space-x-2">
          <button
            @click="exportStory"
            class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowDownTrayIcon class="w-3 h-3 mr-1" />
            Exportar
          </button>
          
          <button
            @click="useAsInput"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowPathIcon class="w-3 h-3 mr-1" />
            Usar como input
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRAGStore } from '~/stores/rag'
import type { ImprovedStory } from '~/stores/rag'
import { 
  ClipboardIcon, 
  CheckCircleIcon, 
  BeakerIcon, 
  ArrowDownTrayIcon,
  ArrowPathIcon
} from '@heroicons/vue/24/outline'

// Props
interface Props {
  story: ImprovedStory
}

const props = defineProps<Props>()

// Store
const ragStore = useRAGStore()

// Computed
const complexityLabel = computed(() => {
  switch (props.story.estimatedComplexity) {
    case 'low': return 'Baja'
    case 'medium': return 'Media'
    case 'high': return 'Alta'
    default: return 'Desconocida'
  }
})

const complexityColorClasses = computed(() => {
  switch (props.story.estimatedComplexity) {
    case 'low': return 'bg-green-100 text-green-800'
    case 'medium': return 'bg-yellow-100 text-yellow-800'
    case 'high': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
})

// Methods
const copyToClipboard = async () => {
  const text = formatStoryForClipboard()
  
  try {
    await navigator.clipboard.writeText(text)
    // TODO: Show toast notification
    console.log('Story copied to clipboard')
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
  }
}

const formatStoryForClipboard = () => {
  return `# User Story

**Original:** ${props.story.original}

**Mejorada:** ${props.story.improved}

## Criterios de Aceptación
${props.story.acceptanceCriteria.map((criteria, index) => `${index + 1}. ${criteria}`).join('\n')}

## Definition of Done
${props.story.definitionOfDone.map(item => `- ${item}`).join('\n')}

## Test Cases Sugeridos  
${props.story.suggestedTestCases.map(testCase => `- ${testCase}`).join('\n')}

**Complejidad:** ${complexityLabel.value}

${props.story.context ? `**Contexto:** ${props.story.context}` : ''}
`
}

const exportStory = () => {
  const text = formatStoryForClipboard()
  const blob = new Blob([text], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  
  const a = document.createElement('a')
  a.href = url
  a.download = `story-${Date.now()}.md`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const useAsInput = () => {
  ragStore.setCurrentStory(props.story.improved)
}
</script>