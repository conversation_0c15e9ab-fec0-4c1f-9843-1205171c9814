<template>
  <div class="bg-white rounded-lg shadow-sm border">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Historial de Stories</h3>
    </div>
    
    <div class="p-6">
      <div class="space-y-4">
        <div 
          v-for="(story, index) in history" 
          :key="index"
          class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors cursor-pointer"
          @click="selectStory(story)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ story.improved }}
              </p>
              <p class="text-xs text-gray-500 mt-1 truncate">
                Original: {{ story.original }}
              </p>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <span 
                :class="['px-2 py-1 rounded-full text-xs font-medium', getComplexityColorClasses(story.estimatedComplexity)]"
              >
                {{ getComplexityLabel(story.estimatedComplexity) }}
              </span>
            </div>
          </div>
          
          <div class="mt-3 flex items-center justify-between text-xs text-gray-500">
            <div class="flex items-center space-x-4">
              <span>{{ story.acceptanceCriteria.length }} criterios</span>
              <span>{{ story.definitionOfDone.length }} DoD</span>
              <span>{{ story.suggestedTestCases.length }} tests</span>
            </div>
            <button
              @click.stop="useStory(story)"
              class="text-blue-600 hover:text-blue-800 font-medium"
            >
              Usar
            </button>
          </div>
        </div>
      </div>
      
      <div v-if="history.length === 0" class="text-center py-8">
        <ClockIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p class="text-sm text-gray-500">No hay historial de stories</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRAGStore } from '~/stores/rag'
import type { ImprovedStory } from '~/stores/rag'
import { ClockIcon } from '@heroicons/vue/24/outline'

// Props
interface Props {
  history: ImprovedStory[]
}

const props = defineProps<Props>()

// Store
const ragStore = useRAGStore()

// Methods
const getComplexityLabel = (complexity: string) => {
  switch (complexity) {
    case 'low': return 'Baja'
    case 'medium': return 'Media'
    case 'high': return 'Alta'
    default: return 'N/A'
  }
}

const getComplexityColorClasses = (complexity: string) => {
  switch (complexity) {
    case 'low': return 'bg-green-100 text-green-800'
    case 'medium': return 'bg-yellow-100 text-yellow-800'
    case 'high': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const selectStory = (story: ImprovedStory) => {
  ragStore.improvedStory = story
}

const useStory = (story: ImprovedStory) => {
  ragStore.setCurrentStory(story.improved)
}
</script>