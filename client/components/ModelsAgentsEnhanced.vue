<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900">Agent Model Configuration</h3>
        <p class="text-sm text-gray-600">Configure which models to use for each system agent and functionality</p>
      </div>
      
      <div class="flex items-center space-x-3">
        <UButton 
          @click="refreshData"
          :loading="modelsStore.loading.agents"
          variant="outline"
          icon="i-heroicons-arrow-path"
          size="sm"
        >
          Refresh
        </UButton>
        
        <UButton 
          @click="showBulkConfigModal = true"
          icon="i-heroicons-cog-6-tooth"
          size="sm"
        >
          Bulk Configure
        </UButton>
      </div>
    </div>

    <!-- Filter and Search -->
    <div class="flex flex-wrap items-center gap-4">
      <div class="flex-1">
        <UInput
          v-model="searchQuery"
          placeholder="Search agents..."
          icon="i-heroicons-magnifying-glass"
        />
      </div>
      
      <USelectMenu
        v-model="statusFilter"
        :options="statusOptions"
        placeholder="All Status"
      />
    </div>

    <!-- Agents Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard
        v-for="agent in filteredAgents"
        :key="agent.id"
        class="hover:shadow-md transition-shadow"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div 
                  class="w-10 h-10 rounded-lg flex items-center justify-center"
                  :class="getAgentColorClasses(agent.agent_type)"
                >
                  <span class="text-lg">{{ getAgentIcon(agent.agent_type) }}</span>
                </div>
              </div>
              <div>
                <h4 class="font-semibold text-gray-900">{{ agent.display_name }}</h4>
                <p class="text-sm text-gray-500">{{ agent.agent_type }}</p>
              </div>
            </div>
            
            <UBadge 
              :color="agent.enabled ? 'green' : 'red'"
              variant="soft"
            >
              {{ agent.enabled ? 'Enabled' : 'Disabled' }}
            </UBadge>
          </div>
        </template>

        <div class="space-y-4">
          <!-- Description -->
          <p class="text-sm text-gray-600">{{ agent.description }}</p>

          <!-- Primary Model -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Primary Model</label>
            <USelectMenu
              v-model="agent.primary_model_id"
              :options="modelOptions"
              placeholder="Select primary model"
              @change="updateAgentModel(agent)"
            >
              <template #label>
                <div v-if="getPrimaryModel(agent)" class="flex items-center space-x-2">
                  <span class="text-xs px-1.5 py-0.5 rounded text-white"
                        :style="{ backgroundColor: getModelProviderColor(getPrimaryModel(agent)!) }">
                    {{ getModelProviderName(getPrimaryModel(agent)!) }}
                  </span>
                  <span>{{ getPrimaryModel(agent)!.name }}</span>
                </div>
                <span v-else class="text-gray-400">Select primary model</span>
              </template>
            </USelectMenu>
          </div>

          <!-- Fallback Model -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">Fallback Model</label>
            <USelectMenu
              v-model="agent.fallback_model_id"
              :options="fallbackModelOptions(agent.primary_model_id)"
              placeholder="Select fallback model (optional)"
              @change="updateAgentModel(agent)"
            >
              <template #label>
                <div v-if="getFallbackModel(agent)" class="flex items-center space-x-2">
                  <span class="text-xs px-1.5 py-0.5 rounded text-white"
                        :style="{ backgroundColor: getModelProviderColor(getFallbackModel(agent)!) }">
                    {{ getModelProviderName(getFallbackModel(agent)!) }}
                  </span>
                  <span>{{ getFallbackModel(agent)!.name }}</span>
                </div>
                <span v-else class="text-gray-400">No fallback</span>
              </template>
            </USelectMenu>
          </div>

          <!-- Model Parameters -->
          <div class="grid grid-cols-2 gap-3">
            <UFormGroup label="Temperature" size="sm">
              <UInput
                v-model.number="agent.temperature"
                type="number"
                step="0.1"
                min="0"
                max="2"
                size="sm"
                @blur="updateAgentModel(agent)"
              />
            </UFormGroup>
            
            <UFormGroup label="Max Tokens" size="sm">
              <UInput
                v-model.number="agent.max_tokens"
                type="number"
                size="sm"
                @blur="updateAgentModel(agent)"
              />
            </UFormGroup>
          </div>

          <!-- System Prompt -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700">System Prompt</label>
            <UTextarea
              v-model="agent.system_prompt"
              rows="3"
              placeholder="Custom system prompt for this agent..."
              @blur="updateAgentModel(agent)"
            />
          </div>
        </div>

        <template #footer>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <UButton
                @click="testAgent(agent)"
                size="xs"
                variant="outline"
                icon="i-heroicons-beaker"
                :loading="testingAgent === agent.id"
              >
                Test
              </UButton>
              
              <UButton
                @click="openAdvancedConfig(agent)"
                size="xs"
                variant="ghost"
                icon="i-heroicons-adjustments-horizontal"
              >
                Advanced
              </UButton>
            </div>
            
            <UToggle
              v-model="agent.enabled"
              @change="updateAgentModel(agent)"
            />
          </div>
        </template>
      </UCard>
    </div>

    <!-- Empty State -->
    <div v-if="filteredAgents.length === 0" class="text-center py-12">
      <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
        <span class="text-gray-400 text-2xl">🤖</span>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No agents found</h3>
      <p class="text-gray-500 mb-4">Try adjusting your search or filters.</p>
    </div>

    <!-- Bulk Configuration Modal -->
    <UModal v-model="showBulkConfigModal" :ui="{ width: 'sm:max-w-2xl' }">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">Bulk Model Configuration</h3>
          <UButton
            @click="showBulkConfigModal = false"
            variant="ghost"
            icon="i-heroicons-x-mark"
            size="sm"
          />
        </div>

        <div class="space-y-6">
          <!-- Apply to All -->
          <div class="p-4 bg-blue-50 rounded-lg">
            <h4 class="font-medium text-blue-900 mb-3">Apply to All Agents</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <UFormGroup label="Primary Model">
                <USelectMenu
                  v-model="bulkConfig.primary_model_id"
                  :options="modelOptions"
                  placeholder="Select model"
                />
              </UFormGroup>
              
              <UFormGroup label="Fallback Model">
                <USelectMenu
                  v-model="bulkConfig.fallback_model_id"
                  :options="modelOptions"
                  placeholder="Select fallback"
                />
              </UFormGroup>
            </div>
            
            <div class="flex items-center justify-between mt-4">
              <UCheckbox
                v-model="bulkConfig.applyParameters"
                label="Also apply parameters below"
              />
              
              <UButton
                @click="applyBulkConfig"
                :loading="applyingBulk"
                size="sm"
              >
                Apply to All
              </UButton>
            </div>
          </div>

          <!-- Parameters -->
          <div v-if="bulkConfig.applyParameters" class="grid grid-cols-2 gap-4">
            <UFormGroup label="Temperature">
              <UInput
                v-model.number="bulkConfig.temperature"
                type="number"
                step="0.1"
                min="0"
                max="2"
              />
            </UFormGroup>
            
            <UFormGroup label="Max Tokens">
              <UInput
                v-model.number="bulkConfig.max_tokens"
                type="number"
              />
            </UFormGroup>
          </div>

          <!-- Presets -->
          <div>
            <h4 class="font-medium text-gray-900 mb-3">Quick Presets</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <UButton
                v-for="preset in configPresets"
                :key="preset.name"
                @click="applyPreset(preset)"
                variant="outline"
                block
              >
                <div class="text-left">
                  <div class="font-medium">{{ preset.name }}</div>
                  <div class="text-xs text-gray-500">{{ preset.description }}</div>
                </div>
              </UButton>
            </div>
          </div>
        </div>
      </div>
    </UModal>

    <!-- Advanced Configuration Modal -->
    <UModal v-model="showAdvancedModal" :ui="{ width: 'sm:max-w-3xl' }">
      <div class="p-6" v-if="selectedAgent">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">
            Advanced Configuration - {{ selectedAgent.display_name }}
          </h3>
          <UButton
            @click="showAdvancedModal = false"
            variant="ghost"
            icon="i-heroicons-x-mark"
            size="sm"
          />
        </div>

        <UTabs :items="advancedTabs">
          <template #parameters="{ item }">
            <div class="space-y-4 py-4">
              <div class="grid grid-cols-2 gap-4">
                <UFormGroup label="Temperature">
                  <UInput
                    v-model.number="selectedAgent.temperature"
                    type="number"
                    step="0.01"
                    min="0"
                    max="2"
                  />
                  <template #help>
                    Controls randomness. Lower = more focused, Higher = more creative
                  </template>
                </UFormGroup>
                
                <UFormGroup label="Top P">
                  <UInput
                    v-model.number="selectedAgent.top_p"
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                  />
                  <template #help>
                    Nucleus sampling. Controls diversity of responses
                  </template>
                </UFormGroup>
              </div>
              
              <UFormGroup label="Max Tokens">
                <UInput
                  v-model.number="selectedAgent.max_tokens"
                  type="number"
                  min="1"
                />
                <template #help>
                  Maximum tokens in the response
                </template>
              </UFormGroup>
            </div>
          </template>

          <template #prompt="{ item }">
            <div class="py-4">
              <UFormGroup label="System Prompt">
                <UTextarea
                  v-model="selectedAgent.system_prompt"
                  rows="10"
                  placeholder="Enter system prompt for this agent..."
                />
                <template #help>
                  Defines the agent's behavior and context
                </template>
              </UFormGroup>
            </div>
          </template>

          <template #testing="{ item }">
            <div class="space-y-4 py-4">
              <UFormGroup label="Test Prompt">
                <UTextarea
                  v-model="testPrompt"
                  rows="4"
                  placeholder="Enter a test prompt to validate this agent's configuration..."
                />
              </UFormGroup>
              
              <UButton
                @click="runAgentTest"
                :loading="testingAgent === selectedAgent.id"
                icon="i-heroicons-play"
              >
                Run Test
              </UButton>
              
              <div v-if="testResult" class="p-4 bg-gray-50 rounded-lg">
                <h4 class="font-medium mb-2">Test Result:</h4>
                <pre class="text-sm whitespace-pre-wrap">{{ testResult }}</pre>
              </div>
            </div>
          </template>
        </UTabs>

        <div class="flex justify-end space-x-3 mt-6 pt-6 border-t">
          <UButton
            @click="showAdvancedModal = false"
            variant="outline"
          >
            Cancel
          </UButton>
          <UButton
            @click="saveAdvancedConfig"
            :loading="modelsStore.loading.agents"
          >
            Save Changes
          </UButton>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useModelsEnhancedStore } from '~/stores/models-enhanced'
import type { AgentConfiguration, ModelConfig } from '~/types/models'

// Stores
const modelsStore = useModelsEnhancedStore()
const toast = useToast()

// State
const searchQuery = ref('')
const statusFilter = ref('')
const showBulkConfigModal = ref(false)
const showAdvancedModal = ref(false)
const selectedAgent = ref<AgentConfiguration | null>(null)
const testingAgent = ref<string | null>(null)
const applyingBulk = ref(false)
const testPrompt = ref('')
const testResult = ref('')

// Bulk configuration
const bulkConfig = ref({
  primary_model_id: '',
  fallback_model_id: '',
  temperature: 0.1,
  max_tokens: 2048,
  applyParameters: false
})

// Configuration presets
const configPresets = [
  {
    name: 'Performance Optimized',
    description: 'Fast, cost-effective models',
    config: {
      primary_model_id: 'gpt-4o-mini',
      fallback_model_id: 'claude-3-haiku-20240307',
      temperature: 0.1,
      max_tokens: 2048
    }
  },
  {
    name: 'Quality Focused',
    description: 'Best available models',
    config: {
      primary_model_id: 'claude-3-5-sonnet-20241022',
      fallback_model_id: 'gpt-4o',
      temperature: 0.1,
      max_tokens: 4096
    }
  },
  {
    name: 'Balanced',
    description: 'Good balance of cost and quality',
    config: {
      primary_model_id: 'gpt-4o',
      fallback_model_id: 'gpt-4o-mini',
      temperature: 0.1,
      max_tokens: 2048
    }
  }
]

// Filter options
const statusOptions = [
  { label: 'All Status', value: '' },
  { label: 'Enabled', value: 'enabled' },
  { label: 'Disabled', value: 'disabled' }
]

// Advanced modal tabs
const advancedTabs = [
  { key: 'parameters', label: 'Parameters', icon: 'i-heroicons-adjustments-horizontal' },
  { key: 'prompt', label: 'System Prompt', icon: 'i-heroicons-chat-bubble-left-right' },
  { key: 'testing', label: 'Testing', icon: 'i-heroicons-beaker' }
]

// Computed
const filteredAgents = computed(() => {
  let agents = modelsStore.agentConfigurations

  // Filter by search
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    agents = agents.filter(agent => 
      agent.display_name.toLowerCase().includes(query) ||
      agent.agent_type.toLowerCase().includes(query) ||
      agent.description.toLowerCase().includes(query)
    )
  }

  // Filter by status
  if (statusFilter.value) {
    const enabled = statusFilter.value === 'enabled'
    agents = agents.filter(agent => agent.enabled === enabled)
  }

  return agents
})

const modelOptions = computed(() => 
  modelsStore.enabledModels.map(model => ({
    label: model.name,
    value: model.id,
    model: model
  }))
)

const fallbackModelOptions = computed(() => (primaryModelId: string) =>
  modelsStore.enabledModels
    .filter(model => model.id !== primaryModelId)
    .map(model => ({
      label: model.name,
      value: model.id,
      model: model
    }))
)

// Methods
const refreshData = async () => {
  try {
    await Promise.all([
      modelsStore.fetchAgentConfigurations(),
      modelsStore.fetchModels(undefined, undefined, true), // solo habilitados
      modelsStore.fetchProviders(true),
      modelsStore.fetchCategories(true)
    ])
  } catch (error: any) {
    toast.add({
      title: 'Error refreshing data',
      description: error.message,
      color: 'red'
    })
  }
}

const updateAgentModel = async (agent: AgentConfiguration) => {
  try {
    await modelsStore.updateAgentConfiguration(agent.agent_type, {
      primary_model_id: agent.primary_model_id,
      fallback_model_id: agent.fallback_model_id,
      temperature: agent.temperature,
      max_tokens: agent.max_tokens,
      top_p: agent.top_p,
      system_prompt: agent.system_prompt,
      enabled: agent.enabled
    })

    toast.add({
      title: 'Configuration updated',
      description: `${agent.display_name} configuration saved`,
      color: 'green'
    })
  } catch (error: any) {
    toast.add({
      title: 'Error updating configuration',
      description: error.message,
      color: 'red'
    })
  }
}

const testAgent = async (agent: AgentConfiguration) => {
  testingAgent.value = agent.id
  try {
    // Implementar test del agente
    await new Promise(resolve => setTimeout(resolve, 2000)) // Simular test
    
    toast.add({
      title: 'Test completed',
      description: `${agent.display_name} is working correctly`,
      color: 'green'
    })
  } catch (error: any) {
    toast.add({
      title: 'Test failed',
      description: error.message,
      color: 'red'
    })
  } finally {
    testingAgent.value = null
  }
}

const openAdvancedConfig = (agent: AgentConfiguration) => {
  selectedAgent.value = { ...agent }
  showAdvancedModal.value = true
}

const saveAdvancedConfig = async () => {
  if (!selectedAgent.value) return
  
  await updateAgentModel(selectedAgent.value)
  showAdvancedModal.value = false
}

const runAgentTest = async () => {
  if (!selectedAgent.value || !testPrompt.value) return
  
  testingAgent.value = selectedAgent.value.id
  try {
    const response = await modelsStore.testModel({
      model_id: selectedAgent.value.primary_model_id,
      prompt: testPrompt.value,
      temperature: selectedAgent.value.temperature,
      max_tokens: selectedAgent.value.max_tokens
    })
    
    testResult.value = response.response || 'No response received'
  } catch (error: any) {
    testResult.value = `Error: ${error.message}`
  } finally {
    testingAgent.value = null
  }
}

const applyBulkConfig = async () => {
  applyingBulk.value = true
  try {
    const promises = modelsStore.agentConfigurations.map(agent => {
      const config: any = {}
      
      if (bulkConfig.value.primary_model_id) {
        config.primary_model_id = bulkConfig.value.primary_model_id
      }
      if (bulkConfig.value.fallback_model_id) {
        config.fallback_model_id = bulkConfig.value.fallback_model_id
      }
      
      if (bulkConfig.value.applyParameters) {
        config.temperature = bulkConfig.value.temperature
        config.max_tokens = bulkConfig.value.max_tokens
      }
      
      return modelsStore.updateAgentConfiguration(agent.agent_type, config)
    })
    
    await Promise.all(promises)
    
    toast.add({
      title: 'Bulk configuration applied',
      description: 'All agents have been updated',
      color: 'green'
    })
    
    showBulkConfigModal.value = false
  } catch (error: any) {
    toast.add({
      title: 'Error applying bulk configuration',
      description: error.message,
      color: 'red'
    })
  } finally {
    applyingBulk.value = false
  }
}

const applyPreset = (preset: any) => {
  bulkConfig.value = { ...preset.config, applyParameters: true }
}

// Helper methods
const getPrimaryModel = (agent: AgentConfiguration): ModelConfig | undefined => {
  return modelsStore.models.find(m => m.id === agent.primary_model_id)
}

const getFallbackModel = (agent: AgentConfiguration): ModelConfig | undefined => {
  return agent.fallback_model_id ? 
    modelsStore.models.find(m => m.id === agent.fallback_model_id) : 
    undefined
}

const getModelProviderName = (model: ModelConfig): string => {
  const provider = modelsStore.getProviderById(model.provider_id)
  return provider?.display_name || 'Unknown'
}

const getModelProviderColor = (model: ModelConfig): string => {
  const provider = modelsStore.getProviderById(model.provider_id)
  const colors: Record<string, string> = {
    openai: '#10B981',
    anthropic: '#F59E0B',
    openrouter: '#3B82F6',
    cohere: '#8B5CF6'
  }
  return colors[provider?.name || ''] || '#6B7280'
}

const getAgentIcon = (agentType: string): string => {
  const icons: Record<string, string> = {
    instruction_analyzer: '🧠',
    action_planner: '📋',
    element_selector: '🎯',
    validator: '✅',
    self_healer: '🔧'
  }
  return icons[agentType] || '🤖'
}

const getAgentColorClasses = (agentType: string): string => {
  const classes: Record<string, string> = {
    instruction_analyzer: 'bg-blue-100 text-blue-600',
    action_planner: 'bg-green-100 text-green-600',
    element_selector: 'bg-purple-100 text-purple-600',
    validator: 'bg-emerald-100 text-emerald-600',
    self_healer: 'bg-orange-100 text-orange-600'
  }
  return classes[agentType] || 'bg-gray-100 text-gray-600'
}

// Initialize
onMounted(() => {
  refreshData()
})
</script>
