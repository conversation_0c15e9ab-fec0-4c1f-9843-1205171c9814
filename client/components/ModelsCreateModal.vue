<template>
  <UModal v-model="isOpen" :ui="{ width: 'sm:max-w-2xl' }">
    <form @submit.prevent="createModel" class="p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Add New Model</h3>
        <UButton
          @click="isOpen = false"
          variant="ghost"
          icon="i-heroicons-x-mark"
          size="sm"
        />
      </div>

      <div class="space-y-6">
        <!-- Basic Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="Model Name" required>
            <UInput
              v-model="form.name"
              placeholder="e.g., GPT-4 Turbo"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>

          <UFormGroup label="Provider" required>
            <USelectMenu
              v-model="form.provider"
              :options="providerOptions"
              placeholder="Select provider"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="Category" required>
            <USelectMenu
              v-model="form.category"
              :options="categoryOptions"
              placeholder="Select category"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>

          <UFormGroup label="Context Length" required>
            <UInput
              v-model.number="form.context_length"
              type="number"
              placeholder="e.g., 8192"
              :disabled="modelsStore.loading.create"
            />
          </UFormGroup>
        </div>

        <!-- Pricing -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Pricing (per 1M tokens)</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="Input Cost ($)" required>
              <UInput
                v-model.number="form.pricing.input"
                type="number"
                step="0.01"
                placeholder="e.g., 2.50"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>

            <UFormGroup label="Output Cost ($)" required>
              <UInput
                v-model.number="form.pricing.output"
                type="number"
                step="0.01"
                placeholder="e.g., 10.00"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Default Parameters -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Default Parameters</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <UFormGroup label="Temperature">
              <UInput
                v-model.number="form.default_params.temperature"
                type="number"
                step="0.1"
                min="0"
                max="2"
                placeholder="e.g., 0.1"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>

            <UFormGroup label="Max Tokens">
              <UInput
                v-model.number="form.default_params.max_tokens"
                type="number"
                min="1"
                placeholder="e.g., 2000"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>

            <UFormGroup label="Top P">
              <UInput
                v-model.number="form.default_params.top_p"
                type="number"
                step="0.1"
                min="0"
                max="1"
                placeholder="e.g., 0.9"
                :disabled="modelsStore.loading.create"
              />
            </UFormGroup>
          </div>
        </div>

        <!-- Features -->
        <div>
          <h4 class="text-sm font-medium text-gray-900 mb-3">Features</h4>
          <div class="space-y-2">
            <div 
              v-for="(feature, index) in form.features" 
              :key="index"
              class="flex items-center space-x-3"
            >
              <UInput
                v-model="feature.name"
                placeholder="Feature name"
                class="flex-1"
                :disabled="modelsStore.loading.create"
              />
              
              <UToggle
                v-model="feature.supported"
                :disabled="modelsStore.loading.create"
              />
              
              <UButton
                @click="removeFeature(index)"
                variant="ghost"
                icon="i-heroicons-trash"
                size="sm"
                :disabled="modelsStore.loading.create"
              />
            </div>
            
            <UButton
              @click="addFeature"
              variant="outline"
              icon="i-heroicons-plus"
              size="sm"
              :disabled="modelsStore.loading.create"
            >
              Add Feature
            </UButton>
          </div>
        </div>

        <!-- Status -->
        <div class="flex items-center space-x-3">
          <UToggle
            v-model="form.enabled"
            :disabled="modelsStore.loading.create"
          />
          <label class="text-sm font-medium text-gray-900">
            Enable this model
          </label>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
        <UButton
          @click="isOpen = false"
          variant="outline"
          :disabled="modelsStore.loading.create"
        >
          Cancel
        </UButton>
        
        <UButton
          type="submit"
          :loading="modelsStore.loading.create"
          icon="i-heroicons-plus"
        >
          Create Model
        </UButton>
      </div>
    </form>
  </UModal>
</template>

<script setup lang="ts">
import { useModelsStore } from '~/stores/models'
import type { CreateModelRequest, ModelFeature } from '~/types/models'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'created': []
}>()

// Stores
const modelsStore = useModelsStore()
const toast = useToast()

// State
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const form = ref<CreateModelRequest>({
  name: '',
  provider: 'openai',
  category: 'chat',
  pricing: {
    input: 0,
    output: 0
  },
  context_length: 8192,
  features: [],
  enabled: true,
  default_params: {
    temperature: 0.1,
    max_tokens: 2000,
    top_p: 0.9
  }
})

// Options
const providerOptions = [
  { label: 'OpenAI', value: 'openai' },
  { label: 'Anthropic', value: 'anthropic' },
  { label: 'OpenRouter', value: 'openrouter' },
  { label: 'Cohere', value: 'cohere' }
]

const categoryOptions = [
  { label: 'Chat', value: 'chat' },
  { label: 'Embedding', value: 'embedding' },
  { label: 'Completion', value: 'completion' }
]

// Methods
const addFeature = () => {
  form.value.features?.push({
    name: '',
    supported: true,
    description: ''
  })
}

const removeFeature = (index: number) => {
  form.value.features?.splice(index, 1)
}

const resetForm = () => {
  form.value = {
    name: '',
    provider: 'openai',
    category: 'chat',
    pricing: {
      input: 0,
      output: 0
    },
    context_length: 8192,
    features: [],
    enabled: true,
    default_params: {
      temperature: 0.1,
      max_tokens: 2000,
      top_p: 0.9
    }
  }
}

const validateForm = (): string | null => {
  if (!form.value.name.trim()) {
    return 'Model name is required'
  }
  
  if (!form.value.provider) {
    return 'Provider is required'
  }
  
  if (!form.value.category) {
    return 'Category is required'
  }
  
  if (form.value.pricing.input < 0 || form.value.pricing.output < 0) {
    return 'Pricing must be non-negative'
  }
  
  if (form.value.context_length <= 0) {
    return 'Context length must be positive'
  }
  
  // Validate features
  if (form.value.features) {
    for (const feature of form.value.features) {
      if (!feature.name.trim()) {
        return 'All feature names must be provided'
      }
    }
  }
  
  // Validate default params
  if (form.value.default_params?.temperature !== undefined) {
    if (form.value.default_params.temperature < 0 || form.value.default_params.temperature > 2) {
      return 'Temperature must be between 0 and 2'
    }
  }
  
  if (form.value.default_params?.max_tokens !== undefined) {
    if (form.value.default_params.max_tokens <= 0) {
      return 'Max tokens must be positive'
    }
  }
  
  if (form.value.default_params?.top_p !== undefined) {
    if (form.value.default_params.top_p < 0 || form.value.default_params.top_p > 1) {
      return 'Top P must be between 0 and 1'
    }
  }
  
  return null
}

const createModel = async () => {
  const error = validateForm()
  if (error) {
    toast.add({
      title: 'Validation Error',
      description: error,
      color: 'red'
    })
    return
  }
  
  try {
    await modelsStore.createModel(form.value)
    emit('created')
    isOpen.value = false
    resetForm()
  } catch (error) {
    toast.add({
      title: 'Error creating model',
      description: error.message,
      color: 'red'
    })
  }
}

// Watch for modal close to reset form
watch(isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>