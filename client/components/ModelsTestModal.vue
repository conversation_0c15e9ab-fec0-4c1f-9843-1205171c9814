<template>
  <UModal v-model="isOpen" :ui="{ width: 'sm:max-w-3xl' }">
    <div class="p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900">Test Model</h3>
          <p class="text-sm text-gray-500">{{ model?.name }} ({{ model?.id }})</p>
        </div>
        <UButton
          @click="isOpen = false"
          variant="ghost"
          icon="i-heroicons-x-mark"
          size="sm"
        />
      </div>

      <div v-if="model" class="space-y-6">
        <!-- Model Info -->
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-600">Provider:</span>
              <UBadge 
                :color="modelsStore.getProviderColor(model.provider)"
                variant="soft"
                class="ml-2"
              >
                {{ model.provider }}
              </UBadge>
            </div>
            
            <div>
              <span class="text-gray-600">Category:</span>
              <span class="ml-2 font-medium">{{ model.category }}</span>
            </div>
            
            <div>
              <span class="text-gray-600">Context:</span>
              <span class="ml-2 font-medium">{{ modelsStore.formatTokens(model.context_length) }}</span>
            </div>
            
            <div>
              <span class="text-gray-600">Status:</span>
              <UBadge 
                :color="model.enabled ? 'green' : 'red'"
                variant="soft"
                class="ml-2"
              >
                {{ model.enabled ? 'Enabled' : 'Disabled' }}
              </UBadge>
            </div>
          </div>
        </div>

        <!-- Test Form -->
        <form @submit.prevent="testModel" class="space-y-4">
          <UFormGroup label="Test Prompt" required>
            <UTextarea
              v-model="testPrompt"
              placeholder="Enter a test prompt to send to the model..."
              rows="4"
              :disabled="modelsStore.loading.test"
            />
          </UFormGroup>

          <!-- Parameters -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Parameters</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <UFormGroup label="Temperature">
                <UInput
                  v-model.number="testParams.temperature"
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  :placeholder="String(model.default_params.temperature || 0.1)"
                  :disabled="modelsStore.loading.test"
                />
              </UFormGroup>

              <UFormGroup label="Max Tokens">
                <UInput
                  v-model.number="testParams.max_tokens"
                  type="number"
                  min="1"
                  :placeholder="String(model.default_params.max_tokens || 1000)"
                  :disabled="modelsStore.loading.test"
                />
              </UFormGroup>

              <UFormGroup label="Top P">
                <UInput
                  v-model.number="testParams.top_p"
                  type="number"
                  step="0.1"
                  min="0"
                  max="1"
                  :placeholder="String(model.default_params.top_p || 0.9)"
                  :disabled="modelsStore.loading.test"
                />
              </UFormGroup>
            </div>
          </div>

          <!-- Test Button -->
          <div class="flex justify-end">
            <UButton
              type="submit"
              :loading="modelsStore.loading.test"
              :disabled="!testPrompt.trim() || !model.enabled"
              icon="i-heroicons-beaker"
            >
              Run Test
            </UButton>
          </div>
        </form>

        <!-- Test Results -->
        <div v-if="testResult" class="space-y-4">
          <div class="border-t border-gray-200 pt-6">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Test Results</h4>
          </div>

          <!-- Success Result -->
          <div v-if="testResult.success" class="space-y-4">
            <!-- Response -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Model Response
              </label>
              <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <pre class="text-sm text-green-800 whitespace-pre-wrap">{{ testResult.response }}</pre>
              </div>
            </div>

            <!-- Metrics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="bg-blue-50 rounded-lg p-3 text-center">
                <div class="text-lg font-semibold text-blue-900">
                  {{ testResult.tokens_used || 'N/A' }}
                </div>
                <div class="text-xs text-blue-600">Tokens Used</div>
              </div>
              
              <div class="bg-purple-50 rounded-lg p-3 text-center">
                <div class="text-lg font-semibold text-purple-900">
                  {{ testResult.response_time || 'N/A' }}ms
                </div>
                <div class="text-xs text-purple-600">Response Time</div>
              </div>
              
              <div class="bg-yellow-50 rounded-lg p-3 text-center">
                <div class="text-lg font-semibold text-yellow-900">
                  {{ testResult.cost ? modelsStore.formatCost(testResult.cost) : 'N/A' }}
                </div>
                <div class="text-xs text-yellow-600">Estimated Cost</div>
              </div>
              
              <div class="bg-green-50 rounded-lg p-3 text-center">
                <div class="text-lg font-semibold text-green-900">✅</div>
                <div class="text-xs text-green-600">Success</div>
              </div>
            </div>
          </div>

          <!-- Error Result -->
          <div v-else class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Error Details
              </label>
              <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-start space-x-3">
                  <span class="text-red-500 text-lg">❌</span>
                  <div>
                    <h5 class="font-medium text-red-900">Test Failed</h5>
                    <p class="text-sm text-red-700 mt-1">{{ testResult.error }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Troubleshooting Tips -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div class="flex items-start space-x-3">
                <span class="text-yellow-500 text-lg">💡</span>
                <div>
                  <h5 class="font-medium text-yellow-900">Troubleshooting Tips</h5>
                  <ul class="text-sm text-yellow-700 mt-1 space-y-1">
                    <li>• Check if the model is enabled and properly configured</li>
                    <li>• Verify that the provider API key is valid and has sufficient credits</li>
                    <li>• Try a simpler prompt or adjust the parameters</li>
                    <li>• Check if the model supports the requested features</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sample Prompts -->
        <div class="border-t border-gray-200 pt-6">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Sample Prompts</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <button
              v-for="sample in getSamplePrompts(model.category)"
              :key="sample.title"
              @click="testPrompt = sample.prompt"
              class="text-left p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
              :disabled="modelsStore.loading.test"
            >
              <div class="font-medium text-sm text-gray-900">{{ sample.title }}</div>
              <div class="text-xs text-gray-500 mt-1 truncate">{{ sample.prompt }}</div>
            </button>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
        <UButton
          @click="clearResults"
          variant="outline"
          :disabled="modelsStore.loading.test"
        >
          Clear Results
        </UButton>
        
        <UButton
          @click="isOpen = false"
          :disabled="modelsStore.loading.test"
        >
          Close
        </UButton>
      </div>
    </div>
  </UModal>
</template>

<script setup lang="ts">
import { useModelsStore } from '~/stores/models'
import type { ModelConfig, ModelTestResponse } from '~/types/models'

// Props
interface Props {
  modelValue: boolean
  model: ModelConfig | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// Stores
const modelsStore = useModelsStore()
const toast = useToast()

// State
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const testPrompt = ref('')
const testParams = ref({
  temperature: undefined as number | undefined,
  max_tokens: undefined as number | undefined,
  top_p: undefined as number | undefined
})
const testResult = ref<ModelTestResponse | null>(null)

// Sample prompts for different categories
const samplePrompts = {
  chat: [
    {
      title: 'Simple Conversation',
      prompt: 'Hello! How can you help me today?'
    },
    {
      title: 'Code Explanation',
      prompt: 'Explain what this JavaScript code does: console.log("Hello, World!");'
    },
    {
      title: 'Creative Writing',
      prompt: 'Write a short story about a robot learning to paint.'
    },
    {
      title: 'Problem Solving',
      prompt: 'How would you approach solving the traveling salesman problem?'
    }
  ],
  embedding: [
    {
      title: 'Document Similarity',
      prompt: 'This is a sample document about artificial intelligence and machine learning.'
    },
    {
      title: 'Code Snippet',
      prompt: 'function calculateSum(a, b) { return a + b; }'
    },
    {
      title: 'Product Description',
      prompt: 'High-quality wireless headphones with noise cancellation and 30-hour battery life.'
    },
    {
      title: 'Technical Content',
      prompt: 'REST APIs use HTTP methods like GET, POST, PUT, and DELETE to perform CRUD operations.'
    }
  ],
  completion: [
    {
      title: 'Text Completion',
      prompt: 'The benefits of renewable energy include'
    },
    {
      title: 'Code Completion',
      prompt: 'def fibonacci(n):\n    if n <= 1:\n        return n\n    else:'
    },
    {
      title: 'Story Continuation',
      prompt: 'Once upon a time, in a land far away, there lived a young wizard who'
    },
    {
      title: 'Technical Writing',
      prompt: 'To implement a binary search algorithm, you first need to'
    }
  ]
}

// Methods
const getSamplePrompts = (category: string) => {
  return samplePrompts[category as keyof typeof samplePrompts] || samplePrompts.chat
}

const testModel = async () => {
  if (!props.model || !testPrompt.value.trim()) return
  
  try {
    const params: any = {}
    
    if (testParams.value.temperature !== undefined) {
      params.temperature = testParams.value.temperature
    }
    if (testParams.value.max_tokens !== undefined) {
      params.max_tokens = testParams.value.max_tokens
    }
    if (testParams.value.top_p !== undefined) {
      params.top_p = testParams.value.top_p
    }
    
    const result = await modelsStore.testModel({
      model_id: props.model.id,
      prompt: testPrompt.value,
      params: Object.keys(params).length > 0 ? params : undefined
    })
    
    testResult.value = result
    
    if (result.success) {
      toast.add({
        title: 'Test completed successfully',
        description: `Model responded in ${result.response_time}ms`,
        color: 'green'
      })
    } else {
      toast.add({
        title: 'Test failed',
        description: result.error || 'Unknown error',
        color: 'red'
      })
    }
  } catch (error) {
    toast.add({
      title: 'Error running test',
      description: error.message,
      color: 'red'
    })
  }
}

const clearResults = () => {
  testResult.value = null
  testPrompt.value = ''
  testParams.value = {
    temperature: undefined,
    max_tokens: undefined,
    top_p: undefined
  }
}

// Reset form when modal opens/closes
watch(isOpen, (newValue) => {
  if (!newValue) {
    clearResults()
  } else if (props.model) {
    // Set default prompt based on category
    const samples = getSamplePrompts(props.model.category)
    if (samples.length > 0) {
      testPrompt.value = samples[0].prompt
    }
  }
})
</script>