<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white" @click.stop>
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Añadir Conocimiento</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>

        <!-- Project Info -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-3 mb-6">
          <div class="flex items-center">
            <InformationCircleIcon class="w-5 h-5 text-blue-400 mr-2" />
            <p class="text-sm text-blue-800">
              Añadiendo conocimiento a: <strong>{{ ragStore.currentProject?.name }}</strong>
            </p>
          </div>
          <p class="text-xs text-blue-600 mt-1">
            Los documentos largos (+8000 caracteres) se dividirán automáticamente en partes para optimizar el procesamiento.
          </p>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Documents -->
          <div>
            <div class="flex items-center justify-between mb-3">
              <label class="block text-sm font-medium text-gray-700">
                Documentos
              </label>
              <button
                type="button"
                @click="addDocument"
                class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200"
              >
                <PlusIcon class="w-3 h-3 mr-1" />
                Añadir documento
              </button>
            </div>

            <div v-if="documents.length === 0" class="text-center py-8">
              <DocumentIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-sm text-gray-500">No hay documentos añadidos</p>
              <button
                type="button"
                @click="addDocument"
                class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
              >
                <PlusIcon class="w-4 h-4 mr-2" />
                Añadir primer documento
              </button>
            </div>

            <div v-else class="space-y-4">
              <div 
                v-for="(doc, index) in documents" 
                :key="index"
                class="border border-gray-200 rounded-lg p-4"
              >
                <div class="flex items-center justify-between mb-3">
                  <div class="flex items-center space-x-3">
                    <select
                      v-model="doc.type"
                      class="text-sm border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="story">User Story</option>
                      <option value="requirement">Requerimiento</option>
                      <option value="test">Test Case</option>
                      <option value="documentation">Documentación</option>
                    </select>
                    
                    <input
                      v-model="doc.source"
                      placeholder="Fuente (opcional)"
                      class="text-sm border border-gray-300 rounded px-2 py-1 w-32"
                    />
                  </div>
                  
                  <button
                    type="button"
                    @click="removeDocument(index)"
                    class="text-red-400 hover:text-red-600"
                    title="Eliminar documento"
                  >
                    <TrashIcon class="w-4 h-4" />
                  </button>
                </div>
                
                <textarea
                  v-model="doc.content"
                  rows="4"
                  placeholder="Contenido del documento..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  required
                />
                
                <div class="mt-2 flex items-center justify-between text-xs text-gray-500">
                  <span>{{ doc.content.length }} caracteres</span>
                  <span :class="doc.content.length > 8000 ? 'text-orange-500' : 'text-green-500'">
                    {{ doc.content.length > 8000 ? `Se dividirá en ${Math.ceil(doc.content.length / 6000)} partes` : 'OK' }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Bulk Input -->
          <div class="border-t border-gray-200 pt-6">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Importación Rápida</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                type="button"
                @click="showBulkInput = !showBulkInput"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <DocumentTextIcon class="w-4 h-4 mr-2" />
                Texto en bulk
              </button>

              <label class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer">
                <input
                  type="file"
                  ref="fileInput"
                  @change="handleFileUpload"
                  accept=".md,.pdf,.txt"
                  multiple
                  class="hidden"
                />
                <DocumentArrowUpIcon class="w-4 h-4 mr-2" />
                Subir archivos
              </label>
              
              <button
                type="button"
                @click="addExampleDocuments"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <LightBulbIcon class="w-4 h-4 mr-2" />
                Ejemplos
              </button>
            </div>

            <!-- File Upload Status -->
            <div v-if="uploadedFiles.length > 0" class="mt-4">
              <h5 class="text-sm font-medium text-gray-700 mb-2">Archivos procesados:</h5>
              <div class="space-y-2">
                <div 
                  v-for="file in uploadedFiles" 
                  :key="file.name"
                  class="flex items-center justify-between bg-green-50 border border-green-200 rounded-md p-2"
                >
                  <div class="flex items-center space-x-2">
                    <CheckCircleIcon class="w-4 h-4 text-green-500" />
                    <span class="text-sm text-green-800">{{ file.name }}</span>
                    <span class="text-xs text-green-600">({{ file.size }})</span>
                  </div>
                  <span class="text-xs text-green-600">{{ file.type }}</span>
                </div>
              </div>
            </div>

            <!-- Processing Status -->
            <div v-if="processingFiles" class="mt-4 bg-blue-50 border border-blue-200 rounded-md p-3">
              <div class="flex items-center">
                <svg class="animate-spin h-4 w-4 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-blue-800">Procesando archivos...</span>
              </div>
            </div>

            <!-- Bulk Input TextArea -->
            <div v-if="showBulkInput" class="mt-4">
              <textarea
                v-model="bulkText"
                rows="6"
                placeholder="Pega aquí múltiples documentos separados por líneas en blanco o '---'"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <div class="flex items-center justify-between mt-2">
                <button
                  type="button"
                  @click="processBulkText"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                >
                  <DocumentPlusIcon class="w-4 h-4 mr-2" />
                  Procesar texto
                </button>
                <span class="text-xs text-gray-500">
                  Separar documentos con líneas en blanco o '---'
                </span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              type="submit"
              :disabled="!canSubmit || loading"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <template v-if="loading">
                <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Añadiendo...
              </template>
              <template v-else>
                <DocumentPlusIcon class="w-4 h-4 mr-2" />
                Añadir {{ documents.length }} documento{{ documents.length !== 1 ? 's' : '' }}
              </template>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRAGStore } from '~/stores/rag'
import { 
  XMarkIcon, 
  PlusIcon, 
  DocumentIcon,
  TrashIcon,
  DocumentTextIcon,
  LightBulbIcon,
  DocumentPlusIcon,
  InformationCircleIcon,
  DocumentArrowUpIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'

// Emits
const emit = defineEmits<{
  close: []
  added: [count: number]
}>()

// Store
const ragStore = useRAGStore()

// State
const documents = ref<Array<{
  content: string
  type: 'story' | 'requirement' | 'test' | 'documentation'
  source: string
}>>([])

const loading = ref(false)
const showBulkInput = ref(false)
const bulkText = ref('')
const fileInput = ref<HTMLInputElement>()
const uploadedFiles = ref<Array<{
  name: string
  size: string
  type: string
}>>([])
const processingFiles = ref(false)

// Computed
const canSubmit = computed(() => {
  return documents.value.length > 0 && 
         documents.value.every(doc => doc.content.trim().length > 0)
})

// Methods
const addDocument = () => {
  documents.value.push({
    content: '',
    type: 'story',
    source: 'manual'
  })
}

const removeDocument = (index: number) => {
  documents.value.splice(index, 1)
}

const processBulkText = () => {
  if (!bulkText.value.trim()) return
  
  const separators = /\n\s*\n|---/
  const chunks = bulkText.value.split(separators)
    .map(chunk => chunk.trim())
    .filter(chunk => chunk.length > 0)
  
  chunks.forEach(chunk => {
    documents.value.push({
      content: chunk,
      type: 'documentation',
      source: 'bulk-import'
    })
  })
  
  bulkText.value = ''
  showBulkInput.value = false
}

const addExampleDocuments = () => {
  const examples = [
    {
      content: 'Como cliente, quiero poder agregar productos al carrito para comprarlos posteriormente. Los productos deben mostrarse con precio, disponibilidad y descripción.',
      type: 'story' as const,
      source: 'examples'
    },
    {
      content: 'Requerimiento: El sistema debe validar que el usuario esté autenticado antes de permitir agregar productos al carrito. También debe verificar stock disponible.',
      type: 'requirement' as const,
      source: 'examples'
    },
    {
      content: 'Test Case: Verificar que el botón "Agregar al carrito" se habilita solo cuando hay stock disponible. Steps: 1. Navegar al producto, 2. Verificar stock, 3. Hacer clic en agregar.',
      type: 'test' as const,
      source: 'examples'
    }
  ]
  
  examples.forEach(example => {
    documents.value.push(example)
  })
}

const handleFileUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) return
  
  processingFiles.value = true
  uploadedFiles.value = []
  
  try {
    for (const file of Array.from(files)) {
      const fileContent = await processFile(file)
      if (fileContent) {
        const docType = getDocumentTypeFromFile(file)
        const chunks = chunkDocument(fileContent, file.name)
        
        // Agregar cada chunk como un documento separado
        chunks.forEach((chunk, index) => {
          documents.value.push({
            content: chunk.content,
            type: docType,
            source: chunks.length > 1 ? `file:${file.name} (parte ${index + 1}/${chunks.length})` : `file:${file.name}`
          })
        })
        
        // Agregar a la lista de archivos procesados
        uploadedFiles.value.push({
          name: file.name,
          size: formatFileSize(file.size),
          type: chunks.length > 1 ? `${docType} (${chunks.length} partes)` : docType
        })
      }
    }
  } catch (error) {
    console.error('Error processing files:', error)
    alert('Error al procesar algunos archivos. Revisa la consola para más detalles.')
  } finally {
    processingFiles.value = false
    // Reset file input
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const processFile = async (file: File): Promise<string | null> => {
  try {
    if (file.type === 'application/pdf') {
      return await processPDFFile(file)
    } else if (file.name.endsWith('.md') || file.type === 'text/markdown') {
      return await processTextFile(file)
    } else if (file.type === 'text/plain') {
      return await processTextFile(file)
    } else {
      console.warn(`Unsupported file type: ${file.type}`)
      return null
    }
  } catch (error) {
    console.error(`Error processing file ${file.name}:`, error)
    return null
  }
}

const processTextFile = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      resolve(content)
    }
    reader.onerror = (e) => {
      reject(new Error(`Failed to read file: ${file.name}`))
    }
    reader.readAsText(file)
  })
}

const processPDFFile = async (file: File): Promise<string> => {
  try {
    // Importación dinámica para evitar problemas SSR
    const { getDocument } = await import('pdfjs-dist')
    
    // Configurar worker path
    const pdfjsLib = await import('pdfjs-dist')
    pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'
    
    const arrayBuffer = await file.arrayBuffer()
    const pdf = await getDocument({ data: arrayBuffer }).promise
    
    let textContent = ''
    
    // Extraer texto de todas las páginas
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum)
      const content = await page.getTextContent()
      
      const pageText = content.items
        .map((item: any) => item.str)
        .join(' ')
      
      textContent += `\n--- Página ${pageNum} ---\n${pageText}\n`
    }
    
    return textContent.trim()
  } catch (error) {
    console.error('Error processing PDF:', error)
    return `[PDF Content from ${file.name}]\n\nError: Could not extract text from PDF. Please convert to text manually.\nError details: ${error.message}`
  }
}

const getDocumentTypeFromFile = (file: File): 'story' | 'requirement' | 'test' | 'documentation' => {
  const fileName = file.name.toLowerCase()
  
  if (fileName.includes('story') || fileName.includes('user')) {
    return 'story'
  } else if (fileName.includes('requirement') || fileName.includes('req')) {
    return 'requirement'
  } else if (fileName.includes('test') || fileName.includes('spec')) {
    return 'test'
  } else {
    return 'documentation'
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const chunkDocument = (content: string, fileName: string): Array<{ content: string }> => {
  const maxChunkSize = 6000 // Tamaño más seguro para evitar límites de API
  const overlap = 200 // Solapamiento entre chunks para mantener contexto
  
  if (content.length <= maxChunkSize) {
    return [{ content }]
  }
  
  const chunks: Array<{ content: string }> = []
  let start = 0
  
  // Intentar dividir por secciones de markdown primero
  if (fileName.endsWith('.md')) {
    const sections = content.split(/\n#{1,6}\s+/)
    let currentChunk = ''
    
    for (let i = 0; i < sections.length; i++) {
      const section = i === 0 ? sections[i] : `# ${sections[i]}`
      
      if ((currentChunk + section).length <= maxChunkSize) {
        currentChunk += section
      } else {
        if (currentChunk) {
          chunks.push({ content: currentChunk.trim() })
        }
        
        // Si la sección es muy grande, dividirla por párrafos
        if (section.length > maxChunkSize) {
          const subChunks = chunkByParagraphs(section, maxChunkSize, overlap)
          chunks.push(...subChunks.map(chunk => ({ content: chunk })))
          currentChunk = ''
        } else {
          currentChunk = section
        }
      }
    }
    
    if (currentChunk) {
      chunks.push({ content: currentChunk.trim() })
    }
  } else {
    // Para otros tipos de archivo, dividir por párrafos
    const paragraphChunks = chunkByParagraphs(content, maxChunkSize, overlap)
    chunks.push(...paragraphChunks.map(chunk => ({ content: chunk })))
  }
  
  return chunks.filter(chunk => chunk.content.length > 50) // Filtrar chunks muy pequeños
}

const chunkByParagraphs = (text: string, chunkSize: number, overlap: number): string[] => {
  const chunks: string[] = []
  let start = 0
  
  while (start < text.length) {
    const end = Math.min(start + chunkSize, text.length)
    let chunk = text.substring(start, end)
    
    // Intentar cortar en una oración o párrafo completo
    if (end < text.length) {
      const lastPeriod = chunk.lastIndexOf('. ')
      const lastNewline = chunk.lastIndexOf('\n\n')
      const lastSingleNewline = chunk.lastIndexOf('\n')
      
      // Preferir cortes en párrafos, luego oraciones, luego líneas
      let cutPoint = Math.max(lastNewline, lastPeriod)
      if (cutPoint === -1) cutPoint = lastSingleNewline
      
      if (cutPoint > start + chunkSize * 0.7) { // Al menos 70% del chunk
        chunk = text.substring(start, cutPoint + 1)
        start = Math.max(cutPoint + 1 - overlap, start + 1)
      } else {
        start = end - overlap
      }
    } else {
      start = end
    }
    
    chunks.push(chunk.trim())
  }
  
  return chunks.filter(chunk => chunk.length > 50)
}

const handleSubmit = async () => {
  if (!canSubmit.value || !ragStore.currentProject) return
  
  loading.value = true
  
  try {
    const count = await ragStore.addKnowledge(documents.value)
    emit('added', count)
  } catch (error) {
    console.error('Failed to add knowledge:', error)
  } finally {
    loading.value = false
  }
}
</script>