import { defineStore } from 'pinia'
import { useApiStore } from './api'
import type { 
  ModelConfig, 
  ModelProvider,
  ModelCategory,
  CreateModelRequest, 
  UpdateModelRequest,
  ModelUsageStats,
  AgentConfiguration,
  UpdateAgentConfigRequest,
  ModelTestRequest,
  ModelsApiResponse,
  ModelApiResponse,
  AgentConfigurationsApiResponse,
  ModelStatsApiResponse
} from '../types/models'

interface ModelsState {
  models: ModelConfig[]
  providers: ModelProvider[]
  categories: ModelCategory[]
  currentModel: ModelConfig | null
  usageStats: ModelUsageStats[]
  agentConfigurations: AgentConfiguration[]
  loading: {
    models: boolean
    providers: boolean
    categories: boolean
    stats: boolean
    agents: boolean
    test: boolean
    create: boolean
    update: boolean
    delete: boolean
  }
  error: string | null
  totalCost: number
  totalRequests: number
}

export const useModelsEnhancedStore = defineStore('modelsEnhanced', {
  state: (): ModelsState => ({
    models: [],
    providers: [],
    categories: [],
    currentModel: null,
    usageStats: [],
    agentConfigurations: [],
    loading: {
      models: false,
      providers: false,
      categories: false,
      stats: false,
      agents: false,
      test: false,
      create: false,
      update: false,
      delete: false
    },
    error: null,
    totalCost: 0,
    totalRequests: 0
  }),

  getters: {
    // Filtros de modelos
    enabledModels: (state) => state.models.filter(m => m.enabled),
    disabledModels: (state) => state.models.filter(m => !m.enabled),
    
    modelsByProvider: (state) => (providerId: string) => 
      state.models.filter(m => m.provider_id === providerId),
    
    modelsByCategory: (state) => (categoryId: string) => 
      state.models.filter(m => m.category_id === categoryId),

    // Obtener información de proveedor y categoría por ID
    getProviderById: (state) => (id: string) => 
      state.providers.find(p => p.id === id),
    
    getCategoryById: (state) => (id: string) => 
      state.categories.find(c => c.id === id),

    // Modelos por tipo
    chatModels: (state) => state.models.filter(m => {
      const category = state.categories.find(c => c.id === m.category_id)
      return category?.name === 'chat'
    }),
    
    embeddingModels: (state) => state.models.filter(m => {
      const category = state.categories.find(c => c.id === m.category_id)
      return category?.name === 'embedding'
    }),

    // Estadísticas calculadas
    avgCostPerRequest: (state) => 
      state.totalRequests > 0 ? state.totalCost / state.totalRequests : 0,

    mostUsedModel: (state) => {
      if (state.usageStats.length === 0) return null
      return state.usageStats.reduce((prev, current) => 
        prev.total_requests > current.total_requests ? prev : current
      )
    },

    mostExpensiveModel: (state) => {
      if (state.usageStats.length === 0) return null
      return state.usageStats.reduce((prev, current) => 
        prev.total_cost > current.total_cost ? prev : current
      )
    },

    // Configuraciones de agentes
    getAgentConfiguration: (state) => (agentType: string) => 
      state.agentConfigurations.find(a => a.agent_type === agentType),

    // Estado de carga
    isLoading: (state) => Object.values(state.loading).some(loading => loading)
  },

  actions: {
    // === GESTIÓN DE PROVEEDORES ===
    async fetchProviders(enabledOnly: boolean = false) {
      this.loading.providers = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const params = new URLSearchParams()
        if (enabledOnly) params.append('enabled_only', 'true')
        
        const response = await apiStore.request(`/api/models/providers?${params}`)
        
        if (response.success) {
          this.providers = response || []
        } else {
          throw new Error(response.error || 'Failed to fetch providers')
        }
      } catch (error) {
        console.error('Error fetching providers:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch providers'
        throw error
      } finally {
        this.loading.providers = false
      }
    },

    // === GESTIÓN DE CATEGORÍAS ===
    async fetchCategories(enabledOnly: boolean = false) {
      this.loading.categories = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const params = new URLSearchParams()
        if (enabledOnly) params.append('enabled_only', 'true')
        
        const response = await apiStore.request(`/api/models/categories?${params}`)
        
        if (response.success) {
          this.categories = response || []
        } else {
          throw new Error(response.error || 'Failed to fetch categories')
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch categories'
        throw error
      } finally {
        this.loading.categories = false
      }
    },

    // === GESTIÓN DE MODELOS ===
    async fetchModels(
      provider?: string, 
      category?: string, 
      enabled?: boolean
    ) {
      this.loading.models = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const params = new URLSearchParams()
        
        if (provider) params.append('provider', provider)
        if (category) params.append('category', category)
        if (enabled !== undefined) params.append('enabled', String(enabled))
        
        const response: ModelsApiResponse = await apiStore.request(`/api/models?${params}`)
        
        if (response.success) {
          this.models = response.models || []
          // También actualizar proveedores y categorías disponibles
          if (response.providers) {
            // Map provider names to full provider objects if needed
          }
          if (response.categories) {
            // Map category names to full category objects if needed
          }
        } else {
          throw new Error(response.error || 'Failed to fetch models')
        }
      } catch (error) {
        console.error('Error fetching models:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch models'
        throw error
      } finally {
        this.loading.models = false
      }
    },

    async getModel(id: string) {
      try {
        const apiStore = useApiStore()
        const response: ModelApiResponse = await apiStore.request(`/api/models/${encodeURIComponent(id)}`)
        
        if (response.success) {
          this.currentModel = response.model
          return response.model
        } else {
          throw new Error(response.error || 'Failed to get model')
        }
      } catch (error) {
        console.error('Error getting model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to get model'
        throw error
      }
    },

    async createModel(request: CreateModelRequest) {
      this.loading.create = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response: ModelApiResponse = await apiStore.request('/api/models', {
          method: 'POST',
          body: JSON.stringify(request)
        })
        
        if (response.success) {
          // Refresh models list
          await this.fetchModels()
          return response.model
        } else {
          throw new Error(response.error || 'Failed to create model')
        }
      } catch (error) {
        console.error('Error creating model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to create model'
        throw error
      } finally {
        this.loading.create = false
      }
    },

    async updateModel(id: string, request: UpdateModelRequest) {
      this.loading.update = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response: ModelApiResponse = await apiStore.request(`/api/models/${encodeURIComponent(id)}`, {
          method: 'PUT',
          body: JSON.stringify(request)
        })
        
        if (response.success) {
          // Update local model
          const index = this.models.findIndex(m => m.model_id === id)
          if (index !== -1) {
            this.models[index] = response.model
          }
          
          if (this.currentModel?.model_id === id) {
            this.currentModel = response.model
          }
          
          return response.model
        } else {
          throw new Error(response.error || 'Failed to update model')
        }
      } catch (error) {
        console.error('Error updating model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to update model'
        throw error
      } finally {
        this.loading.update = false
      }
    },

    async deleteModel(id: string) {
      this.loading.delete = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/models/${encodeURIComponent(id)}`, {
          method: 'DELETE'
        })
        
        if (response.success) {
          // Remove from local models
          this.models = this.models.filter(m => m.model_id !== id)
          
          if (this.currentModel?.model_id === id) {
            this.currentModel = null
          }
          
          return true
        } else {
          throw new Error(response.error || 'Failed to delete model')
        }
      } catch (error) {
        console.error('Error deleting model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to delete model'
        throw error
      } finally {
        this.loading.delete = false
      }
    },

    // === ESTADÍSTICAS ===
    async fetchUsageStats(modelId?: string) {
      this.loading.stats = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const endpoint = modelId 
          ? `/api/models/stats?model_id=${encodeURIComponent(modelId)}`
          : '/api/models/stats'
        
        const response: ModelStatsApiResponse = await apiStore.request(endpoint)
        
        if (response.success) {
          this.usageStats = response.stats || []
          this.totalCost = response.total_cost || 0
          this.totalRequests = response.total_requests || 0
        } else {
          throw new Error(response.error || 'Failed to fetch usage stats')
        }
      } catch (error) {
        console.error('Error fetching usage stats:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch usage stats'
        throw error
      } finally {
        this.loading.stats = false
      }
    },

    // === CONFIGURACIÓN DE AGENTES ===
    async fetchAgentConfigurations() {
      this.loading.agents = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response: AgentConfigurationsApiResponse = await apiStore.request('/api/models/agents')
        
        if (response.success) {
          this.agentConfigurations = response.selections || []
        } else {
          throw new Error(response.error || 'Failed to fetch agent configurations')
        }
      } catch (error) {
        console.error('Error fetching agent configurations:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch agent configurations'
        throw error
      } finally {
        this.loading.agents = false
      }
    },

    async updateAgentConfiguration(agentType: string, request: UpdateAgentConfigRequest) {
      this.loading.agents = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/models/agents/${agentType}`, {
          method: 'PUT',
          body: JSON.stringify(request)
        })
        
        if (response.success) {
          // Update local agent configuration
          const index = this.agentConfigurations.findIndex(a => a.agent_type === agentType)
          if (index !== -1) {
            this.agentConfigurations[index] = response.configuration
          } else {
            this.agentConfigurations.push(response.configuration)
          }
          
          return true
        } else {
          throw new Error(response.error || 'Failed to update agent configuration')
        }
      } catch (error) {
        console.error('Error updating agent configuration:', error)
        this.error = error instanceof Error ? error.message : 'Failed to update agent configuration'
        throw error
      } finally {
        this.loading.agents = false
      }
    },

    // === TESTING ===
    async testModel(request: ModelTestRequest) {
      this.loading.test = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request('/api/models/test', {
          method: 'POST',
          body: JSON.stringify(request)
        })
        
        return response // Return the full response (success or error)
      } catch (error) {
        console.error('Error testing model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to test model'
        throw error
      } finally {
        this.loading.test = false
      }
    },

    // === UTILIDADES ===
    clearError() {
      this.error = null
    },

    clearCurrentModel() {
      this.currentModel = null
    },

    // Formateo de datos
    formatCost(cost: number): string {
      return `$${cost.toFixed(6)}`
    },

    formatTokens(tokens: number): string {
      if (tokens >= 1000000) {
        return `${(tokens / 1000000).toFixed(1)}M`
      } else if (tokens >= 1000) {
        return `${(tokens / 1000).toFixed(1)}K`
      }
      return tokens.toString()
    },

    getProviderColor(providerId: string): string {
      const provider = this.getProviderById(providerId)
      if (!provider) return 'gray'
      
      const colors: Record<string, string> = {
        openai: 'green',
        anthropic: 'orange',
        openrouter: 'blue',
        cohere: 'purple'
      }
      return colors[provider.name] || 'gray'
    },

    getCategoryIcon(categoryId: string): string {
      const category = this.getCategoryById(categoryId)
      if (!category) return '🤖'
      
      return category.icon || '🤖'
    },

    // Compatibility methods for existing code
    get providers() {
      return this.providers.map(p => p.name)
    },

    get categories() {
      return this.categories.map(c => c.name)
    },

    get enabledModels() {
      return this.models.filter(m => m.enabled)
    }
  }
})
