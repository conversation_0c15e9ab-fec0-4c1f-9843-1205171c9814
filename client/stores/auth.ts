import { defineStore } from 'pinia'

interface User {
  id: string
  email: string
  plan: string
  created_at?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  loading: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && !!state.token,
    userEmail: (state) => state.user?.email || '',
    userPlan: (state) => state.user?.plan || 'basic'
  },

  actions: {
    async login(email: string, password: string) {
      this.loading = true
      try {
        const requestBody = { email, password }
        console.log('Sending login request with:', requestBody)

        const response = await fetch('http://localhost:8000/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || data.message || `HTTP error! status: ${response.status}`)
        }

        if (data.success && data.data.token && data.data.user) {
          this.token = data.data.token
          this.user = data.data.user
          this.isAuthenticated = true

          // Store in localStorage for persistence
          if (process.client) {
            localStorage.setItem('aery_token', data.data.token)
            localStorage.setItem('aery_user', JSON.stringify(data.data.user))
          }

          return { success: true }
        }

        return { success: false, error: 'Invalid response format' }
      } catch (error: any) {
        console.error('Login error:', error)
        return { 
          success: false, 
          error: error.data?.message || error.message || 'Login failed' 
        }
      } finally {
        this.loading = false
      }
    },

    async register(email: string, password: string, plan: string = 'basic') {
      this.loading = true
      try {
        const response = await fetch('http://localhost:8000/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email, password, plan })
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || data.message || `HTTP error! status: ${response.status}`)
        }

        if (data.success && data.data.token && data.data.user) {
          this.token = data.data.token
          this.user = data.data.user
          this.isAuthenticated = true

          // Store in localStorage for persistence
          if (process.client) {
            localStorage.setItem('aery_token', data.data.token)
            localStorage.setItem('aery_user', JSON.stringify(data.data.user))
          }

          return { success: true }
        }

        return { success: false, error: 'Invalid response format' }
      } catch (error: any) {
        console.error('Registration error:', error)
        return { 
          success: false, 
          error: error.data?.message || error.message || 'Registration failed' 
        }
      } finally {
        this.loading = false
      }
    },

    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      
      if (process.client) {
        localStorage.removeItem('aery_token')
        localStorage.removeItem('aery_user')
      }
      
      // Redirect to login
      navigateTo('/login')
    },

    // Restore auth state from localStorage
    initializeAuth() {
      if (process.client) {
        const token = localStorage.getItem('aery_token')
        const userStr = localStorage.getItem('aery_user')
        
        if (token && userStr) {
          try {
            this.token = token
            this.user = JSON.parse(userStr)
            this.isAuthenticated = true
          } catch (error) {
            console.error('Error parsing stored user data:', error)
            this.logout()
          }
        }
      }
    },

    // Get authorization headers for API calls
    getAuthHeaders() {
      return this.token ? { Authorization: `Bearer ${this.token}` } : {}
    }
  }
})
