// RAG Store - Gestión de estado para el servicio RAG
import { defineStore } from 'pinia'
import { useApiStore } from './api'

export interface RAGProject {
  id: string
  name: string
  description?: string
  createdAt: string
  updatedAt: string
}

export interface ImprovedStory {
  original: string
  improved: string
  acceptanceCriteria: string[]
  definitionOfDone: string[]
  estimatedComplexity: 'low' | 'medium' | 'high'
  suggestedTestCases: string[]
  context: string
}

export interface TestCase {
  title: string
  description: string
  type: 'happy_path' | 'negative' | 'boundary' | 'integration'
  steps: Array<{
    step: number
    action: string
    data?: string
    expectedBehavior?: string
  }>
  expectedResult: string
  priority: 'high' | 'medium' | 'low'
}

export interface EdgeCase {
  scenario: string
  description: string
  riskLevel: 'high' | 'medium' | 'low'
  suggestedHandling: string
  testApproach: string
}

export interface KnowledgeStats {
  totalDocuments: number
  lastUpdated: string
  documents: Array<{
    id: string
    content: string
    metadata: {
      projectId: string
      type: 'story' | 'requirement' | 'test' | 'documentation'
      source?: string
      timestamp: string
      [key: string]: any
    }
  }>
  projectInfo: {
    name: string
    description?: string
    createdAt: string
    updatedAt: string
  }
}

export const useRAGStore = defineStore('rag', {
  state: () => ({
    // Proyectos
    projects: [] as RAGProject[],
    currentProject: null as RAGProject | null,
    
    // Knowledge Base
    knowledgeStats: null as KnowledgeStats | null,
    
    // Stories
    currentStory: '',
    improvedStory: null as ImprovedStory | null,
    storyHistory: [] as ImprovedStory[],
    
    // Tests
    currentTestCases: [] as TestCase[],
    currentEdgeCases: [] as EdgeCase[],
    testHistory: [] as Array<{
      story: string
      testCases: TestCase[]
      edgeCases: EdgeCase[]
      generatedAt: string
    }>,
    
    // UI State
    loading: {
      projects: false,
      improveStory: false,
      generateTests: false,
      addKnowledge: false
    },
    
    errors: {
      projects: null as string | null,
      stories: null as string | null,
      tests: null as string | null,
      knowledge: null as string | null
    }
  }),

  getters: {
    hasProjects: (state) => state.projects.length > 0,
    hasCurrentProject: (state) => state.currentProject !== null,
    canImproveStory: (state) => state.currentProject && state.currentStory.trim().length > 0,
    canGenerateTests: (state) => state.currentProject && state.currentStory.trim().length > 0,
    
    projectById: (state) => (id: string) => 
      state.projects.find(p => p.id === id),
    
    storyComplexityColor: () => (complexity: string) => {
      switch (complexity) {
        case 'low': return 'text-green-600'
        case 'medium': return 'text-yellow-600'
        case 'high': return 'text-red-600'
        default: return 'text-gray-600'
      }
    },
    
    testPriorityColor: () => (priority: string) => {
      switch (priority) {
        case 'high': return 'text-red-600'
        case 'medium': return 'text-yellow-600'
        case 'low': return 'text-green-600'
        default: return 'text-gray-600'
      }
    }
  },

  actions: {
    // Proyectos
    async fetchProjects() {
      this.loading.projects = true
      this.errors.projects = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request('/api/rag/projects')
        
        if (response.success) {
          this.projects = response.projects
        } else {
          throw new Error(response.error || 'Failed to fetch projects')
        }
      } catch (error: any) {
        this.errors.projects = error.message
        console.error('Error fetching projects:', error)
      } finally {
        this.loading.projects = false
      }
    },

    async createProject(name: string, description?: string, initialDocuments?: Array<{
      content: string
      type: 'story' | 'requirement' | 'test' | 'documentation'
      source?: string
    }>) {
      this.loading.projects = true
      this.errors.projects = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request('/api/rag/projects', {
          method: 'POST',
          body: JSON.stringify({
            name,
            description,
            initialDocuments
          })
        })
        
        if (response.success) {
          await this.fetchProjects() // Refresh lista
          const newProject = this.projects.find(p => p.id === response.projectId)
          if (newProject) {
            this.setCurrentProject(newProject)
          }
          return response.projectId
        } else {
          throw new Error(response.error || 'Failed to create project')
        }
      } catch (error: any) {
        this.errors.projects = error.message
        console.error('Error creating project:', error)
        throw error
      } finally {
        this.loading.projects = false
      }
    },

    async deleteProject(projectId: string) {
      this.loading.projects = true
      this.errors.projects = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/rag/projects/${projectId}/knowledge`, {
          method: 'DELETE'
        })
        
        if (response.success) {
          await this.fetchProjects() // Refresh lista
          if (this.currentProject?.id === projectId) {
            this.currentProject = null
            this.knowledgeStats = null
          }
        } else {
          throw new Error(response.error || 'Failed to delete project')
        }
      } catch (error: any) {
        this.errors.projects = error.message
        console.error('Error deleting project:', error)
        throw error
      } finally {
        this.loading.projects = false
      }
    },

    setCurrentProject(project: RAGProject) {
      this.currentProject = project
      this.fetchKnowledgeStats()
    },

    // Knowledge Base
    async fetchKnowledgeStats() {
      if (!this.currentProject) return
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/rag/projects/${this.currentProject.id}/knowledge`)
        
        if (response.success) {
          this.knowledgeStats = response.knowledge
        } else {
          console.error('Failed to fetch knowledge stats:', response.error)
        }
      } catch (error: any) {
        console.error('Error fetching knowledge stats:', error)
      }
    },

    async addKnowledge(documents: Array<{
      content: string
      type: 'story' | 'requirement' | 'test' | 'documentation'
      source?: string
      metadata?: Record<string, any>
    }>) {
      if (!this.currentProject) throw new Error('No current project selected')
      
      this.loading.addKnowledge = true
      this.errors.knowledge = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/rag/projects/${this.currentProject.id}/knowledge`, {
          method: 'POST',
          body: JSON.stringify({ documents })
        })
        
        if (response.success) {
          await this.fetchKnowledgeStats() // Refresh stats
          return response.documentsAdded
        } else {
          throw new Error(response.error || 'Failed to add knowledge')
        }
      } catch (error: any) {
        this.errors.knowledge = error.message
        console.error('Error adding knowledge:', error)
        throw error
      } finally {
        this.loading.addKnowledge = false
      }
    },

    // Stories
    async improveStory(story: string, context?: string) {
      if (!this.currentProject) throw new Error('No current project selected')
      
      this.loading.improveStory = true
      this.errors.stories = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/rag/${this.currentProject.id}/stories/improve`, {
          method: 'POST',
          body: JSON.stringify({ story, context })
        })
        
        if (response.success) {
          this.improvedStory = response.improvedStory
          this.storyHistory.unshift(response.improvedStory)
          
          // Limitar historial a 10 items
          if (this.storyHistory.length > 10) {
            this.storyHistory = this.storyHistory.slice(0, 10)
          }
          
          return response.improvedStory
        } else {
          throw new Error(response.error || 'Failed to improve story')
        }
      } catch (error: any) {
        this.errors.stories = error.message
        console.error('Error improving story:', error)
        throw error
      } finally {
        this.loading.improveStory = false
      }
    },

    // Tests
    async generateTests(story: string, acceptanceCriteria?: string[]) {
      if (!this.currentProject) throw new Error('No current project selected')
      
      this.loading.generateTests = true
      this.errors.tests = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/rag/${this.currentProject.id}/stories/tests`, {
          method: 'POST',
          body: JSON.stringify({ story, acceptanceCriteria })
        })
        
        if (response.success) {
          this.currentTestCases = response.testCases
          this.currentEdgeCases = response.edgeCases
          
          // Añadir al historial
          this.testHistory.unshift({
            story,
            testCases: response.testCases,
            edgeCases: response.edgeCases,
            generatedAt: new Date().toISOString()
          })
          
          // Limitar historial a 10 items
          if (this.testHistory.length > 10) {
            this.testHistory = this.testHistory.slice(0, 10)
          }
          
          return {
            testCases: response.testCases,
            edgeCases: response.edgeCases,
            coverage: response.coverage
          }
        } else {
          throw new Error(response.error || 'Failed to generate tests')
        }
      } catch (error: any) {
        this.errors.tests = error.message
        console.error('Error generating tests:', error)
        throw error
      } finally {
        this.loading.generateTests = false
      }
    },

    // UI Actions
    setCurrentStory(story: string) {
      this.currentStory = story
    },

    clearErrors() {
      this.errors = {
        projects: null,
        stories: null,
        tests: null,
        knowledge: null
      }
    },

    resetStoryState() {
      this.currentStory = ''
      this.improvedStory = null
      this.currentTestCases = []
      this.currentEdgeCases = []
    }
  }
})