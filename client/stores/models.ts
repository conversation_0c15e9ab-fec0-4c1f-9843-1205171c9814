// Models Store - Estado para gestión de modelos de IA
import { defineStore } from 'pinia'
import { useApiStore } from './api'
import type { 
  ModelConfig, 
  CreateModelRequest, 
  UpdateModelRequest,
  ModelUsageStats,
  ModelSelectionConfig,
  ModelTestRequest
} from '~/types/models'

interface ModelsState {
  models: ModelConfig[]
  currentModel: ModelConfig | null
  usageStats: ModelUsageStats[]
  agentSelections: ModelSelectionConfig[]
  providers: string[]
  categories: string[]
  loading: {
    models: boolean
    stats: boolean
    agents: boolean
    test: boolean
    create: boolean
    update: boolean
    delete: boolean
  }
  error: string | null
  totalCost: number
  totalRequests: number
}

export const useModelsStore = defineStore('models', {
  state: (): ModelsState => ({
    models: [],
    currentModel: null,
    usageStats: [],
    agentSelections: [],
    providers: [],
    categories: [],
    loading: {
      models: false,
      stats: false,
      agents: false,
      test: false,
      create: false,
      update: false,
      delete: false
    },
    error: null,
    totalCost: 0,
    totalRequests: 0
  }),

  getters: {
    // Filtros de modelos
    enabledModels: (state) => state.models.filter(m => m.enabled),
    disabledModels: (state) => state.models.filter(m => !m.enabled),
    
    modelsByProvider: (state) => (provider: string) => 
      state.models.filter(m => m.provider === provider),
    
    modelsByCategory: (state) => (category: string) => 
      state.models.filter(m => m.category === category),

    chatModels: (state) => state.models.filter(m => m.category === 'chat'),
    embeddingModels: (state) => state.models.filter(m => m.category === 'embedding'),

    // Estadísticas calculadas
    avgCostPerRequest: (state) => 
      state.totalRequests > 0 ? state.totalCost / state.totalRequests : 0,

    mostUsedModel: (state) => {
      if (state.usageStats.length === 0) return null
      return state.usageStats.reduce((prev, current) => 
        prev.total_requests > current.total_requests ? prev : current
      )
    },

    mostExpensiveModel: (state) => {
      if (state.usageStats.length === 0) return null
      return state.usageStats.reduce((prev, current) => 
        prev.total_cost > current.total_cost ? prev : current
      )
    },

    // Configuraciones de agentes
    getAgentModel: (state) => (agentType: string) => 
      state.agentSelections.find(s => s.agent_type === agentType),

    // Estado de carga
    isLoading: (state) => Object.values(state.loading).some(loading => loading)
  },

  actions: {
    // Gestión de modelos
    async fetchModels(provider?: string, category?: string, enabled?: boolean) {
      this.loading.models = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const params = new URLSearchParams()
        
        if (provider) params.append('provider', provider)
        if (category) params.append('category', category)
        if (enabled !== undefined) params.append('enabled', String(enabled))
        
        const endpoint = `/api/models${params.toString() ? '?' + params.toString() : ''}`
        const response = await apiStore.request(endpoint)
        
        if (response.success) {
          this.models = response.models || []
          this.providers = response.providers || []
          this.categories = response.categories || []
        } else {
          throw new Error(response.error || 'Failed to fetch models')
        }
      } catch (error) {
        console.error('Error fetching models:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch models'
        throw error
      } finally {
        this.loading.models = false
      }
    },

    async getModel(id: string) {
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/models/${encodeURIComponent(id)}`)
        
        if (response.success) {
          this.currentModel = response.model
          return response.model
        } else {
          throw new Error(response.error || 'Failed to get model')
        }
      } catch (error) {
        console.error('Error getting model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to get model'
        throw error
      }
    },

    async createModel(request: CreateModelRequest) {
      this.loading.create = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request('/api/models', {
          method: 'POST',
          body: JSON.stringify(request)
        })
        
        if (response.success) {
          // Refresh models list
          await this.fetchModels()
          return response.model
        } else {
          throw new Error(response.error || 'Failed to create model')
        }
      } catch (error) {
        console.error('Error creating model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to create model'
        throw error
      } finally {
        this.loading.create = false
      }
    },

    async updateModel(id: string, request: UpdateModelRequest) {
      this.loading.update = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/models/${encodeURIComponent(id)}`, {
          method: 'PUT',
          body: JSON.stringify(request)
        })
        
        if (response.success) {
          // Update local model
          const index = this.models.findIndex(m => m.id === id)
          if (index !== -1) {
            this.models[index] = response.model
          }
          
          if (this.currentModel?.id === id) {
            this.currentModel = response.model
          }
          
          return response.model
        } else {
          throw new Error(response.error || 'Failed to update model')
        }
      } catch (error) {
        console.error('Error updating model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to update model'
        throw error
      } finally {
        this.loading.update = false
      }
    },

    async deleteModel(id: string) {
      this.loading.delete = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request(`/api/models/${encodeURIComponent(id)}`, {
          method: 'DELETE'
        })
        
        if (response.success) {
          // Remove from local models
          this.models = this.models.filter(m => m.id !== id)
          
          if (this.currentModel?.id === id) {
            this.currentModel = null
          }
          
          return true
        } else {
          throw new Error(response.error || 'Failed to delete model')
        }
      } catch (error) {
        console.error('Error deleting model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to delete model'
        throw error
      } finally {
        this.loading.delete = false
      }
    },

    // Estadísticas
    async fetchUsageStats(modelId?: string) {
      this.loading.stats = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const endpoint = modelId 
          ? `/api/models/stats?model_id=${encodeURIComponent(modelId)}`
          : '/api/models/stats'
        
        const response = await apiStore.request(endpoint)
        
        if (response.success) {
          this.usageStats = response.stats || []
          this.totalCost = response.total_cost || 0
          this.totalRequests = response.total_requests || 0
        } else {
          throw new Error(response.error || 'Failed to fetch usage stats')
        }
      } catch (error) {
        console.error('Error fetching usage stats:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch usage stats'
        throw error
      } finally {
        this.loading.stats = false
      }
    },

    // Configuración de agentes
    async fetchAgentSelections() {
      this.loading.agents = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request('/api/models/agents')
        
        if (response.success) {
          this.agentSelections = response.selections || []
        } else {
          throw new Error(response.error || 'Failed to fetch agent selections')
        }
      } catch (error) {
        console.error('Error fetching agent selections:', error)
        this.error = error instanceof Error ? error.message : 'Failed to fetch agent selections'
        throw error
      } finally {
        this.loading.agents = false
      }
    },

    async updateAgentModel(agentType: string, modelId: string, fallbackModelId?: string) {
      this.loading.agents = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request('/api/models/agents', {
          method: 'PUT',
          body: JSON.stringify({
            agent_type: agentType,
            model_id: modelId,
            fallback_model_id: fallbackModelId
          })
        })
        
        if (response.success) {
          // Update local agent selection
          const index = this.agentSelections.findIndex(s => s.agent_type === agentType)
          if (index !== -1) {
            this.agentSelections[index] = {
              agent_type: agentType,
              model_id: modelId,
              fallback_model_id: fallbackModelId,
              enabled: true
            }
          } else {
            this.agentSelections.push({
              agent_type: agentType,
              model_id: modelId,
              fallback_model_id: fallbackModelId,
              enabled: true
            })
          }
          
          return true
        } else {
          throw new Error(response.error || 'Failed to update agent model')
        }
      } catch (error) {
        console.error('Error updating agent model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to update agent model'
        throw error
      } finally {
        this.loading.agents = false
      }
    },

    // Testing
    async testModel(request: ModelTestRequest) {
      this.loading.test = true
      this.error = null
      
      try {
        const apiStore = useApiStore()
        const response = await apiStore.request('/api/models/test', {
          method: 'POST',
          body: JSON.stringify(request)
        })
        
        return response // Return the full response (success or error)
      } catch (error) {
        console.error('Error testing model:', error)
        this.error = error instanceof Error ? error.message : 'Failed to test model'
        throw error
      } finally {
        this.loading.test = false
      }
    },

    // Utilidades
    clearError() {
      this.error = null
    },

    clearCurrentModel() {
      this.currentModel = null
    },

    // Formateo de datos
    formatCost(cost: number): string {
      return `$${cost.toFixed(6)}`
    },

    formatTokens(tokens: number): string {
      if (tokens >= 1000000) {
        return `${(tokens / 1000000).toFixed(1)}M`
      } else if (tokens >= 1000) {
        return `${(tokens / 1000).toFixed(1)}K`
      }
      return tokens.toString()
    },

    getProviderColor(provider: string): string {
      const colors = {
        openai: 'green',
        anthropic: 'orange',
        openrouter: 'blue',
        cohere: 'purple'
      }
      return colors[provider as keyof typeof colors] || 'gray'
    },

    getCategoryIcon(category: string): string {
      const icons = {
        chat: '💬',
        embedding: '🔗',
        completion: '📝'
      }
      return icons[category as keyof typeof icons] || '🤖'
    }
  }
})