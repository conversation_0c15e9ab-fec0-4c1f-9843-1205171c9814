// Model Management Types for Frontend - Enhanced Version
export interface ModelProvider {
  id: string;
  name: string;
  display_name: string;
  api_base_url: string;
  auth_type: 'api_key' | 'oauth' | 'bearer';
  config: Record<string, any>;
  enabled: boolean;
  rate_limit_rpm: number;
  rate_limit_tpm: number;
}

export interface ModelCategory {
  id: string;
  name: string;
  display_name: string;
  description: string;
  icon: string;
  enabled: boolean;
}

export interface ModelConfig {
  id: string;
  model_id: string;
  name: string;
  provider_id: string;
  category_id: string;
  
  // Configuración técnica
  context_length: number;
  max_tokens?: number;
  supports_streaming: boolean;
  supports_function_calling: boolean;
  supports_vision: boolean;
  
  // Precios (por 1M tokens)
  input_cost_per_1m: number;
  output_cost_per_1m: number;
  
  // Parámetros por defecto
  default_temperature: number;
  default_top_p: number;
  default_top_k?: number;
  default_max_tokens?: number;
  
  // Estado
  enabled: boolean;
  features: string[];
  metadata: Record<string, any>;
}

export interface CreateModelRequest {
  model_id: string;
  name: string;
  provider_name: string;
  category_name: string;
  context_length: number;
  max_tokens?: number;
  supports_streaming?: boolean;
  supports_function_calling?: boolean;
  supports_vision?: boolean;
  input_cost_per_1m: number;
  output_cost_per_1m: number;
  default_temperature?: number;
  default_top_p?: number;
  default_top_k?: number;
  default_max_tokens?: number;
  enabled?: boolean;
  features?: string[];
  metadata?: Record<string, any>;
}

export interface UpdateModelRequest {
  name?: string;
  context_length?: number;
  max_tokens?: number;
  supports_streaming?: boolean;
  supports_function_calling?: boolean;
  supports_vision?: boolean;
  input_cost_per_1m?: number;
  output_cost_per_1m?: number;
  default_temperature?: number;
  default_top_p?: number;
  default_top_k?: number;
  default_max_tokens?: number;
  enabled?: boolean;
  features?: string[];
  metadata?: Record<string, any>;
}

export interface AgentConfiguration {
  id: string;
  agent_type: string;
  display_name: string;
  description: string;
  primary_model_id: string;
  fallback_model_id?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  system_prompt?: string;
  enabled: boolean;
}

export interface UpdateAgentConfigRequest {
  primary_model_id?: string;
  fallback_model_id?: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  system_prompt?: string;
  enabled?: boolean;
}

export interface ModelUsageStats {
  model_id: string;
  total_requests: number;
  total_tokens_input: number;
  total_tokens_output: number;
  total_cost: number;
  last_used: string;
  avg_response_time: number;
  success_rate: number;
}

// Backward compatibility - mantener interfaces existentes con alias
export type ModelSelectionConfig = AgentConfiguration;

export interface ModelTestRequest {
  model_id: string;
  prompt: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

export interface ModelTestResponse {
  success: boolean;
  response?: string;
  tokens_used?: number;
  response_time?: number;
  cost?: number;
  error?: string;
}

// API Response types
export interface ModelsApiResponse {
  success: boolean;
  models: ModelConfig[];
  providers: string[];
  categories: string[];
  total: number;
  error?: string;
}

export interface ModelApiResponse {
  success: boolean;
  model: ModelConfig;
  error?: string;
}

export interface AgentConfigurationsApiResponse {
  success: boolean;
  selections: AgentConfiguration[];
  error?: string;
}

export interface ModelStatsApiResponse {
  success: boolean;
  stats: ModelUsageStats[];
  total_cost: number;
  total_requests: number;
  error?: string;
}