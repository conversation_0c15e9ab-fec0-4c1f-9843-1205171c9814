// Página de pruebas de API
import { Head } from "$fresh/runtime.ts";
import { PageProps } from "$fresh/server.ts";
import AdminLayout from "../../src/layouts/AdminLayout.tsx";
import APITester from "../../src/components/admin/APITester.tsx";

export default function AdminTesting(_props: PageProps) {
  return (
    <>
      <Head>
        <title>AERY Admin - Pruebas de API</title>
        <meta name="description" content="Herramientas de prueba para la API de AERY" />
      </Head>
      
      <AdminLayout>
        <APITester />
      </AdminLayout>
    </>
  );
}