// Página de monitoreo de servicios
import { Head } from "$fresh/runtime.ts";
import { PageProps } from "$fresh/server.ts";
import AdminLayout from "../../src/layouts/AdminLayout.tsx";
import ServicesMonitor from "../../src/components/admin/ServicesMonitor.tsx";

export default function AdminServices(_props: PageProps) {
  return (
    <>
      <Head>
        <title>AERY Admin - Monitoreo de Servicios</title>
        <meta name="description" content="Estado y monitoreo de todos los servicios de AERY" />
      </Head>
      
      <AdminLayout>
        <ServicesMonitor />
      </AdminLayout>
    </>
  );
}