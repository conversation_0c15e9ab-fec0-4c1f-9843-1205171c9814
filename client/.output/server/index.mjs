import process from 'node:process';globalThis._importMeta_={url:import.meta.url,env:process.env};import 'node:http';
import 'node:https';
export { n as default } from './chunks/nitro/nitro.mjs';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:path';
import 'node:crypto';
import 'node:url';
import '@iconify/utils';
import 'consola';
import 'node:module';
//# sourceMappingURL=index.mjs.map
