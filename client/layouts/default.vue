<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <h1 class="text-xl font-bold text-gray-900">
                🤖 AERY Dashboard
              </h1>
            </div>
            
            <!-- Navigation Links -->
            <div class="hidden md:ml-6 md:flex md:space-x-8" v-if="authStore.isLoggedIn">
              <NuxtLink 
                to="/" 
                class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                active-class="border-blue-500 text-blue-600"
              >
                Dashboard
              </NuxtLink>
              <NuxtLink 
                to="/tests" 
                class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                active-class="border-blue-500 text-blue-600"
              >
                API Tests
              </NuxtLink>
              <NuxtLink 
                to="/tasks" 
                class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                active-class="border-blue-500 text-blue-600"
              >
                Execute Tasks
              </NuxtLink>
              <NuxtLink 
                to="/prescripts" 
                class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                active-class="border-blue-500 text-blue-600"
              >
                Pre-scripts
              </NuxtLink>
              <NuxtLink 
                to="/rag" 
                class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                active-class="border-blue-500 text-blue-600"
              >
                RAG Stories
              </NuxtLink>
              <NuxtLink 
                to="/models" 
                class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors"
                active-class="border-blue-500 text-blue-600"
              >
                Models
              </NuxtLink>
            </div>
          </div>
          
          <!-- User Menu -->
          <div class="flex items-center" v-if="authStore.isLoggedIn">
            <UDropdown :items="userMenuItems">
              <UButton 
                variant="ghost" 
                :label="authStore.userEmail"
                trailing-icon="i-heroicons-chevron-down-20-solid"
              />
            </UDropdown>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <slot />
    </main>

    <!-- Toast Notifications -->
    <UNotifications />
  </div>
</template>

<script setup>
const authStore = useAuthStore()
const toast = useToast()

// Initialize auth on mount
onMounted(() => {
  authStore.initializeAuth()
})

const userMenuItems = [
  [{
    label: authStore.userEmail,
    slot: 'account',
    disabled: true
  }], [{
    label: 'Sign out',
    icon: 'i-heroicons-arrow-right-on-rectangle',
    click: () => {
      authStore.logout()
      toast.add({
        title: 'Logged out successfully',
        color: 'green'
      })
    }
  }]
]
</script>
