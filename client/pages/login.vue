<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          🤖 AERY Dashboard
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Sign in to your admin account
        </p>
      </div>
      
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">Sign In</h3>
        </template>
        
        <UForm :schema="schema" :state="state" @submit="handleLogin" class="space-y-4">
          <UFormGroup label="Email" name="email">
            <UInput 
              v-model="state.email" 
              type="email" 
              placeholder="<EMAIL>"
              icon="i-heroicons-envelope"
            />
          </UFormGroup>
          
          <UFormGroup label="Password" name="password">
            <UInput 
              v-model="state.password" 
              type="password" 
              placeholder="Enter your password"
              icon="i-heroicons-lock-closed"
            />
          </UFormGroup>
          
          <UButton 
            type="submit" 
            block 
            :loading="authStore.loading"
            :disabled="authStore.loading"
          >
            Sign In
          </UButton>
        </UForm>
        
        <template #footer>
          <div class="text-center">
            <p class="text-sm text-gray-600">
              Don't have an account?
              <NuxtLink to="/register" class="font-medium text-blue-600 hover:text-blue-500">
                Sign up
              </NuxtLink>
            </p>
          </div>
        </template>
      </UCard>
      
      <!-- API Status -->
      <div class="mt-8">
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h4 class="text-sm font-medium text-gray-900">API Status</h4>
              <UButton 
                variant="ghost" 
                size="xs" 
                icon="i-heroicons-arrow-path"
                :loading="apiStore.loading.health"
                @click="checkApiHealth"
              >
                Refresh
              </UButton>
            </div>
          </template>
          
          <div class="flex items-center space-x-2">
            <UBadge 
              :color="apiStore.healthStatusColor" 
              variant="subtle"
            >
              {{ apiStore.healthStatus }}
            </UBadge>
            <span class="text-sm text-gray-500" v-if="apiStore.lastHealthCheck">
              Last checked: {{ formatTime(apiStore.lastHealthCheck) }}
            </span>
          </div>
        </UCard>
      </div>
    </div>
  </div>
</template>

<script setup>
import { z } from 'zod'

definePageMeta({
  layout: false,
  middleware: 'auth'
})

const authStore = useAuthStore()
const apiStore = useApiStore()
const toast = useToast()

// Form schema
const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(6, 'Password must be at least 6 characters')
})

// Form state
const state = reactive({
  email: '',
  password: ''
})

// Check API health on mount
onMounted(() => {
  checkApiHealth()
})

const handleLogin = async (data) => {
  console.log('Form data:', data)
  console.log('State values:', state)
  const result = await authStore.login(state.email, state.password)
  
  if (result.success) {
    toast.add({
      title: 'Login successful',
      description: 'Welcome to AERY Dashboard!',
      color: 'green'
    })
    navigateTo('/')
  } else {
    toast.add({
      title: 'Login failed',
      description: result.error,
      color: 'red'
    })
  }
}

const checkApiHealth = () => {
  apiStore.checkHealth()
}

const formatTime = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}
</script>
