<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200 mb-6">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">🤖 Model Management</h1>
            <p class="text-sm text-gray-600">Administra modelos de IA, configuraciones y asignaciones por agente</p>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- View toggle -->
            <UTabs 
              v-model="activeTab" 
              :items="tabItems"
              class="w-auto"
            />
            
            <!-- Refresh button -->
            <UButton 
              @click="refreshData"
              :loading="modelsStore.loading.models"
              variant="outline"
              icon="i-heroicons-arrow-path"
            >
              Refresh
            </UButton>
            
            <!-- Add model button -->
            <UButton 
              @click="showCreateModal = true"
              icon="i-heroicons-plus"
              color="blue"
            >
              Add Model
            </UButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="px-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <span class="text-blue-600 text-lg">🤖</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Models</p>
              <p class="text-2xl font-bold text-gray-900">{{ modelsStore.models.length }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <span class="text-green-600 text-lg">✅</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Enabled</p>
              <p class="text-2xl font-bold text-gray-900">{{ modelsStore.enabledModels.length }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <span class="text-purple-600 text-lg">📊</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Requests</p>
              <p class="text-2xl font-bold text-gray-900">{{ modelsStore.formatTokens(modelsStore.totalRequests) }}</p>
            </div>
          </div>
        </UCard>

        <UCard>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span class="text-yellow-600 text-lg">💰</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Cost</p>
              <p class="text-2xl font-bold text-gray-900">{{ modelsStore.formatCost(modelsStore.totalCost) }}</p>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- Tabs -->
    <div class="px-6">
      <UTabs v-model="activeTab" :items="tabItems">
        <!-- Models Tab -->
        <template #models="{ item }">
          <div class="py-6">
            <!-- Filters -->
            <div class="mb-6 flex flex-wrap items-center gap-4">
              <USelectMenu
                v-model="selectedProvider"
                :options="providerOptions"
                placeholder="All Providers"
                @change="filterModels"
              />
              
              <USelectMenu
                v-model="selectedCategory"
                :options="categoryOptions"
                placeholder="All Categories"
                @change="filterModels"
              />
              
              <USelectMenu
                v-model="selectedStatus"
                :options="statusOptions"
                placeholder="All Status"
                @change="filterModels"
              />
              
              <UButton 
                v-if="hasActiveFilters"
                @click="clearFilters"
                variant="ghost"
                size="sm"
                icon="i-heroicons-x-mark"
              >
                Clear Filters
              </UButton>
            </div>

            <!-- Models Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <UCard 
                v-for="model in filteredModels" 
                :key="model.id"
                class="hover:shadow-md transition-shadow cursor-pointer"
                @click="selectModel(model)"
              >
                <div class="flex items-start justify-between mb-3">
                  <div class="flex items-center space-x-2">
                    <span class="text-lg">{{ modelsStore.getCategoryIcon(model.category) }}</span>
                    <div>
                      <h3 class="font-semibold text-gray-900">{{ model.name }}</h3>
                      <p class="text-xs text-gray-500">{{ model.id }}</p>
                    </div>
                  </div>
                  
                  <UBadge 
                    :color="modelsStore.getProviderColor(model.provider)"
                    variant="soft"
                  >
                    {{ model.provider }}
                  </UBadge>
                </div>

                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-600">Category:</span>
                    <span class="font-medium">{{ model.category }}</span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-gray-600">Context:</span>
                    <span class="font-medium">{{ modelsStore.formatTokens(model.context_length) }}</span>
                  </div>
                  
                  <div class="flex justify-between">
                    <span class="text-gray-600">Pricing:</span>
                    <span class="font-medium">${{ model.pricing.input }}/${{ model.pricing.output }}</span>
                  </div>
                  
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">Status:</span>
                    <UBadge 
                      :color="model.enabled ? 'green' : 'red'"
                      variant="soft"
                    >
                      {{ model.enabled ? 'Enabled' : 'Disabled' }}
                    </UBadge>
                  </div>
                </div>

                <div class="mt-4 flex justify-end space-x-2">
                  <UButton
                    @click.stop="testModel(model)"
                    size="xs"
                    variant="outline"
                    icon="i-heroicons-beaker"
                  >
                    Test
                  </UButton>
                  
                  <UButton
                    @click.stop="editModel(model)"
                    size="xs"
                    variant="outline"
                    icon="i-heroicons-pencil"
                  >
                    Edit
                  </UButton>
                </div>
              </UCard>
            </div>

            <!-- Empty state -->
            <div v-if="filteredModels.length === 0" class="text-center py-12">
              <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-gray-400 text-2xl">🤖</span>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No models found</h3>
              <p class="text-gray-500 mb-4">Try adjusting your filters or add a new model.</p>
              <UButton @click="showCreateModal = true" icon="i-heroicons-plus">
                Add First Model
              </UButton>
            </div>
          </div>
        </template>

        <!-- Statistics Tab -->
        <template #stats="{ item }">
          <ModelsStats />
        </template>

        <!-- Agent Configuration Tab -->
        <template #agents="{ item }">
          <ModelsAgentsEnhanced />
        </template>
      </UTabs>
    </div>

    <!-- Create Model Modal -->
    <ModelsCreateModal 
      v-model="showCreateModal"
      @created="onModelCreated"
    />

    <!-- Edit Model Modal -->
    <ModelsEditModal 
      v-model="showEditModal"
      :model="selectedModel"
      @updated="onModelUpdated"
    />

    <!-- Test Model Modal -->
    <ModelsTestModal 
      v-model="showTestModal"
      :model="selectedModel"
    />

    <!-- Model Details Modal -->
    <ModelsDetailModal 
      v-model="showDetailModal"
      :model="selectedModel"
      @edit="editModel"
      @test="testModel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useModelsStore } from '~/stores/models'
import type { ModelConfig } from '~/types/models'

// Stores
const modelsStore = useModelsStore()
const { $toast } = useNuxtApp()

// State
const activeTab = ref('models')
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showTestModal = ref(false)
const showDetailModal = ref(false)
const selectedModel = ref<ModelConfig | null>(null)

// Filters
const selectedProvider = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')

// Computed
const tabItems = [
  { key: 'models', label: 'Models', icon: 'i-heroicons-cpu-chip' },
  { key: 'stats', label: 'Statistics', icon: 'i-heroicons-chart-bar' },
  { key: 'agents', label: 'Agent Config', icon: 'i-heroicons-cog-6-tooth' }
]

const providerOptions = computed(() => [
  { label: 'All Providers', value: '' },
  ...modelsStore.providers.map(p => ({ 
    label: p.charAt(0).toUpperCase() + p.slice(1), 
    value: p 
  }))
])

const categoryOptions = computed(() => [
  { label: 'All Categories', value: '' },
  ...modelsStore.categories.map(c => ({ 
    label: c.charAt(0).toUpperCase() + c.slice(1), 
    value: c 
  }))
])

const statusOptions = [
  { label: 'All Status', value: '' },
  { label: 'Enabled', value: 'enabled' },
  { label: 'Disabled', value: 'disabled' }
]

const hasActiveFilters = computed(() => 
  selectedProvider.value || selectedCategory.value || selectedStatus.value
)

const filteredModels = computed(() => {
  let models = modelsStore.models
  
  if (selectedProvider.value) {
    models = models.filter(m => m.provider === selectedProvider.value)
  }
  
  if (selectedCategory.value) {
    models = models.filter(m => m.category === selectedCategory.value)
  }
  
  if (selectedStatus.value) {
    const enabled = selectedStatus.value === 'enabled'
    models = models.filter(m => m.enabled === enabled)
  }
  
  return models
})

// Methods
const refreshData = async () => {
  try {
    await Promise.all([
      modelsStore.fetchModels(),
      modelsStore.fetchUsageStats(),
      modelsStore.fetchAgentSelections()
    ])
  } catch (error: any) {
    $toast.add({
      title: 'Error refreshing data',
      description: error.message,
      color: 'red'
    })
  }
}

const filterModels = () => {
  // Filters are reactive, so this just triggers reactivity
}

const clearFilters = () => {
  selectedProvider.value = ''
  selectedCategory.value = ''
  selectedStatus.value = ''
}

const selectModel = (model: ModelConfig) => {
  selectedModel.value = model
  showDetailModal.value = true
}

const editModel = (model: ModelConfig) => {
  selectedModel.value = model
  showEditModal.value = true
}

const testModel = (model: ModelConfig) => {
  selectedModel.value = model
  showTestModal.value = true
}

const onModelCreated = () => {
  refreshData()
  $toast.add({
    title: 'Model created successfully',
    color: 'green'
  })
}

const onModelUpdated = () => {
  refreshData()
  $toast.add({
    title: 'Model updated successfully',
    color: 'green'
  })
}

// Initialize data
onMounted(() => {
  refreshData()
})
</script>