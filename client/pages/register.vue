<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          🤖 AERY Dashboard
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Create your admin account
        </p>
      </div>
      
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">Sign Up</h3>
        </template>
        
        <UForm :schema="schema" :state="state" @submit="handleRegister" class="space-y-4">
          <UFormGroup label="Email" name="email">
            <UInput 
              v-model="state.email" 
              type="email" 
              placeholder="<EMAIL>"
              icon="i-heroicons-envelope"
            />
          </UFormGroup>
          
          <UFormGroup label="Password" name="password">
            <UInput 
              v-model="state.password" 
              type="password" 
              placeholder="Enter your password"
              icon="i-heroicons-lock-closed"
            />
          </UFormGroup>
          
          <UFormGroup label="Confirm Password" name="confirmPassword">
            <UInput 
              v-model="state.confirmPassword" 
              type="password" 
              placeholder="Confirm your password"
              icon="i-heroicons-lock-closed"
            />
          </UFormGroup>
          
          <UFormGroup label="Plan" name="plan">
            <USelect 
              v-model="state.plan" 
              :options="planOptions"
              placeholder="Select a plan"
            />
          </UFormGroup>
          
          <UButton 
            type="submit" 
            block 
            :loading="authStore.loading"
            :disabled="authStore.loading"
          >
            Create Account
          </UButton>
        </UForm>
        
        <template #footer>
          <div class="text-center">
            <p class="text-sm text-gray-600">
              Already have an account?
              <NuxtLink to="/login" class="font-medium text-blue-600 hover:text-blue-500">
                Sign in
              </NuxtLink>
            </p>
          </div>
        </template>
      </UCard>
    </div>
  </div>
</template>

<script setup>
import { z } from 'zod'

definePageMeta({
  layout: false,
  middleware: 'auth'
})

const authStore = useAuthStore()
const toast = useToast()

// Form schema
const schema = z.object({
  email: z.string().email('Invalid email'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string(),
  plan: z.string().min(1, 'Please select a plan')
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// Form state
const state = reactive({
  email: '',
  password: '',
  confirmPassword: '',
  plan: 'basic'
})

const planOptions = [
  { label: 'Basic', value: 'basic' },
  { label: 'Pro', value: 'pro' },
  { label: 'Enterprise', value: 'enterprise' }
]

const handleRegister = async (data) => {
  const result = await authStore.register(data.email, data.password, data.plan)
  
  if (result.success) {
    toast.add({
      title: 'Registration successful',
      description: 'Welcome to AERY Dashboard!',
      color: 'green'
    })
    navigateTo('/')
  } else {
    toast.add({
      title: 'Registration failed',
      description: result.error,
      color: 'red'
    })
  }
}
</script>
