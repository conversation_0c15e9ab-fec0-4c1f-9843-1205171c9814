<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">API Tests</h1>
        <p class="mt-1 text-sm text-gray-500">
          Test all API endpoints and verify system functionality
        </p>
      </div>

      <div class="flex space-x-3">
        <UButton
          @click="runAllTests"
          :loading="runningAllTests"
          icon="i-heroicons-play"
          color="primary"
        >
          Run All Tests
        </UButton>

        <UButton
          @click="clearResults"
          variant="outline"
          icon="i-heroicons-trash"
        >
          Clear Results
        </UButton>
      </div>
    </div>

    <!-- Test Summary -->
    <div v-if="testResults.length > 0" class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <div class="text-center">
          <p class="text-2xl font-bold text-green-600">{{ passedTests }}</p>
          <p class="text-sm text-gray-500">Passed</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <p class="text-2xl font-bold text-red-600">{{ failedTests }}</p>
          <p class="text-sm text-gray-500">Failed</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <p class="text-2xl font-bold text-gray-600">{{ totalTests }}</p>
          <p class="text-sm text-gray-500">Total</p>
        </div>
      </UCard>

      <UCard>
        <div class="text-center">
          <p class="text-2xl font-bold text-blue-600">{{ successRate }}%</p>
          <p class="text-sm text-gray-500">Success Rate</p>
        </div>
      </UCard>
    </div>

    <!-- Individual Tests -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Health Check Test -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Health Check</h3>
            <UButton
              @click="runHealthTest"
              :loading="loadingStates.health"
              size="xs"
              icon="i-heroicons-play"
            >
              Test
            </UButton>
          </div>
        </template>

        <div class="space-y-3">
          <p class="text-sm text-gray-600">
            Tests the <code>/health</code> endpoint to verify API availability.
          </p>

          <div class="flex items-center space-x-2">
            <UBadge variant="outline">GET /health</UBadge>
            <UBadge variant="outline">No Auth Required</UBadge>
          </div>

          <TestResult :result="getTestResult('health')" />
        </div>
      </UCard>

      <!-- Authentication Tests -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Authentication</h3>
            <UButton
              @click="runAuthTests"
              :loading="loadingStates.auth"
              size="xs"
              icon="i-heroicons-play"
            >
              Test
            </UButton>
          </div>
        </template>

        <div class="space-y-3">
          <p class="text-sm text-gray-600">
            Tests user registration and login functionality.
          </p>

          <div class="flex flex-wrap gap-1">
            <UBadge variant="outline">POST /auth/register</UBadge>
            <UBadge variant="outline">POST /auth/login</UBadge>
          </div>

          <TestResult :result="getTestResult('auth')" />
        </div>
      </UCard>

      <!-- Metrics Test -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Metrics</h3>
            <UButton
              @click="runMetricsTest"
              :loading="loadingStates.metrics"
              size="xs"
              icon="i-heroicons-play"
            >
              Test
            </UButton>
          </div>
        </template>

        <div class="space-y-3">
          <p class="text-sm text-gray-600">
            Tests the <code>/metrics</code> endpoint for system metrics.
          </p>

          <div class="flex items-center space-x-2">
            <UBadge variant="outline">GET /metrics</UBadge>
            <UBadge variant="outline" color="yellow">Auth Required</UBadge>
          </div>

          <TestResult :result="getTestResult('metrics')" />
        </div>
      </UCard>

      <!-- Task Execution Test -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Task Execution</h3>
            <UButton
              @click="runTaskTest"
              :loading="loadingStates.tasks"
              size="xs"
              icon="i-heroicons-play"
            >
              Test
            </UButton>
          </div>
        </template>

        <div class="space-y-3">
          <p class="text-sm text-gray-600">
            Tests task execution with natural language instructions.
          </p>

          <div class="flex items-center space-x-2">
            <UBadge variant="outline">POST /execute</UBadge>
            <UBadge variant="outline" color="yellow">Auth Required</UBadge>
          </div>

          <TestResult :result="getTestResult('tasks')" />
        </div>
      </UCard>

      <!-- Pre-scripts Test -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Pre-scripts</h3>
            <UButton
              @click="runPrescriptsTest"
              :loading="loadingStates.prescripts"
              size="xs"
              icon="i-heroicons-play"
            >
              Test
            </UButton>
          </div>
        </template>

        <div class="space-y-3">
          <p class="text-sm text-gray-600">
            Tests pre-script management functionality.
          </p>

          <div class="flex flex-wrap gap-1">
            <UBadge variant="outline">GET /prescripts</UBadge>
            <UBadge variant="outline">POST /prescripts</UBadge>
            <UBadge variant="outline" color="yellow">Auth Required</UBadge>
          </div>

          <TestResult :result="getTestResult('prescripts')" />
        </div>
      </UCard>

      <!-- Error Handling Test -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Error Handling</h3>
            <UButton
              @click="runErrorTests"
              :loading="loadingStates.errors"
              size="xs"
              icon="i-heroicons-play"
            >
              Test
            </UButton>
          </div>
        </template>

        <div class="space-y-3">
          <p class="text-sm text-gray-600">
            Tests proper error responses for invalid requests.
          </p>

          <div class="flex flex-wrap gap-1">
            <UBadge variant="outline">404 Errors</UBadge>
            <UBadge variant="outline">401 Unauthorized</UBadge>
            <UBadge variant="outline">400 Bad Request</UBadge>
          </div>

          <TestResult :result="getTestResult('errors')" />
        </div>
      </UCard>
    </div>

    <!-- Detailed Results -->
    <UCard v-if="testResults.length > 0">
      <template #header>
        <h3 class="text-lg font-medium text-gray-900">Detailed Results</h3>
      </template>

      <div class="space-y-4">
        <div
          v-for="result in testResults"
          :key="result.category"
          class="border rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-2">
            <h4 class="font-medium text-gray-900">{{ result.category }}</h4>
            <UBadge
              :color="result.success ? 'green' : 'red'"
              variant="subtle"
            >
              {{ result.success ? 'PASS' : 'FAIL' }}
            </UBadge>
          </div>

          <div class="text-sm space-y-1">
            <p><strong>Duration:</strong> {{ result.duration }}ms</p>
            <p><strong>Status:</strong> {{ result.status }}</p>
            <p v-if="result.error" class="text-red-600">
              <strong>Error:</strong> {{ result.error }}
            </p>
            <p v-if="result.details" class="text-gray-600">
              <strong>Details:</strong> {{ result.details }}
            </p>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

const authStore = useAuthStore()
const apiStore = useApiStore()
const toast = useToast()

// Reactive data
const runningAllTests = ref(false)
const testResults = ref([])

const loadingStates = reactive({
  health: false,
  auth: false,
  metrics: false,
  tasks: false,
  prescripts: false,
  errors: false
})

// Computed properties
const passedTests = computed(() => testResults.value.filter(r => r.success).length)
const failedTests = computed(() => testResults.value.filter(r => !r.success).length)
const totalTests = computed(() => testResults.value.length)
const successRate = computed(() =>
  totalTests.value > 0 ? Math.round((passedTests.value / totalTests.value) * 100) : 0
)

// Helper functions
const getTestResult = (category) => {
  return testResults.value.find(r => r.category === category)
}

const addTestResult = (category, success, status, duration, error = null, details = null) => {
  const existingIndex = testResults.value.findIndex(r => r.category === category)
  const result = { category, success, status, duration, error, details, timestamp: new Date() }

  if (existingIndex >= 0) {
    testResults.value[existingIndex] = result
  } else {
    testResults.value.push(result)
  }
}

// Test functions
const runHealthTest = async () => {
  loadingStates.health = true
  const startTime = Date.now()

  try {
    const result = await apiStore.checkHealth()
    const duration = Date.now() - startTime

    addTestResult(
      'health',
      result.success,
      result.success ? 'API is healthy' : 'API is unhealthy',
      duration,
      result.success ? null : 'Health check failed'
    )
  } catch (error) {
    const duration = Date.now() - startTime
    addTestResult('health', false, 'Error', duration, error.message)
  } finally {
    loadingStates.health = false
  }
}

const runAuthTests = async () => {
  loadingStates.auth = true
  const startTime = Date.now()

  try {
    // Test with current auth (should work if logged in)
    const config = useRuntimeConfig()
    const response = await $fetch(`${config.public.apiBase}/auth/login`, {
      method: 'POST',
      body: {
        email: authStore.userEmail,
        password: 'test' // This will fail but tests the endpoint
      }
    })

    const duration = Date.now() - startTime
    addTestResult('auth', false, 'Expected failure', duration, 'Invalid credentials (expected)')
  } catch (error) {
    const duration = Date.now() - startTime
    // 401 is expected for wrong password
    const success = error.status === 401
    addTestResult(
      'auth',
      success,
      success ? 'Auth endpoint working' : 'Auth endpoint error',
      duration,
      success ? null : error.message
    )
  } finally {
    loadingStates.auth = false
  }
}

const runMetricsTest = async () => {
  loadingStates.metrics = true
  const startTime = Date.now()

  try {
    const result = await apiStore.getMetrics()
    const duration = Date.now() - startTime

    addTestResult(
      'metrics',
      result.success,
      result.success ? 'Metrics retrieved' : 'Failed to get metrics',
      duration,
      result.success ? null : 'Metrics endpoint failed'
    )
  } catch (error) {
    const duration = Date.now() - startTime
    addTestResult('metrics', false, 'Error', duration, error.message)
  } finally {
    loadingStates.metrics = false
  }
}

const runTaskTest = async () => {
  loadingStates.tasks = true
  const startTime = Date.now()

  try {
    const result = await apiStore.executeTask(
      'Take a screenshot for testing',
      'https://httpbin.org'
    )
    const duration = Date.now() - startTime

    addTestResult(
      'tasks',
      result.success,
      result.success ? 'Task submitted successfully' : 'Task submission failed',
      duration,
      result.success ? null : 'Task execution failed',
      result.success ? `Task ID: ${result.data.task_id}` : null
    )
  } catch (error) {
    const duration = Date.now() - startTime
    addTestResult('tasks', false, 'Error', duration, error.message)
  } finally {
    loadingStates.tasks = false
  }
}

const runPrescriptsTest = async () => {
  loadingStates.prescripts = true
  const startTime = Date.now()

  try {
    const result = await apiStore.getPrescripts()
    const duration = Date.now() - startTime

    addTestResult(
      'prescripts',
      result.success,
      result.success ? 'Pre-scripts retrieved' : 'Failed to get pre-scripts',
      duration,
      result.success ? null : 'Pre-scripts endpoint failed'
    )
  } catch (error) {
    const duration = Date.now() - startTime
    addTestResult('prescripts', false, 'Error', duration, error.message)
  } finally {
    loadingStates.prescripts = false
  }
}

const runErrorTests = async () => {
  loadingStates.errors = true
  const startTime = Date.now()

  try {
    const config = useRuntimeConfig()

    // Test 404
    try {
      await $fetch(`${config.public.apiBase}/non-existent-endpoint`)
      addTestResult('errors', false, 'Should have returned 404', Date.now() - startTime, '404 test failed')
    } catch (error) {
      const success = error.status === 404
      addTestResult(
        'errors',
        success,
        success ? 'Error handling working' : 'Unexpected error response',
        Date.now() - startTime,
        success ? null : `Expected 404, got ${error.status}`
      )
    }
  } finally {
    loadingStates.errors = false
  }
}

const runAllTests = async () => {
  runningAllTests.value = true

  await runHealthTest()
  await runAuthTests()
  await runMetricsTest()
  await runTaskTest()
  await runPrescriptsTest()
  await runErrorTests()

  runningAllTests.value = false

  toast.add({
    title: 'All tests completed',
    description: `${passedTests.value}/${totalTests.value} tests passed`,
    color: passedTests.value === totalTests.value ? 'green' : 'yellow'
  })
}

const clearResults = () => {
  testResults.value = []
  toast.add({
    title: 'Results cleared',
    color: 'blue'
  })
}
</script>
