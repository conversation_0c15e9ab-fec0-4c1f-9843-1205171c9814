<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span class="text-white font-bold text-sm">RAG</span>
              </div>
              <h1 class="text-2xl font-bold text-gray-900">Story Assistant</h1>
            </div>
            <div class="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
              <span>•</span>
              <span>Mejora automática de user stories</span>
              <span>•</span>
              <span>Generación de test cases</span>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- Project Selector -->
            <div class="relative" v-if="ragStore.hasProjects">
              <select 
                v-model="selectedProjectId"
                @change="handleProjectChange"
                class="block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Seleccionar proyecto</option>
                <option 
                  v-for="project in ragStore.projects" 
                  :key="project.id" 
                  :value="project.id"
                >
                  {{ project.name }}
                </option>
              </select>
            </div>
            
            <!-- New Project Button -->
            <button
              @click="showCreateProject = true"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon class="w-4 h-4 mr-2" />
              Nuevo Proyecto
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- No Project Selected -->
      <div v-if="!ragStore.hasCurrentProject" class="text-center py-12">
        <div class="w-24 h-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FolderIcon class="w-12 h-12 text-gray-400" />
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Selecciona o crea un proyecto</h3>
        <p class="text-gray-500 mb-6">Los proyectos mantienen su propio knowledge base para contexto específico</p>
        <button
          @click="showCreateProject = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon class="w-4 h-4 mr-2" />
          Crear primer proyecto
        </button>
      </div>

      <!-- Project Dashboard -->
      <div v-else class="space-y-8">
        <!-- Project Info -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">{{ ragStore.currentProject?.name }}</h2>
              <p class="text-gray-500 mt-1">{{ ragStore.currentProject?.description || 'Sin descripción' }}</p>
            </div>
            <div class="flex items-center space-x-4">
              <div class="text-right">
                <div class="text-sm text-gray-500">Knowledge Base</div>
                <div class="text-lg font-semibold text-gray-900">
                  {{ ragStore.knowledgeStats?.totalDocuments || 0 }} documentos
                </div>
              </div>
              <button
                @click="showAddKnowledge = true"
                class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <DocumentPlusIcon class="w-4 h-4 mr-2" />
                Añadir conocimiento
              </button>
            </div>
          </div>
        </div>

        <!-- Story Input -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">User Story</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Escribe tu user story
              </label>
              <textarea
                v-model="ragStore.currentStory"
                placeholder="Ej: Como usuario, quiero poder buscar productos para encontrar lo que necesito"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <div class="flex items-center space-x-4">
              <button
                @click="improveStory"
                :disabled="!ragStore.canImproveStory || ragStore.loading.improveStory"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <template v-if="ragStore.loading.improveStory">
                  <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Mejorando...
                </template>
                <template v-else>
                  <SparklesIcon class="w-4 h-4 mr-2" />
                  Mejorar Story
                </template>
              </button>
              
              <button
                @click="generateTests"
                :disabled="!ragStore.canGenerateTests || ragStore.loading.generateTests"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <template v-if="ragStore.loading.generateTests">
                  <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generando...
                </template>
                <template v-else>
                  <BeakerIcon class="w-4 h-4 mr-2" />
                  Generar Tests
                </template>
              </button>
            </div>
          </div>
        </div>

        <!-- Knowledge Base -->
        <RAGKnowledgeBase 
          v-if="ragStore.knowledgeStats"
          :knowledgeStats="ragStore.knowledgeStats"
          @documentDeleted="handleDocumentDeleted"
        />

        <!-- Results -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Improved Story -->
          <RAGImprovedStory 
            v-if="ragStore.improvedStory"
            :story="ragStore.improvedStory"
          />
          
          <!-- Test Cases -->
          <RAGTestCases 
            v-if="ragStore.currentTestCases.length > 0"
            :testCases="ragStore.currentTestCases"
            :edgeCases="ragStore.currentEdgeCases"
          />
        </div>

        <!-- History -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Story History -->
          <RAGStoryHistory 
            v-if="ragStore.storyHistory.length > 0"
            :history="ragStore.storyHistory"
          />
          
          <!-- Test History -->
          <RAGTestHistory 
            v-if="ragStore.testHistory.length > 0"
            :history="ragStore.testHistory"
          />
        </div>
      </div>
    </main>

    <!-- Modals -->
    <RAGCreateProjectModal 
      v-if="showCreateProject"
      @close="showCreateProject = false"
      @created="handleProjectCreated"
    />
    
    <RAGAddKnowledgeModal 
      v-if="showAddKnowledge"
      @close="showAddKnowledge = false"
      @added="handleKnowledgeAdded"
    />

    <!-- Error Toast -->
    <div 
      v-if="errorMessage"
      class="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-sm"
      role="alert"
    >
      <div class="flex">
        <div class="py-1">
          <ExclamationTriangleIcon class="w-5 h-5 text-red-500 mr-3" />
        </div>
        <div>
          <p class="font-bold">Error</p>
          <p class="text-sm">{{ errorMessage }}</p>
        </div>
        <button 
          @click="clearError"
          class="ml-4 text-red-500 hover:text-red-700"
        >
          <XMarkIcon class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRAGStore } from '~/stores/rag'
import { 
  PlusIcon, 
  FolderIcon, 
  DocumentPlusIcon, 
  SparklesIcon, 
  BeakerIcon,
  ExclamationTriangleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

// Store
const ragStore = useRAGStore()

// Reactive state
const selectedProjectId = ref('')
const showCreateProject = ref(false)
const showAddKnowledge = ref(false)

// Computed
const errorMessage = computed(() => {
  return ragStore.errors.projects || 
         ragStore.errors.stories || 
         ragStore.errors.tests || 
         ragStore.errors.knowledge
})

// Methods
const handleProjectChange = () => {
  if (selectedProjectId.value) {
    const project = ragStore.projectById(selectedProjectId.value)
    if (project) {
      ragStore.setCurrentProject(project)
    }
  }
}

const handleProjectCreated = (projectId: string) => {
  showCreateProject.value = false
  selectedProjectId.value = projectId
  const project = ragStore.projectById(projectId)
  if (project) {
    ragStore.setCurrentProject(project)
  }
}

const handleKnowledgeAdded = () => {
  showAddKnowledge.value = false
}

const handleDocumentDeleted = async (documentId: string) => {
  try {
    // Por ahora solo refrescamos las estadísticas
    // En el futuro se podría implementar un endpoint DELETE específico para documentos
    await ragStore.fetchKnowledgeStats()
  } catch (error) {
    console.error('Error refreshing knowledge stats:', error)
  }
}

const improveStory = async () => {
  try {
    await ragStore.improveStory(ragStore.currentStory)
  } catch (error) {
    console.error('Error improving story:', error)
  }
}

const generateTests = async () => {
  try {
    await ragStore.generateTests(ragStore.currentStory)
  } catch (error) {
    console.error('Error generating tests:', error)
  }
}

const clearError = () => {
  ragStore.clearErrors()
}

// Lifecycle
onMounted(async () => {
  await ragStore.fetchProjects()
  
  // Auto-seleccionar primer proyecto si existe
  if (ragStore.hasProjects && !ragStore.hasCurrentProject) {
    const firstProject = ragStore.projects[0]
    selectedProjectId.value = firstProject.id
    ragStore.setCurrentProject(firstProject)
  }
})

// Watch para actualizar selectedProjectId cuando cambie currentProject
watch(() => ragStore.currentProject, (newProject) => {
  if (newProject && selectedProjectId.value !== newProject.id) {
    selectedProjectId.value = newProject.id
  }
})

// Meta
definePageMeta({
  layout: 'default'
})

// Head
useHead({
  title: 'RAG Story Assistant - AERY',
  meta: [
    { name: 'description', content: 'Mejora automática de user stories y generación de test cases usando IA' }
  ]
})
</script>