<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Pre-scripts</h1>
        <p class="mt-1 text-sm text-gray-500">
          Create and manage reusable automation scripts
        </p>
      </div>
      
      <UButton 
        @click="showCreateModal = true"
        icon="i-heroicons-plus"
        color="primary"
      >
        New Pre-script
      </UButton>
    </div>

    <!-- Pre-scripts List -->
    <div v-if="prescripts.length === 0 && !apiStore.loading.prescripts" class="text-center py-12">
      <Icon name="i-heroicons-document-text" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No pre-scripts yet</h3>
      <p class="text-gray-500 mb-4">Create your first reusable automation script</p>
      <UButton 
        @click="showCreateModal = true"
        icon="i-heroicons-plus"
      >
        Create Pre-script
      </UButton>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <UCard 
        v-for="prescript in prescripts" 
        :key="prescript.id"
        class="hover:shadow-md transition-shadow"
      >
        <template #header>
          <div class="flex items-start justify-between">
            <div>
              <h3 class="font-medium text-gray-900">{{ prescript.name }}</h3>
              <UBadge 
                :color="getCategoryColor(prescript.category)" 
                variant="subtle" 
                size="xs"
              >
                {{ prescript.category }}
              </UBadge>
            </div>
            
            <UDropdown :items="getPrescriptActions(prescript)">
              <UButton variant="ghost" size="xs" icon="i-heroicons-ellipsis-vertical" />
            </UDropdown>
          </div>
        </template>
        
        <div class="space-y-3">
          <p class="text-sm text-gray-600">{{ prescript.description }}</p>
          
          <div class="space-y-2">
            <p class="text-xs font-medium text-gray-700">Actions ({{ prescript.actions?.length || 0 }}):</p>
            <div class="space-y-1">
              <div 
                v-for="(action, index) in (prescript.actions || []).slice(0, 3)" 
                :key="index"
                class="text-xs bg-gray-50 rounded px-2 py-1"
              >
                <span class="font-medium">{{ action.type }}:</span>
                <span class="text-gray-600">{{ getActionDescription(action) }}</span>
              </div>
              <div v-if="(prescript.actions?.length || 0) > 3" class="text-xs text-gray-500">
                +{{ (prescript.actions?.length || 0) - 3 }} more actions
              </div>
            </div>
          </div>
          
          <div v-if="prescript.variables?.length" class="space-y-2">
            <p class="text-xs font-medium text-gray-700">Variables ({{ prescript.variables.length }}):</p>
            <div class="flex flex-wrap gap-1">
              <UBadge 
                v-for="variable in prescript.variables.slice(0, 3)" 
                :key="variable.name"
                variant="outline" 
                size="xs"
              >
                {{ variable.name }}
              </UBadge>
              <UBadge 
                v-if="prescript.variables.length > 3" 
                variant="outline" 
                size="xs"
              >
                +{{ prescript.variables.length - 3 }}
              </UBadge>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="flex items-center justify-between">
            <span class="text-xs text-gray-500">
              {{ formatDate(prescript.created_at) }}
            </span>
            <UButton 
              size="xs" 
              variant="outline"
              @click="usePreScript(prescript)"
            >
              Use Script
            </UButton>
          </div>
        </template>
      </UCard>
    </div>

    <!-- Loading State -->
    <div v-if="apiStore.loading.prescripts" class="text-center py-12">
      <div class="spinner mx-auto mb-4"></div>
      <p class="text-gray-500">Loading pre-scripts...</p>
    </div>

    <!-- Create/Edit Modal -->
    <UModal v-model="showCreateModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">
            {{ editingPreScript ? 'Edit Pre-script' : 'Create New Pre-script' }}
          </h3>
        </template>
        
        <UForm :schema="prescriptSchema" :state="prescriptForm" @submit="savePrescript" class="space-y-4">
          <UFormGroup label="Name" name="name">
            <UInput 
              v-model="prescriptForm.name" 
              placeholder="Login Script"
            />
          </UFormGroup>
          
          <UFormGroup label="Description" name="description">
            <UTextarea 
              v-model="prescriptForm.description" 
              placeholder="Automated login for testing"
              :rows="3"
            />
          </UFormGroup>
          
          <UFormGroup label="Category" name="category">
            <USelect 
              v-model="prescriptForm.category" 
              :options="categoryOptions"
              placeholder="Select category"
            />
          </UFormGroup>
          
          <!-- Actions Section -->
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <label class="text-sm font-medium text-gray-700">Actions</label>
              <UButton 
                size="xs" 
                variant="outline"
                @click="addAction"
                icon="i-heroicons-plus"
              >
                Add Action
              </UButton>
            </div>
            
            <div v-if="prescriptForm.actions.length === 0" class="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
              <p class="text-sm text-gray-500">No actions added yet</p>
            </div>
            
            <div v-else class="space-y-2">
              <div 
                v-for="(action, index) in prescriptForm.actions" 
                :key="index"
                class="border rounded-lg p-3 bg-gray-50"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1 space-y-2">
                    <USelect 
                      v-model="action.type" 
                      :options="actionTypeOptions"
                      placeholder="Action type"
                      size="sm"
                    />
                    
                    <UInput 
                      v-if="action.type === 'navigate'"
                      v-model="action.url" 
                      placeholder="https://example.com"
                      size="sm"
                    />
                    
                    <UInput 
                      v-if="['fill', 'click'].includes(action.type)"
                      v-model="action.selector" 
                      placeholder="CSS selector"
                      size="sm"
                    />
                    
                    <UInput 
                      v-if="action.type === 'fill'"
                      v-model="action.value" 
                      placeholder="Value or {{variable}}"
                      size="sm"
                    />
                  </div>
                  
                  <UButton 
                    variant="ghost" 
                    size="xs" 
                    icon="i-heroicons-trash"
                    @click="removeAction(index)"
                    color="red"
                  />
                </div>
              </div>
            </div>
          </div>
          
          <!-- Variables Section -->
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <label class="text-sm font-medium text-gray-700">Variables</label>
              <UButton 
                size="xs" 
                variant="outline"
                @click="addVariable"
                icon="i-heroicons-plus"
              >
                Add Variable
              </UButton>
            </div>
            
            <div v-if="prescriptForm.variables.length === 0" class="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
              <p class="text-sm text-gray-500">No variables defined</p>
            </div>
            
            <div v-else class="space-y-2">
              <div 
                v-for="(variable, index) in prescriptForm.variables" 
                :key="index"
                class="border rounded-lg p-3 bg-gray-50"
              >
                <div class="flex items-center space-x-2">
                  <UInput 
                    v-model="variable.name" 
                    placeholder="Variable name"
                    size="sm"
                    class="flex-1"
                  />
                  
                  <USelect 
                    v-model="variable.type" 
                    :options="variableTypeOptions"
                    size="sm"
                    class="w-24"
                  />
                  
                  <UToggle v-model="variable.required" size="sm" />
                  
                  <UButton 
                    variant="ghost" 
                    size="xs" 
                    icon="i-heroicons-trash"
                    @click="removeVariable(index)"
                    color="red"
                  />
                </div>
              </div>
            </div>
          </div>
        </UForm>
        
        <template #footer>
          <div class="flex items-center justify-end space-x-3">
            <UButton 
              variant="ghost" 
              @click="closeModal"
            >
              Cancel
            </UButton>
            <UButton 
              @click="savePrescript"
              :loading="apiStore.loading.prescripts"
              :disabled="!isFormValid"
            >
              {{ editingPreScript ? 'Update' : 'Create' }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup>
import { z } from 'zod'

definePageMeta({
  middleware: 'auth'
})

const apiStore = useApiStore()
const toast = useToast()

// Reactive data
const prescripts = ref([])
const showCreateModal = ref(false)
const editingPreScript = ref(null)

// Form schema
const prescriptSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  category: z.string().min(1, 'Category is required'),
  actions: z.array(z.object({
    type: z.string(),
    url: z.string().optional(),
    selector: z.string().optional(),
    value: z.string().optional()
  })).min(1, 'At least one action is required'),
  variables: z.array(z.object({
    name: z.string(),
    type: z.string(),
    required: z.boolean()
  })).optional()
})

// Form state
const prescriptForm = reactive({
  name: '',
  description: '',
  category: 'automation',
  actions: [],
  variables: []
})

// Options
const categoryOptions = [
  { label: 'Authentication', value: 'authentication' },
  { label: 'Automation', value: 'automation' },
  { label: 'Testing', value: 'testing' },
  { label: 'Data Collection', value: 'data_collection' },
  { label: 'Navigation', value: 'navigation' }
]

const actionTypeOptions = [
  { label: 'Navigate', value: 'navigate' },
  { label: 'Fill Input', value: 'fill' },
  { label: 'Click Element', value: 'click' },
  { label: 'Wait', value: 'wait' },
  { label: 'Screenshot', value: 'screenshot' }
]

const variableTypeOptions = [
  { label: 'String', value: 'string' },
  { label: 'Number', value: 'number' },
  { label: 'Boolean', value: 'boolean' }
]

// Computed
const isFormValid = computed(() => {
  return prescriptForm.name &&
         prescriptForm.description &&
         prescriptForm.category &&
         prescriptForm.actions.length > 0
})

// Load prescripts on mount
onMounted(async () => {
  await loadPrescripts()
})

const loadPrescripts = async () => {
  const result = await apiStore.getPrescripts()
  if (result.success) {
    prescripts.value = result.data.prescripts || []
  } else {
    toast.add({
      title: 'Failed to load pre-scripts',
      description: result.error?.data?.message || 'Unknown error',
      color: 'red'
    })
  }
}

const savePrescript = async () => {
  try {
    const result = await apiStore.createPrescript(prescriptForm)

    if (result.success) {
      toast.add({
        title: editingPreScript.value ? 'Pre-script updated' : 'Pre-script created',
        color: 'green'
      })

      closeModal()
      await loadPrescripts()
    } else {
      toast.add({
        title: 'Failed to save pre-script',
        description: result.error?.data?.message || 'Unknown error',
        color: 'red'
      })
    }
  } catch (error) {
    toast.add({
      title: 'Error saving pre-script',
      description: error.message,
      color: 'red'
    })
  }
}

const addAction = () => {
  prescriptForm.actions.push({
    type: 'navigate',
    url: '',
    selector: '',
    value: ''
  })
}

const removeAction = (index) => {
  prescriptForm.actions.splice(index, 1)
}

const addVariable = () => {
  prescriptForm.variables.push({
    name: '',
    type: 'string',
    required: true
  })
}

const removeVariable = (index) => {
  prescriptForm.variables.splice(index, 1)
}

const closeModal = () => {
  showCreateModal.value = false
  editingPreScript.value = null

  // Reset form
  Object.assign(prescriptForm, {
    name: '',
    description: '',
    category: 'automation',
    actions: [],
    variables: []
  })
}

const editPreScript = (prescript) => {
  editingPreScript.value = prescript
  Object.assign(prescriptForm, {
    name: prescript.name,
    description: prescript.description,
    category: prescript.category,
    actions: [...(prescript.actions || [])],
    variables: [...(prescript.variables || [])]
  })
  showCreateModal.value = true
}

const deletePreScript = async (prescript) => {
  // In a real app, this would call the delete API
  const index = prescripts.value.findIndex(p => p.id === prescript.id)
  if (index >= 0) {
    prescripts.value.splice(index, 1)
    toast.add({
      title: 'Pre-script deleted',
      color: 'green'
    })
  }
}

const usePreScript = (prescript) => {
  // Navigate to tasks page with pre-script data
  navigateTo({
    path: '/tasks',
    query: {
      prescript: prescript.id,
      instruction: `Execute pre-script: ${prescript.name}`
    }
  })
}

const getPrescriptActions = (prescript) => {
  return [
    [{
      label: 'Edit',
      icon: 'i-heroicons-pencil',
      click: () => editPreScript(prescript)
    }],
    [{
      label: 'Delete',
      icon: 'i-heroicons-trash',
      click: () => deletePreScript(prescript)
    }]
  ]
}

const getCategoryColor = (category) => {
  const colors = {
    authentication: 'blue',
    automation: 'green',
    testing: 'yellow',
    data_collection: 'purple',
    navigation: 'gray'
  }
  return colors[category] || 'gray'
}

const getActionDescription = (action) => {
  switch (action.type) {
    case 'navigate':
      return action.url || 'URL'
    case 'fill':
      return action.selector || 'selector'
    case 'click':
      return action.selector || 'selector'
    case 'wait':
      return 'pause execution'
    case 'screenshot':
      return 'capture page'
    default:
      return action.type
  }
}

const formatDate = (dateString) => {
  if (!dateString) return 'Unknown'
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  }).format(new Date(dateString))
}
</script>
