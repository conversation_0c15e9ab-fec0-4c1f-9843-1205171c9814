<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
      <p class="mt-1 text-sm text-gray-500">
        Monitor and test your AERY Browser Automation API
      </p>
    </div>

    <!-- Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- API Health -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <Icon name="i-heroicons-heart" class="w-5 h-5 text-green-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">API Status</p>
            <div class="flex items-center space-x-2">
              <UBadge 
                :color="apiStore.healthStatusColor" 
                variant="subtle"
              >
                {{ apiStore.healthStatus }}
              </UBadge>
              <UButton 
                variant="ghost" 
                size="xs" 
                icon="i-heroicons-arrow-path"
                :loading="apiStore.loading.health"
                @click="refreshHealth"
              />
            </div>
          </div>
        </div>
      </UCard>

      <!-- User Info -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Icon name="i-heroicons-user" class="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Current User</p>
            <p class="text-sm text-gray-900">{{ authStore.userEmail }}</p>
            <UBadge size="xs" variant="subtle">{{ authStore.userPlan }}</UBadge>
          </div>
        </div>
      </UCard>

      <!-- Tasks -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <Icon name="i-heroicons-cog-6-tooth" class="w-5 h-5 text-purple-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Tasks Executed</p>
            <p class="text-lg font-semibold text-gray-900">{{ tasksExecuted }}</p>
          </div>
        </div>
      </UCard>

      <!-- Metrics -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Icon name="i-heroicons-chart-bar" class="w-5 h-5 text-yellow-600" />
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">System Metrics</p>
            <UButton 
              variant="ghost" 
              size="xs" 
              :loading="apiStore.loading.metrics"
              @click="refreshMetrics"
            >
              {{ apiStore.metrics ? 'Refresh' : 'Load' }}
            </UButton>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Quick Test -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">Quick API Test</h3>
        </template>
        
        <div class="space-y-4">
          <p class="text-sm text-gray-600">
            Run a quick test to verify all API endpoints are working correctly.
          </p>
          
          <div class="flex space-x-3">
            <UButton 
              @click="runQuickTest"
              :loading="quickTestLoading"
              icon="i-heroicons-play"
            >
              Run Quick Test
            </UButton>
            
            <UButton 
              variant="outline" 
              to="/tests"
              icon="i-heroicons-beaker"
            >
              Full Test Suite
            </UButton>
          </div>
          
          <!-- Test Results -->
          <div v-if="quickTestResults.length > 0" class="mt-4 space-y-2">
            <h4 class="text-sm font-medium text-gray-900">Test Results:</h4>
            <div class="space-y-1">
              <div 
                v-for="result in quickTestResults" 
                :key="result.test"
                class="flex items-center justify-between text-sm"
              >
                <span>{{ result.test }}</span>
                <UBadge 
                  :color="result.success ? 'green' : 'red'" 
                  variant="subtle"
                >
                  {{ result.success ? 'PASS' : 'FAIL' }}
                </UBadge>
              </div>
            </div>
          </div>
        </div>
      </UCard>

      <!-- Quick Task Execution -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">Quick Task Execution</h3>
        </template>
        
        <div class="space-y-4">
          <p class="text-sm text-gray-600">
            Test the task execution functionality with natural language instructions.
          </p>

          <UForm :state="quickTaskState" @submit="executeQuickTask" class="space-y-4">
            <UFormGroup label="Instruction" help="Describe what you want the browser to do">
              <UTextarea
                v-model="quickTaskState.instruction"
                placeholder="Take a screenshot of this webpage"
                rows="2"
              />
            </UFormGroup>

            <UFormGroup label="URL" help="The website to automate">
              <UInput
                v-model="quickTaskState.url"
                placeholder="https://example.com"
                icon="i-heroicons-globe-alt"
              />
            </UFormGroup>

            <div class="flex gap-2">
              <UButton
                type="submit"
                :loading="apiStore.loading.tasks"
                icon="i-heroicons-play"
                size="sm"
              >
                Execute Task
              </UButton>

              <UButton
                variant="outline"
                icon="i-heroicons-arrow-path"
                size="sm"
                @click="resetQuickTask"
              >
                Reset
              </UButton>
            </div>
          </UForm>
          
          <!-- Task Result -->
          <div v-if="lastTaskResult" class="mt-6">
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h4 class="text-sm font-medium text-gray-900">Last Task Result</h4>
                  <UBadge
                    :color="lastTaskResult.status === 'pending' ? 'yellow' : 'blue'"
                    variant="subtle"
                  >
                    {{ lastTaskResult.status }}
                  </UBadge>
                </div>
              </template>

              <div class="space-y-3">
                <div class="grid grid-cols-1 gap-3 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-600">Task ID:</span>
                    <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ lastTaskResult.taskId }}</code>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Queue Position:</span>
                    <span class="font-medium">{{ lastTaskResult.queuePosition || 'N/A' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600">Estimated Wait:</span>
                    <span class="font-medium">{{ lastTaskResult.estimatedWaitTime || 'N/A' }}</span>
                  </div>
                </div>

                <div class="pt-3 border-t">
                  <UButton
                    variant="outline"
                    size="xs"
                    icon="i-heroicons-arrow-path"
                    @click="checkTaskStatus"
                    :loading="apiStore.loading.tasks"
                  >
                    Refresh Status
                  </UButton>
                </div>
              </div>
            </UCard>
          </div>
        </div>
      </UCard>
    </div>

    <!-- System Metrics -->
    <UCard v-if="apiStore.metrics">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">System Metrics</h3>
          <UButton 
            variant="ghost" 
            size="xs" 
            icon="i-heroicons-arrow-path"
            :loading="apiStore.loading.metrics"
            @click="refreshMetrics"
          >
            Refresh
          </UButton>
        </div>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div v-for="(value, key) in apiStore.metrics" :key="key" class="text-center">
          <p class="text-2xl font-semibold text-gray-900">{{ value }}</p>
          <p class="text-sm text-gray-500 capitalize">{{ key.replace('_', ' ') }}</p>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup>
definePageMeta({
  middleware: 'auth'
})

const authStore = useAuthStore()
const apiStore = useApiStore()
const toast = useToast()

// Reactive data
const quickTestLoading = ref(false)
const quickTestResults = ref([])
const tasksExecuted = ref(0)
const lastTaskResult = ref(null)

const quickTaskState = reactive({
  instruction: 'Take a screenshot of this webpage',
  url: 'https://httpbin.org'
})

// Initialize data on mount
onMounted(async () => {
  await refreshHealth()
  await refreshMetrics()
})

const refreshHealth = async () => {
  await apiStore.checkHealth()
}

const refreshMetrics = async () => {
  await apiStore.getMetrics()
}

const runQuickTest = async () => {
  quickTestLoading.value = true
  quickTestResults.value = []
  
  const tests = [
    { name: 'Health Check', test: 'health' },
    { name: 'Metrics', test: 'metrics' },
    { name: 'Prescripts', test: 'prescripts' }
  ]
  
  for (const testItem of tests) {
    try {
      let result
      switch (testItem.test) {
        case 'health':
          result = await apiStore.checkHealth()
          break
        case 'metrics':
          result = await apiStore.getMetrics()
          break
        case 'prescripts':
          result = await apiStore.getPrescripts()
          break
      }
      
      quickTestResults.value.push({
        test: testItem.name,
        success: result.success
      })
    } catch (error) {
      quickTestResults.value.push({
        test: testItem.name,
        success: false
      })
    }
  }
  
  quickTestLoading.value = false
  
  const passedTests = quickTestResults.value.filter(r => r.success).length
  const totalTests = quickTestResults.value.length
  
  toast.add({
    title: 'Quick test completed',
    description: `${passedTests}/${totalTests} tests passed`,
    color: passedTests === totalTests ? 'green' : 'yellow'
  })
}

const executeQuickTask = async (data) => {
  const result = await apiStore.executeTask(
    data?.instruction || quickTaskState.instruction,
    data?.url || quickTaskState.url
  )
  
  if (result.success) {
    lastTaskResult.value = result.data.data
    tasksExecuted.value++
    toast.add({
      title: 'Task submitted successfully',
      description: `Task ID: ${result.data.data.taskId}`,
      color: 'green'
    })
  } else {
    toast.add({
      title: 'Task execution failed',
      description: result.error?.data?.message || 'Unknown error',
      color: 'red'
    })
  }
}

const resetQuickTask = () => {
  quickTaskState.instruction = 'Take a screenshot of this webpage'
  quickTaskState.url = 'https://httpbin.org'
  lastTaskResult.value = null
}

const checkTaskStatus = async () => {
  if (!lastTaskResult.value?.taskId) return

  try {
    // Aquí podrías implementar una llamada al API para verificar el estado
    // Por ahora solo mostramos un mensaje
    toast.add({
      title: 'Status Check',
      description: 'Task status checking is not implemented yet',
      color: 'blue'
    })
  } catch (error) {
    toast.add({
      title: 'Error',
      description: 'Failed to check task status',
      color: 'red'
    })
  }
}
</script>
