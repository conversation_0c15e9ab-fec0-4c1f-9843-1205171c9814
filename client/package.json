{"name": "aery-admin-dashboard", "version": "1.0.0", "description": "AERY Admin Dashboard - Test and manage browser automation API", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxt/ui": "^2.11.1", "nuxt": "^3.8.0"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@nuxtjs/tailwindcss": "^6.8.4", "@pinia/nuxt": "^0.5.1", "@vueuse/core": "^10.5.0", "@vueuse/nuxt": "^10.5.0", "pdfjs-dist": "^5.4.54", "pinia": "^2.1.7", "vue-toastification": "^2.0.0-rc.5"}}