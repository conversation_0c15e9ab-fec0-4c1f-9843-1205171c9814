# AERY Admin Dashboard

A modern admin dashboard built with Nuxt 3 for testing and managing the AERY Browser Automation API.

## Features

- 🔐 **Authentication** - User registration and login
- 🏥 **Health Monitoring** - Real-time API health checks
- 🧪 **API Testing** - Comprehensive test suite for all endpoints
- 🤖 **Task Execution** - Natural language browser automation
- 📝 **Pre-scripts** - Create and manage reusable automation scripts
- 📊 **Metrics** - System performance monitoring
- 🎨 **Modern UI** - Built with Nuxt UI and Tailwind CSS

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm
- AERY API server running on `http://localhost:8000`

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The dashboard will be available at `http://localhost:3000`

### Configuration

Set the API base URL in your environment:

```bash
# .env
NUXT_PUBLIC_API_BASE=http://localhost:8000
```

## Usage

### 1. Authentication
- Register a new admin account or login with existing credentials
- The dashboard automatically manages JWT tokens

### 2. Dashboard Overview
- View API health status
- Quick task execution
- System metrics overview

### 3. API Testing
- Run individual endpoint tests
- Execute full test suite
- View detailed test results

### 4. Task Execution
- Send natural language instructions
- Configure browser options
- View task history and status

### 5. Pre-scripts Management
- Create reusable automation scripts
- Define variables and actions
- Organize by categories

## API Integration

The dashboard integrates with these AERY API endpoints:

- `GET /health` - Health check
- `POST /auth/register` - User registration  
- `POST /auth/login` - User authentication
- `GET /metrics` - System metrics
- `POST /execute` - Task execution
- `GET /prescripts` - List pre-scripts
- `POST /prescripts` - Create pre-script

## Development

### Project Structure

```
client/
├── assets/          # CSS and static assets
├── components/      # Vue components
├── composables/     # Composable functions
├── layouts/         # Layout components
├── middleware/      # Route middleware
├── pages/           # Page components
├── plugins/         # Nuxt plugins
├── stores/          # Pinia stores
└── nuxt.config.ts   # Nuxt configuration
```

### Key Technologies

- **Nuxt 3** - Vue.js framework
- **Nuxt UI** - Component library
- **Pinia** - State management
- **Tailwind CSS** - Styling
- **TypeScript** - Type safety

## Testing Examples

### Natural Language Instructions

```javascript
// Basic actions
"Take a screenshot of the homepage"
"Navigate to the contact page and take a screenshot"
"Scroll down to the footer and capture the page"

// Interactive actions  
"Click on the login button"
"Fill out the contact form with test data"
"Search for 'automation' and take a screenshot"
```

### Pre-script Example

```json
{
  "name": "Login Script",
  "description": "Automated login for testing",
  "category": "authentication",
  "actions": [
    {
      "type": "navigate",
      "url": "https://example.com/login"
    },
    {
      "type": "fill",
      "selector": "input[name='email']",
      "value": "{{email}}"
    },
    {
      "type": "fill", 
      "selector": "input[name='password']",
      "value": "{{password}}"
    },
    {
      "type": "click",
      "selector": "button[type='submit']"
    }
  ],
  "variables": [
    { "name": "email", "type": "string", "required": true },
    { "name": "password", "type": "string", "required": true }
  ]
}
```

## Deployment

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

### Environment Variables

```bash
NUXT_PUBLIC_API_BASE=https://your-api-domain.com
NUXT_PUBLIC_APP_NAME="AERY Dashboard"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of the AERY Browser Automation platform.
