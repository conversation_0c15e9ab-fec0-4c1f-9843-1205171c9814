# AERY API Documentation

Documentación completa de la API de automatización de navegador con IA de AERY.

## Tabla de Contenidos

- [Introducción](#introducción)
- [Autenticación](#autenticación)
- [Endpoints](#endpoints)
- [Modelos de Datos](#modelos-de-datos)
- [Códigos de Error](#códigos-de-error)
- [Ejemplos](#ejemplos)
- [SDKs](#sdks)

## Introducción

AERY es una API RESTful que permite automatizar navegadores web usando instrucciones en lenguaje natural. La API utiliza inteligencia artificial para interpretar las instrucciones y ejecutar las acciones correspondientes en el navegador.

### URL Base

```
http://localhost:8000/api/v1
```

### Formato de Respuesta

Todas las respuestas están en formato JSON y siguen esta estructura:

```json
{
  "success": boolean,
  "data": object | null,
  "error": {
    "code": string,
    "message": string
  } | null,
  "timestamp": string,
  "request_id": string
}
```

## Autenticación

AERY utiliza API Keys para autenticación. Debes incluir tu API key en el header `Authorization`:

```http
Authorization: Bearer YOUR_API_KEY
```

### Obtener una API Key

1. Regístrate en el sistema
2. Accede al dashboard
3. Genera una nueva API key
4. Guarda la key de forma segura

### Límites de Tasa

Los límites de tasa dependen de tu plan:

- **Free**: 60 requests/minuto, 1,000 requests/hora
- **Basic**: 120 requests/minuto, 5,000 requests/hora
- **Pro**: 300 requests/minuto, 20,000 requests/hora
- **Enterprise**: Sin límites

## Endpoints

### POST /execute

Ejecuta una instrucción de automatización en el navegador.

#### Request

```http
POST /api/v1/execute
Content-Type: application/json
Authorization: Bearer YOUR_API_KEY

{
  "instruction": "Navega a google.com y busca 'inteligencia artificial'",
  "options": {
    "timeout": 30000,
    "screenshot": true,
    "wait_for_completion": true,
    "viewport": {
      "width": 1920,
      "height": 1080
    }
  }
}
```

#### Response

```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "processing",
    "estimated_completion": "2024-01-15T10:30:00Z"
  },
  "error": null,
  "timestamp": "2024-01-15T10:25:00Z",
  "request_id": "req_123456789"
}
```

#### Parámetros

| Parámetro | Tipo | Requerido | Descripción |
|-----------|------|-----------|-------------|
| `instruction` | string | Sí | Instrucción en lenguaje natural |
| `options.timeout` | number | No | Timeout en milisegundos (default: 30000) |
| `options.screenshot` | boolean | No | Capturar screenshot (default: false) |
| `options.wait_for_completion` | boolean | No | Esperar a que termine (default: false) |
| `options.viewport.width` | number | No | Ancho del viewport (default: 1920) |
| `options.viewport.height` | number | No | Alto del viewport (default: 1080) |

### GET /task/{id}

Obtiene el estado y resultado de una tarea.

#### Request

```http
GET /api/v1/task/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer YOUR_API_KEY
```

#### Response

```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "instruction": "Navega a google.com y busca 'inteligencia artificial'",
    "created_at": "2024-01-15T10:25:00Z",
    "started_at": "2024-01-15T10:25:05Z",
    "completed_at": "2024-01-15T10:25:45Z",
    "execution_time_ms": 40000,
    "result": {
      "success": true,
      "actions_performed": [
        {
          "type": "navigate",
          "url": "https://google.com",
          "timestamp": "2024-01-15T10:25:10Z"
        },
        {
          "type": "type",
          "selector": "input[name='q']",
          "text": "inteligencia artificial",
          "timestamp": "2024-01-15T10:25:15Z"
        },
        {
          "type": "click",
          "selector": "input[type='submit']",
          "timestamp": "2024-01-15T10:25:20Z"
        }
      ],
      "final_url": "https://www.google.com/search?q=inteligencia+artificial",
      "screenshots": [
        {
          "name": "final_result.png",
          "url": "/api/v1/artifacts/screenshots/550e8400-e29b-41d4-a716-446655440000/final_result.png"
        }
      ],
      "extracted_data": {
        "search_results_count": "Aproximadamente 45,600,000 resultados",
        "first_result_title": "Inteligencia artificial - Wikipedia"
      }
    }
  },
  "error": null,
  "timestamp": "2024-01-15T10:26:00Z",
  "request_id": "req_123456790"
}
```

### GET /tasks

Obtiene la lista de tareas del usuario.

#### Request

```http
GET /api/v1/tasks?page=1&limit=10&status=completed
Authorization: Bearer YOUR_API_KEY
```

#### Response

```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "task_id": "550e8400-e29b-41d4-a716-446655440000",
        "instruction": "Navega a google.com y busca 'inteligencia artificial'",
        "status": "completed",
        "created_at": "2024-01-15T10:25:00Z",
        "execution_time_ms": 40000
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "pages": 1
    }
  },
  "error": null,
  "timestamp": "2024-01-15T10:26:00Z",
  "request_id": "req_123456791"
}
```

### DELETE /task/{id}

Cancela una tarea en ejecución.

#### Request

```http
DELETE /api/v1/task/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer YOUR_API_KEY
```

#### Response

```json
{
  "success": true,
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "cancelled"
  },
  "error": null,
  "timestamp": "2024-01-15T10:26:00Z",
  "request_id": "req_123456792"
}
```

### GET /health

Verifica el estado de la API.

#### Request

```http
GET /api/v1/health
```

#### Response

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 3600,
    "services": {
      "redis": "healthy",
      "postgres": "healthy",
      "workers": {
        "active": 3,
        "total": 3
      }
    }
  },
  "error": null,
  "timestamp": "2024-01-15T10:26:00Z",
  "request_id": "req_123456793"
}
```

### GET /metrics

Obtiene métricas del sistema (requiere permisos de admin).

#### Request

```http
GET /api/v1/metrics
Authorization: Bearer YOUR_ADMIN_API_KEY
```

#### Response

```json
{
  "success": true,
  "data": {
    "tasks": {
      "total": 1000,
      "completed": 950,
      "failed": 30,
      "pending": 20,
      "success_rate": 0.95
    },
    "performance": {
      "avg_execution_time_ms": 25000,
      "p95_execution_time_ms": 45000,
      "p99_execution_time_ms": 60000
    },
    "workers": {
      "active": 3,
      "busy": 1,
      "idle": 2
    }
  },
  "error": null,
  "timestamp": "2024-01-15T10:26:00Z",
  "request_id": "req_123456794"
}
```

## Modelos de Datos

### Task

```typescript
interface Task {
  task_id: string;
  user_id: string;
  instruction: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  execution_time_ms?: number;
  result?: TaskResult;
}
```

### TaskResult

```typescript
interface TaskResult {
  success: boolean;
  actions_performed: Action[];
  final_url?: string;
  screenshots: Screenshot[];
  extracted_data?: Record<string, any>;
  error_message?: string;
}
```

### Action

```typescript
interface Action {
  type: 'navigate' | 'click' | 'type' | 'wait' | 'scroll' | 'extract';
  selector?: string;
  url?: string;
  text?: string;
  timestamp: string;
  success: boolean;
}
```

### Screenshot

```typescript
interface Screenshot {
  name: string;
  url: string;
  timestamp: string;
}
```

## Códigos de Error

| Código | Descripción |
|--------|-------------|
| `INVALID_API_KEY` | API key inválida o expirada |
| `RATE_LIMIT_EXCEEDED` | Límite de tasa excedido |
| `INVALID_INSTRUCTION` | Instrucción inválida o vacía |
| `TASK_NOT_FOUND` | Tarea no encontrada |
| `TASK_TIMEOUT` | Tarea excedió el tiempo límite |
| `BROWSER_ERROR` | Error en el navegador |
| `WORKER_UNAVAILABLE` | No hay workers disponibles |
| `INTERNAL_ERROR` | Error interno del servidor |

## Ejemplos

### Ejemplo 1: Búsqueda Simple

```javascript
const response = await fetch('http://localhost:8000/api/v1/execute', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    instruction: 'Busca "OpenAI" en Google y toma una captura de pantalla',
    options: {
      screenshot: true,
      timeout: 30000
    }
  })
});

const result = await response.json();
console.log('Task ID:', result.data.task_id);
```

### Ejemplo 2: Formulario de Contacto

```javascript
const response = await fetch('http://localhost:8000/api/v1/execute', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    instruction: `
      Navega a https://example.com/contact
      Llena el formulario con:
      - Nombre: Juan Pérez
      - Email: <EMAIL>
      - Mensaje: Hola, me interesa su producto
      Envía el formulario
    `,
    options: {
      screenshot: true,
      wait_for_completion: true
    }
  })
});
```

### Ejemplo 3: Extracción de Datos

```javascript
const response = await fetch('http://localhost:8000/api/v1/execute', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    instruction: `
      Navega a https://news.ycombinator.com
      Extrae los títulos de los primeros 10 artículos
      y sus URLs
    `,
    options: {
      timeout: 45000
    }
  })
});
```

### Ejemplo 4: Monitoreo de Tarea

```javascript
async function waitForTask(taskId) {
  while (true) {
    const response = await fetch(`http://localhost:8000/api/v1/task/${taskId}`, {
      headers: {
        'Authorization': 'Bearer YOUR_API_KEY'
      }
    });
    
    const result = await response.json();
    const status = result.data.status;
    
    if (status === 'completed' || status === 'failed') {
      return result.data;
    }
    
    // Esperar 2 segundos antes de verificar nuevamente
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}
```

## SDKs

### JavaScript/TypeScript

```bash
npm install @aery/sdk
```

```javascript
import { AeryClient } from '@aery/sdk';

const client = new AeryClient({
  apiKey: 'YOUR_API_KEY',
  baseUrl: 'http://localhost:8000'
});

const task = await client.execute({
  instruction: 'Busca "AI" en Google',
  options: { screenshot: true }
});

const result = await client.waitForCompletion(task.task_id);
console.log(result);
```

### Python

```bash
pip install aery-sdk
```

```python
from aery import AeryClient

client = AeryClient(
    api_key='YOUR_API_KEY',
    base_url='http://localhost:8000'
)

task = client.execute(
    instruction='Busca "AI" en Google',
    options={'screenshot': True}
)

result = client.wait_for_completion(task['task_id'])
print(result)
```

### cURL

```bash
# Ejecutar tarea
curl -X POST http://localhost:8000/api/v1/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "instruction": "Busca AI en Google",
    "options": {"screenshot": true}
  }'

# Verificar estado
curl -H "Authorization: Bearer YOUR_API_KEY" \
  http://localhost:8000/api/v1/task/TASK_ID
```

## Webhooks

Puedes configurar webhooks para recibir notificaciones cuando las tareas se completen:

```json
{
  "webhook_url": "https://your-app.com/webhook",
  "events": ["task.completed", "task.failed"]
}
```

El payload del webhook será:

```json
{
  "event": "task.completed",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "timestamp": "2024-01-15T10:25:45Z",
  "data": {
    // Datos completos de la tarea
  }
}
```

## Límites y Consideraciones

- **Timeout máximo**: 5 minutos por tarea
- **Tamaño de instrucción**: Máximo 10,000 caracteres
- **Screenshots**: Máximo 10 por tarea
- **Archivos descargados**: Máximo 100MB por tarea
- **Retención de datos**: 30 días para plan gratuito, 1 año para planes pagos

## Soporte

Para soporte técnico:
- Email: <EMAIL>
- Discord: https://discord.gg/aery
- Documentación: https://docs.aery.ai