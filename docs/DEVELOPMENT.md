# AERY Development Guide

This guide covers everything you need to know for developing with AERY.

## 🚀 Quick Start

### Super Quick Setup (Recommended)

```bash
# Clone and setup in one command
git clone https://github.com/your-org/aery.git && cd aery && make dev-setup

# Or use the interactive script
./scripts/quick-start.sh
```

### Manual Setup

```bash
# 1. Clone repository
git clone https://github.com/your-org/aery.git
cd aery

# 2. Setup environment
cp .env.example .env
# Edit .env with your configuration

# 3. Start development environment
make dev          # Standard Docker
make dev-orbstack # OrbStack optimized (macOS)
```

## 🏗️ Project Structure

```
aery/
├── client/                    # Frontend Application
│   ├── routes/               # Fresh routes
│   ├── src/
│   │   ├── components/       # Reusable UI components
│   │   ├── hooks/            # Custom React hooks
│   │   ├── utils/            # Client utilities
│   │   └── styles/           # Styling (Twind)
│   ├── static/               # Static assets
│   └── main.ts               # Client entry point
│
├── server/
│   ├── gateway/              # API Gateway
│   │   ├── routes/           # API endpoints
│   │   │   ├── auth/         # Authentication routes
│   │   │   ├── tasks/        # Task management
│   │   │   └── prescripts/   # Pre-script management
│   │   ├── src/
│   │   │   ├── lib/          # Core libraries
│   │   │   ├── middleware/   # Express-like middleware
│   │   │   └── utils/        # Gateway utilities
│   │   └── main.ts           # Gateway entry point
│   │
│   └── workers/              # Python Workers
│       ├── src/
│       │   ├── automation/   # Browser automation
│       │   ├── ai/           # AI processing
│       │   └── utils/        # Worker utilities
│       ├── requirements.txt  # Python dependencies
│       └── main.py           # Workers entry point
│
├── shared/                   # Shared Resources
│   ├── types/                # TypeScript type definitions
│   ├── config/               # Shared configuration
│   ├── database/             # Database schemas & migrations
│   └── utils/                # Shared utilities
│
├── infrastructure/           # Infrastructure Configuration
│   ├── docker/               # Docker configurations
│   ├── monitoring/           # Prometheus, Grafana configs
│   └── nginx/                # Nginx configurations
│
├── docs/                     # Documentation
├── scripts/                  # Utility scripts
└── tests/                    # Test files
```

## 🛠️ Development Commands

### Essential Commands

```bash
# Quick setup and start
make dev-setup    # One-time setup
make dev          # Start all services
make dev-logs     # View logs
make health       # Check service health
make stop         # Stop all services
```

### Individual Services

```bash
make client       # Start only client
make gateway      # Start only gateway  
make workers      # Start only workers
```

### Testing & Quality

```bash
make test         # Run all tests
make test-client  # Test client only
make test-gateway # Test gateway only
make test-workers # Test workers only
make lint         # Lint all code
make format       # Format all code
make check        # Run all checks
```

### Database Management

```bash
make db-reset     # Reset database
make db-migrate   # Run migrations
make db-seed      # Seed development data
```

## 🌐 Service URLs

| Service | URL | Description |
|---------|-----|-------------|
| Client | http://localhost:3000 | Frontend application |
| Gateway | http://localhost:8000 | API Gateway |
| PgAdmin | http://localhost:8080 | Database management |
| Redis UI | http://localhost:8081 | Redis management |
| MailHog | http://localhost:8025 | Email testing |
| Prometheus | http://localhost:9090 | Metrics (when enabled) |
| Grafana | http://localhost:3001 | Dashboards (when enabled) |

### Default Credentials

- **PgAdmin**: <EMAIL> / admin
- **Database**: aery / aery_dev_password
- **Grafana**: admin / admin

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```bash
# Application
NODE_ENV=development
JWT_SECRET=your-super-secret-jwt-key

# Database
DATABASE_URL=postgresql://aery:aery_dev_password@localhost:5432/aery

# Redis
REDIS_URL=redis://localhost:6379

# AI Services
ANTHROPIC_API_KEY=your-anthropic-api-key
OPENAI_API_KEY=your-openai-api-key

# Workers
WORKER_CONCURRENCY=3
PLAYWRIGHT_HEADLESS=true
```

### Docker Configurations

- `docker-compose.dev.yml` - Standard development
- `docker-compose.orbstack.yml` - OrbStack optimized (macOS)
- `docker-compose.yml` - Production

## 🧪 Testing

### Running Tests

```bash
# All tests
make test

# Individual services
cd client && deno test -A
cd server/gateway && deno test -A
cd server/workers && python -m pytest

# With coverage
cd server/workers && python -m pytest --cov=src
```

### Test Structure

```
tests/
├── client/           # Client tests
├── gateway/          # Gateway tests
├── workers/          # Worker tests
├── integration/      # Integration tests
└── e2e/             # End-to-end tests
```

## 🐛 Debugging

### Debug Ports

- Gateway: 9229 (Node.js debugger)
- Workers: 5678 (Python debugger)

### VS Code Debug Configuration

```json
{
  "name": "Debug Gateway",
  "type": "node",
  "request": "attach",
  "port": 9229,
  "address": "localhost",
  "localRoot": "${workspaceFolder}/server/gateway",
  "remoteRoot": "/app"
}
```

### Logs

```bash
# All services
make dev-logs

# Individual services
docker-compose -f docker-compose.dev.yml logs -f client
docker-compose -f docker-compose.dev.yml logs -f gateway
docker-compose -f docker-compose.dev.yml logs -f workers
```

## 📊 Monitoring

### Health Checks

```bash
# Quick health check
make health

# Manual checks
curl http://localhost:8000/health
curl http://localhost:3000
```

### Metrics

Enable monitoring stack:

```bash
make monitor
```

Access:
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3001

## 🔄 Development Workflow

### Typical Development Session

1. **Start**: `make dev`
2. **Develop**: Edit code (hot reload enabled)
3. **Test**: `make test`
4. **Debug**: Use debug ports or logs
5. **Stop**: `make stop`

### Making Changes

1. **Frontend**: Edit files in `client/` - auto-reload
2. **Gateway**: Edit files in `server/gateway/` - auto-reload
3. **Workers**: Edit files in `server/workers/` - manual restart
4. **Shared**: Edit files in `shared/` - restart affected services

### Adding Dependencies

```bash
# Client/Gateway (Deno)
# Edit import_map.json or use direct imports

# Workers (Python)
cd server/workers
pip install package-name
pip freeze > requirements.txt
```

## 🚀 Deployment

### Production Build

```bash
make build
docker-compose up -d
```

### Environment-specific Configs

- Development: `.env`
- Staging: `.env.staging`
- Production: `.env.production`

## 🤝 Contributing

### Code Style

- **TypeScript**: Follow Deno conventions
- **Python**: Follow PEP 8, use Black formatter
- **Commits**: Use conventional commits

### Pull Request Process

1. Create feature branch
2. Make changes
3. Run `make check`
4. Update documentation
5. Submit PR

## 🆘 Troubleshooting

### Common Issues

**Services won't start:**
```bash
make clean
make dev-setup
```

**Database connection issues:**
```bash
make db-reset
```

**Port conflicts:**
```bash
make stop
# Check for processes using ports 3000, 8000, 5432, 6379
```

**OrbStack issues (macOS):**
```bash
# Use standard Docker instead
make docker
```

### Getting Help

- Check logs: `make dev-logs`
- Health check: `make health`
- Reset everything: `make clean && make dev-setup`

## 📚 Additional Resources

- [API Documentation](API.md)
- [Architecture Guide](ARCHITECTURE.md)
- [Deployment Guide](DEPLOYMENT.md)
- [Contributing Guidelines](../CONTRIBUTING.md)
