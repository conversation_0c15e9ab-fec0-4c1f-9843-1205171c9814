# Gestión Dinámica de Modelos con PostgreSQL y Redis

## Resumen

Se ha implementado un sistema de gestión de modelos dinámico que migra desde archivos JSON estáticos a una solución basada en PostgreSQL con cache Redis. Esta implementación permite configuraciones más flexibles, mejor rendimiento y escalabilidad.

## Arquitectura

### Antes (Arquitectura Legacy)
```
Cliente Vue.js → FastAPI → model_config.py → archivo JSON
```

### Después (Nueva Arquitectura)
```
Cliente Vue.js → FastAPI → db_model_manager.py → PostgreSQL + Redis Cache
```

## Características Principales

### 🎯 **Gestión Dinámica de Modelos**
- Configuración de modelos via base de datos
- CRUD completo para modelos, proveedores y categorías
- Configuraciones específicas por agente
- Soporte para fallback automático

### ⚡ **Rendimiento con Redis**
- Cache inteligente con TTL configurable
- Invalidación automática de cache
- Reducción de consultas a base de datos

### 📊 **Métricas y Estadísticas**
- Seguimiento de uso por modelo
- Análisis de costos en tiempo real
- Métricas de rendimiento por agente

### 🔧 **Configuración Flexible**
- Parámetros específicos por modelo
- Configuraciones globales del sistema
- Variables de entorno para diferentes ambientes

## Estructura de Base de Datos

### Tablas Principales

#### `model_providers`
Proveedores de IA (OpenAI, Anthropic, etc.)
```sql
- id (UUID)
- name (VARCHAR) - openai, anthropic, etc.
- display_name (VARCHAR)
- api_base_url (VARCHAR)
- auth_type (VARCHAR) - api_key, oauth, bearer
- config (JSONB) - configuración específica
- rate_limit_rpm/tpm (INTEGER)
- enabled (BOOLEAN)
```

#### `model_categories`
Categorías de modelos (chat, embedding, etc.)
```sql
- id (UUID)
- name (VARCHAR) - chat, embedding, completion, image
- display_name (VARCHAR)
- description (TEXT)
- icon (VARCHAR) - para la UI
- enabled (BOOLEAN)
```

#### `ai_models`
Modelos de IA disponibles
```sql
- id (UUID)
- model_id (VARCHAR) - gpt-4, claude-3.5-sonnet, etc.
- name (VARCHAR)
- provider_id (UUID FK)
- category_id (UUID FK)
- context_length (INTEGER)
- supports_streaming/function_calling/vision (BOOLEAN)
- input_cost_per_1m/output_cost_per_1m (DECIMAL)
- default_parameters (temperature, top_p, etc.)
- enabled (BOOLEAN)
- features (JSONB)
- metadata (JSONB)
```

#### `agent_configurations`
Configuraciones específicas por agente
```sql
- id (UUID)
- agent_type (VARCHAR) - instruction_analyzer, action_planner, etc.
- primary_model_id (UUID FK)
- fallback_model_id (UUID FK)
- temperature/max_tokens/top_p (configuración específica)
- system_prompt (TEXT)
- enabled (BOOLEAN)
```

#### `model_usage_stats`
Estadísticas de uso y costos
```sql
- model_id (UUID FK)
- user_id (UUID FK)
- total_requests/tokens_input/tokens_output (INTEGER/BIGINT)
- total_cost (DECIMAL)
- success_rate/avg_response_time (métricas de rendimiento)
- date (DATE) - para agregación temporal
```

## API Endpoints

### Proveedores
- `GET /api/models/providers` - Lista proveedores
- `GET /api/models/providers?enabled_only=true` - Solo habilitados

### Categorías
- `GET /api/models/categories` - Lista categorías
- `GET /api/models/categories?enabled_only=true` - Solo habilitadas

### Modelos
- `GET /api/models` - Lista modelos con filtros opcionales
- `GET /api/models?provider=openai&category=chat&enabled=true`
- `GET /api/models/{model_id}` - Obtiene modelo específico
- `POST /api/models` - Crea nuevo modelo
- `PUT /api/models/{model_id}` - Actualiza modelo
- `DELETE /api/models/{model_id}` - Elimina modelo

### Configuración de Agentes
- `GET /api/models/agents` - Lista configuraciones de agentes
- `PUT /api/models/agents/{agent_type}` - Actualiza configuración

### Testing y Estadísticas
- `POST /api/models/test` - Prueba un modelo
- `GET /api/models/stats` - Estadísticas de uso

## Cache Redis

### Patrones de Claves
```
aery:models:providers:all
aery:models:providers:enabled
aery:models:categories:all
aery:models:models:all
aery:models:models:provider_openai
aery:models:models:category_chat
aery:models:model:{model_id}
aery:models:agent_config:{agent_type}
```

### TTL y Configuración
- TTL por defecto: 300 segundos (5 minutos)
- Invalidación automática en operaciones CUD
- Configuración via variables de entorno

## Instalación y Setup

### 1. Ejecutar Migración
```bash
cd /path/to/aery
python scripts/setup_dynamic_models.py
```

### 2. Variables de Entorno
```bash
# PostgreSQL
DATABASE_URL=postgresql://aery_user:aery_password@localhost:5432/aery_db

# Redis
REDIS_URL=redis://localhost:6379

# Configuración de cache
MODELS_CACHE_TTL=300
```

### 3. Docker Compose
Los servicios PostgreSQL y Redis ya están configurados en `docker-compose.dev.yml`.

## Frontend (Cliente Vue.js)

### Nuevo Store Enhanced
```typescript
// stores/models-enhanced.ts
import { useModelsStore } from '~/stores/models-enhanced'

const modelsStore = useModelsStore()

// Obtener modelos
await modelsStore.fetchModels()
await modelsStore.fetchProviders()
await modelsStore.fetchCategories()

// Configurar agentes
await modelsStore.updateAgentConfiguration('instruction_analyzer', {
  primary_model_id: 'gpt-4o-mini',
  temperature: 0.1
})
```

### Componentes Actualizados
Los componentes existentes (`ModelsCreateModal.vue`, `ModelsEditModal.vue`, etc.) pueden actualizarse gradualmente para usar la nueva API.

## Migración Gradual

### Compatibilidad Backward
- Los tipos existentes se mantienen como alias
- API endpoints legacy siguen funcionando
- Migración gradual de componentes

### Pasos de Migración
1. ✅ Ejecutar script de setup
2. ✅ Verificar que PostgreSQL y Redis estén funcionando
3. 🔄 Actualizar imports en el frontend para usar `models-enhanced.ts`
4. 🔄 Actualizar componentes uno por uno
5. 🔄 Deprecar API legacy gradualmente

## Beneficios de la Nueva Implementación

### 🚀 **Rendimiento**
- Cache Redis reduce latencia
- Queries optimizadas con índices
- Menos I/O de archivos

### 🔧 **Flexibilidad**
- Configuración runtime sin restart
- Parámetros específicos por modelo/agente
- Soporte para múltiples environments

### 📊 **Observabilidad**
- Métricas detalladas de uso
- Tracking de costos automático
- Análisis de rendimiento por modelo

### 🛡️ **Robustez**
- Transacciones ACID en PostgreSQL
- Fallback automático a modelos alternativos
- Validaciones a nivel de base de datos

## Configuración de Producción

### PostgreSQL
- Usar connection pooling (asyncpg.Pool)
- Configurar indices apropiados
- Backup y recovery automatizado

### Redis
- Configurar persistencia RDB/AOF
- Monitorear uso de memoria
- Clustering para alta disponibilidad

### Monitoreo
- Integración con Prometheus/Grafana
- Alertas por costos excesivos
- Métricas de cache hit ratio

## Próximos Pasos

1. **Implementar testing real de modelos** - Integrar con APIs reales
2. **Dashboard de métricas** - UI para visualizar estadísticas
3. **Auto-scaling de modelos** - Basado en uso y costos
4. **Multi-tenancy** - Configuraciones por usuario/organización
5. **A/B Testing** - Comparar rendimiento entre modelos

## Soporte y Mantenimiento

- **Logs**: Ver `server/workers/logs/` para debug
- **Backups**: Automáticos en `backups/models_backup_*.json`
- **Rollback**: Usar backup para revertir a configuración anterior
- **Health Checks**: Endpoints de estado en `/api/models/health`
