# AERY Deployment Guide

This guide covers deployment strategies for AERY across different environments.

## 🚀 Quick Deployment

### Development
```bash
make dev-setup    # One-time setup
make dev          # Start development environment
```

### Production
```bash
make build        # Build production images
make prod         # Start production environment
```

## 🏗️ Deployment Options

### 1. Docker Compose (Recommended for Small-Medium Scale)

**Advantages:**
- Simple setup and management
- Good for single-server deployments
- Easy to understand and debug
- Cost-effective for smaller workloads

**Use Cases:**
- Development environments
- Small production deployments
- Proof of concepts
- Single-server setups

### 2. Kubernetes (Recommended for Large Scale)

**Advantages:**
- Auto-scaling capabilities
- High availability
- Rolling updates
- Service discovery
- Resource management

**Use Cases:**
- Large production deployments
- Multi-region setups
- High-traffic applications
- Enterprise environments

### 3. Cloud Platforms

**Supported Platforms:**
- AWS (ECS, EKS, EC2)
- Google Cloud (GKE, Cloud Run)
- Azure (AKS, Container Instances)
- DigitalOcean (App Platform, Kubernetes)

## 🐳 Docker Compose Deployment

### Production Setup

1. **Prepare Environment**
```bash
# Clone repository
git clone https://github.com/your-org/aery.git
cd aery

# Create production environment file
cp .env.example .env.production
# Edit .env.production with production values
```

2. **Configure Environment Variables**
```bash
# Required production variables
ENVIRONMENT=production
DATABASE_URL=***************************************/aery
REDIS_URL=redis://redis-host:6379
JWT_SECRET=your-super-secure-jwt-secret
ANTHROPIC_API_KEY=your-anthropic-api-key
OPENAI_API_KEY=your-openai-api-key

# SSL Configuration
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# Monitoring
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
```

3. **Deploy Services**
```bash
# Build production images
docker-compose -f docker-compose.yml build

# Start production services
docker-compose -f docker-compose.yml up -d

# Verify deployment
make health
```

### SSL/TLS Configuration

**Option 1: Let's Encrypt with Nginx**
```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

**Option 2: Custom SSL Certificate**
```bash
# Place certificates in infrastructure/nginx/ssl/
mkdir -p infrastructure/nginx/ssl/
cp your-cert.pem infrastructure/nginx/ssl/
cp your-key.pem infrastructure/nginx/ssl/
```

## ☸️ Kubernetes Deployment

### Prerequisites
- Kubernetes cluster (1.20+)
- kubectl configured
- Helm 3.x installed

### 1. Namespace Setup
```bash
kubectl create namespace aery
kubectl config set-context --current --namespace=aery
```

### 2. Secrets Configuration
```bash
# Create database secret
kubectl create secret generic aery-db-secret \
  --from-literal=username=aery \
  --from-literal=password=your-secure-password

# Create API keys secret
kubectl create secret generic aery-api-keys \
  --from-literal=jwt-secret=your-jwt-secret \
  --from-literal=anthropic-key=your-anthropic-key \
  --from-literal=openai-key=your-openai-key
```

### 3. Storage Configuration
```yaml
# storage.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
```

### 4. Database Deployment
```yaml
# postgres.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: aery
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: aery-db-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: aery-db-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
```

### 5. Application Deployment
```yaml
# gateway.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: gateway
  template:
    metadata:
      labels:
        app: gateway
    spec:
      containers:
      - name: gateway
        image: aery/gateway:latest
        env:
        - name: DATABASE_URL
          value: ****************************************************/aery
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: aery-db-secret
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: aery-db-secret
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: aery-api-keys
              key: jwt-secret
        ports:
        - containerPort: 8000
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 6. Service Configuration
```yaml
# services.yaml
apiVersion: v1
kind: Service
metadata:
  name: gateway-service
spec:
  selector:
    app: gateway
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
```

### 7. Ingress Configuration
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: aery-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.aery.dev
    secretName: aery-tls
  rules:
  - host: api.aery.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway-service
            port:
              number: 80
```

## ☁️ Cloud Platform Deployment

### AWS ECS

1. **Create Task Definition**
```json
{
  "family": "aery-gateway",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "gateway",
      "image": "your-account.dkr.ecr.region.amazonaws.com/aery-gateway:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:aery/database-url"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/aery-gateway",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

2. **Create Service**
```bash
aws ecs create-service \
  --cluster aery-cluster \
  --service-name aery-gateway \
  --task-definition aery-gateway:1 \
  --desired-count 2 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
```

### Google Cloud Run

```bash
# Build and push image
gcloud builds submit --tag gcr.io/PROJECT-ID/aery-gateway

# Deploy service
gcloud run deploy aery-gateway \
  --image gcr.io/PROJECT-ID/aery-gateway \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars ENVIRONMENT=production \
  --set-secrets DATABASE_URL=aery-db-url:latest
```

## 📊 Monitoring Setup

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'aery-gateway'
    static_configs:
      - targets: ['gateway:8000']
    metrics_path: /metrics

  - job_name: 'aery-workers'
    static_configs:
      - targets: ['workers:5678']
    metrics_path: /metrics
```

### Grafana Dashboards
- Application metrics
- Infrastructure metrics
- Business metrics
- Alert configurations

## 🔒 Security Considerations

### Network Security
- Use private networks
- Configure firewalls
- Enable VPN access
- Implement WAF

### Data Security
- Encrypt data at rest
- Use TLS for all communications
- Rotate secrets regularly
- Implement backup encryption

### Access Control
- Use IAM roles
- Implement RBAC
- Enable audit logging
- Monitor access patterns

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Build and push images
      run: |
        docker build -t aery/gateway:${{ github.sha }} server/gateway/
        docker push aery/gateway:${{ github.sha }}
    
    - name: Deploy to Kubernetes
      run: |
        kubectl set image deployment/gateway gateway=aery/gateway:${{ github.sha }}
        kubectl rollout status deployment/gateway
```

## 🚨 Backup and Recovery

### Database Backup
```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
pg_dump $DATABASE_URL > $BACKUP_DIR/aery_backup_$DATE.sql

# Upload to S3
aws s3 cp $BACKUP_DIR/aery_backup_$DATE.sql s3://aery-backups/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "aery_backup_*.sql" -mtime +30 -delete
```

### Disaster Recovery
1. **RTO (Recovery Time Objective)**: < 1 hour
2. **RPO (Recovery Point Objective)**: < 15 minutes
3. **Backup Strategy**: Daily full, hourly incremental
4. **Testing**: Monthly recovery drills

## 📈 Scaling Strategies

### Horizontal Scaling
- Gateway: Scale based on CPU/memory
- Workers: Scale based on queue length
- Database: Read replicas for read-heavy workloads

### Vertical Scaling
- Increase container resources
- Optimize database configuration
- Tune application parameters

### Auto-scaling Configuration
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gateway-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

## 🆘 Troubleshooting

### Common Issues
1. **Database connection failures**
2. **Memory leaks in workers**
3. **High response times**
4. **Queue backlog**

### Debugging Tools
- Application logs
- Metrics dashboards
- Health check endpoints
- Database query analysis

### Emergency Procedures
1. **Service degradation**: Scale up resources
2. **Database issues**: Switch to read replica
3. **Security incident**: Isolate affected services
4. **Data corruption**: Restore from backup

## 📚 Additional Resources

- [Development Guide](DEVELOPMENT.md)
- [API Documentation](API.md)
- [Architecture Overview](ARCHITECTURE.md)
- [Security Best Practices](SECURITY.md)
