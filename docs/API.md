# AERY API Documentation

Complete API reference for the AERY Browser Automation platform.

## 🔗 Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://api.aery.dev`

## 🔐 Authentication

AERY uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

## 📋 API Endpoints

### Authentication

#### Register User

```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "plan": "free"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "plan": "free",
      "created_at": "2024-01-01T00:00:00Z"
    },
    "token": "jwt-token-here"
  }
}
```

#### Login

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "plan": "free"
    },
    "token": "jwt-token-here"
  }
}
```

#### Refresh Token

```http
POST /auth/refresh
Authorization: Bearer <your-jwt-token>
```

#### Logout

```http
POST /auth/logout
Authorization: Bearer <your-jwt-token>
```

### Task Execution

#### Execute Task

```http
POST /execute
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "instruction": "Navigate to google.com and search for 'Deno framework'",
  "url": "https://google.com",
  "prescript_id": "optional-prescript-uuid",
  "options": {
    "timeout": 30000,
    "headless": true,
    "viewport": {
      "width": 1920,
      "height": 1080
    },
    "wait_for": "networkidle",
    "screenshot": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "task_id": "task-uuid",
    "status": "queued",
    "created_at": "2024-01-01T00:00:00Z",
    "estimated_completion": "2024-01-01T00:01:00Z"
  }
}
```

#### Get Task Status

```http
GET /tasks/{task_id}
Authorization: Bearer <your-jwt-token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "task-uuid",
    "status": "completed",
    "instruction": "Navigate to google.com...",
    "url": "https://google.com",
    "result": {
      "success": true,
      "screenshot": "base64-encoded-image",
      "extracted_data": {},
      "actions_performed": [
        {
          "type": "navigate",
          "url": "https://google.com",
          "timestamp": "2024-01-01T00:00:30Z"
        },
        {
          "type": "fill",
          "selector": "input[name='q']",
          "value": "Deno framework",
          "timestamp": "2024-01-01T00:00:35Z"
        }
      ]
    },
    "created_at": "2024-01-01T00:00:00Z",
    "completed_at": "2024-01-01T00:01:00Z",
    "duration_ms": 60000
  }
}
```

#### List Tasks

```http
GET /tasks?status=completed&limit=10&offset=0&sort=created_at&order=desc
Authorization: Bearer <your-jwt-token>
```

**Query Parameters:**
- `status`: `queued`, `running`, `completed`, `failed`
- `limit`: Number of results (max 100)
- `offset`: Pagination offset
- `sort`: Sort field (`created_at`, `completed_at`, `duration_ms`)
- `order`: Sort order (`asc`, `desc`)

#### Cancel Task

```http
DELETE /tasks/{task_id}
Authorization: Bearer <your-jwt-token>
```

### Pre-scripts

#### List Pre-scripts

```http
GET /prescripts?category=ecommerce&active=true&limit=20
Authorization: Bearer <your-jwt-token>
```

**Query Parameters:**
- `category`: Filter by category
- `active`: Filter by active status
- `search`: Search in name/description
- `limit`: Number of results

#### Get Pre-script

```http
GET /prescripts/{prescript_id}
Authorization: Bearer <your-jwt-token>
```

#### Create Pre-script

```http
POST /prescripts
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "Login to Dashboard",
  "description": "Automated login flow for admin dashboard",
  "category": "authentication",
  "actions": [
    {
      "type": "navigate",
      "url": "{{base_url}}/login"
    },
    {
      "type": "fill",
      "selector": "#email",
      "value": "{{email}}"
    },
    {
      "type": "fill",
      "selector": "#password",
      "value": "{{password}}"
    },
    {
      "type": "click",
      "selector": "button[type=submit]"
    },
    {
      "type": "wait",
      "condition": "url_contains",
      "value": "/dashboard"
    }
  ],
  "variables": [
    {
      "name": "base_url",
      "type": "string",
      "required": true,
      "description": "Base URL of the application"
    },
    {
      "name": "email",
      "type": "string",
      "required": true,
      "description": "User email address"
    },
    {
      "name": "password",
      "type": "string",
      "required": true,
      "description": "User password"
    }
  ]
}
```

#### Update Pre-script

```http
PUT /prescripts/{prescript_id}
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

#### Delete Pre-script

```http
DELETE /prescripts/{prescript_id}
Authorization: Bearer <your-jwt-token>
```

### User Management

#### Get User Profile

```http
GET /user/profile
Authorization: Bearer <your-jwt-token>
```

#### Update User Profile

```http
PUT /user/profile
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "John Doe",
  "preferences": {
    "default_timeout": 30000,
    "default_headless": true
  }
}
```

#### Get Usage Statistics

```http
GET /user/usage?period=month
Authorization: Bearer <your-jwt-token>
```

### System

#### Health Check

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "workers": "healthy"
  },
  "version": "1.0.0"
}
```

#### System Metrics

```http
GET /metrics
Authorization: Bearer <your-jwt-token>
```

## 📊 Response Format

All API responses follow this format:

```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Response

```json
{
  "success": false,
  "data": null,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "details": {
      "field": "email",
      "value": "invalid-email"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🚫 Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `RATE_LIMITED` | 429 | Rate limit exceeded |
| `INTERNAL_ERROR` | 500 | Internal server error |
| `SERVICE_UNAVAILABLE` | 503 | Service temporarily unavailable |

## 🔒 Rate Limits

| Plan | Requests/15min | Concurrent Tasks |
|------|----------------|------------------|
| Free | 10 | 1 |
| Pro | 100 | 3 |
| Enterprise | 1000 | 10 |

Rate limit headers:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## 🎯 Action Types

### Navigation Actions
- `navigate`: Navigate to URL
- `go_back`: Go back in history
- `go_forward`: Go forward in history
- `reload`: Reload page

### Interaction Actions
- `click`: Click element
- `double_click`: Double click element
- `right_click`: Right click element
- `hover`: Hover over element
- `focus`: Focus element

### Input Actions
- `fill`: Fill input field
- `clear`: Clear input field
- `select`: Select dropdown option
- `upload`: Upload file
- `press_key`: Press keyboard key

### Wait Actions
- `wait`: Wait for time/element/condition
- `wait_for_element`: Wait for element to appear
- `wait_for_text`: Wait for text to appear
- `wait_for_url`: Wait for URL change

### Data Actions
- `extract`: Extract data from page
- `screenshot`: Take screenshot
- `pdf`: Generate PDF
- `scroll`: Scroll page

### Custom Actions
- `javascript`: Execute custom JavaScript
- `python`: Execute custom Python code

## 📝 Examples

### Complete Task Example

```bash
# 1. Register user
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "plan": "free"
  }'

# 2. Execute task
curl -X POST http://localhost:8000/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "instruction": "Go to example.com and take a screenshot",
    "url": "https://example.com",
    "options": {
      "screenshot": true,
      "timeout": 30000
    }
  }'

# 3. Check task status
curl http://localhost:8000/tasks/TASK_ID \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔧 SDKs and Libraries

- **JavaScript/TypeScript**: `@aery/sdk-js`
- **Python**: `aery-python`
- **Go**: `github.com/aery/go-sdk`
- **PHP**: `aery/php-sdk`

## 📚 Additional Resources

- [Development Guide](DEVELOPMENT.md)
- [Architecture Overview](ARCHITECTURE.md)
- [Deployment Guide](DEPLOYMENT.md)
