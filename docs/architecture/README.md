# AERY - Arquitectura del Sistema

Documentación completa de la arquitectura de AERY (AI-Enhanced Robotic Yielding), un sistema de automatización de navegador web con inteligencia artificial.

## Tabla de Contenidos

- [Visión General](#visión-general)
- [Arquitectura de Alto Nivel](#arquitectura-de-alto-nivel)
- [Componentes del Sistema](#componentes-del-sistema)
- [Flujo de Datos](#flujo-de-datos)
- [Tecnologías Utilizadas](#tecnologías-utilizadas)
- [Patrones de Diseño](#patrones-de-diseño)
- [Escalabilidad](#escalabilidad)
- [Seguridad](#seguridad)
- [Monitoreo y Observabilidad](#monitoreo-y-observabilidad)

## Visión General

AERY es un sistema distribuido que permite automatizar navegadores web mediante instrucciones en lenguaje natural. El sistema utiliza inteligencia artificial para interpretar las instrucciones del usuario y ejecutar las acciones correspondientes en el navegador.

### Características Principales

- **Procesamiento de Lenguaje Natural**: Interpreta instrucciones en español e inglés
- **Automatización Inteligente**: Utiliza IA para planificar y ejecutar acciones
- **Escalabilidad Horizontal**: Múltiples workers para procesamiento paralelo
- **Self-Healing**: Recuperación automática de errores
- **Pre-scripts Optimizados**: Cache inteligente de acciones comunes
- **Monitoreo en Tiempo Real**: Métricas y observabilidad completa

## Arquitectura de Alto Nivel

```mermaid
graph TB
    Client[Cliente] --> LB[Load Balancer/Nginx]
    LB --> GW[Deno Gateway]
    
    GW --> Redis[(Redis)]
    GW --> PG[(PostgreSQL)]
    
    Redis --> W1[Worker 1]
    Redis --> W2[Worker 2]
    Redis --> W3[Worker N]
    
    W1 --> AI[OpenRouter/AI]
    W2 --> AI
    W3 --> AI
    
    W1 --> Browser1[Playwright/Browser]
    W2 --> Browser2[Playwright/Browser]
    W3 --> Browser3[Playwright/Browser]
    
    GW --> Metrics[Prometheus]
    Metrics --> Grafana[Grafana]
    
    subgraph "Monitoring Stack"
        Metrics
        Grafana
        AlertManager[Alert Manager]
    end
    
    subgraph "Storage Layer"
        Redis
        PG
        Files[File Storage]
    end
```

## Componentes del Sistema

### 1. Deno Gateway

**Tecnología**: Deno + Fresh Framework
**Puerto**: 8000
**Responsabilidades**:
- API REST para clientes
- Autenticación y autorización
- Rate limiting
- Validación de requests
- Gestión de colas de tareas
- Servir archivos estáticos

**Estructura**:
```
gateway/
├── routes/           # Rutas de la API
│   ├── api/v1/      # Endpoints v1
│   └── static/      # Archivos estáticos
├── middleware/      # Middlewares
├── utils/          # Utilidades
├── islands/        # Componentes interactivos
└── components/     # Componentes UI
```

### 2. Python Workers

**Tecnología**: Python 3.11 + AsyncIO
**Responsabilidades**:
- Procesamiento de tareas de automatización
- Coordinación de agentes IA
- Ejecución de acciones en navegador
- Self-healing y recuperación de errores
- Generación de pre-scripts

**Arquitectura Interna**:
```
workers/
├── agents/          # Agentes IA especializados
│   ├── coordinator.py    # Coordinador principal
│   ├── analyzer.py      # Analizador de instrucciones
│   ├── planner.py       # Planificador de acciones
│   └── validator.py     # Validador de resultados
├── automation/      # Motor de automatización
│   ├── browser_engine.py # Motor Playwright
│   └── actions.py       # Acciones específicas
└── utils/          # Utilidades y configuración
```

### 3. Sistema de Colas (Redis)

**Tecnología**: Redis 7
**Responsabilidades**:
- Gestión de colas de tareas
- Cache de resultados
- Almacenamiento de sesiones
- Pub/Sub para notificaciones
- Métricas en tiempo real

**Colas Principales**:
- `queue:fast_execution` - Tareas con pre-scripts optimizados
- `queue:full_processing` - Tareas que requieren procesamiento IA completo
- `queue:failed` - Tareas fallidas para retry
- `queue:priority` - Tareas de alta prioridad

### 4. Base de Datos (PostgreSQL)

**Tecnología**: PostgreSQL 15
**Responsabilidades**:
- Almacenamiento persistente de datos
- Gestión de usuarios y API keys
- Historial de tareas
- Métricas y auditoría
- Pre-scripts optimizados

**Esquemas**:
- `aery` - Datos principales de la aplicación
- `metrics` - Métricas y estadísticas
- `audit` - Logs de auditoría

### 5. Proxy Reverso (Nginx)

**Tecnología**: Nginx
**Responsabilidades**:
- Load balancing
- SSL termination
- Rate limiting
- Compresión
- Servir archivos estáticos
- Proxy para servicios internos

### 6. Monitoreo (Prometheus + Grafana)

**Tecnologías**: Prometheus, Grafana, AlertManager
**Responsabilidades**:
- Recolección de métricas
- Visualización de dashboards
- Alertas automáticas
- Análisis de performance

## Flujo de Datos

### 1. Flujo de Ejecución de Tarea

```mermaid
sequenceDiagram
    participant C as Cliente
    participant GW as Gateway
    participant R as Redis
    participant W as Worker
    participant AI as OpenRouter
    participant B as Browser
    
    C->>GW: POST /execute
    GW->>GW: Validar API key
    GW->>GW: Generar task_id
    GW->>GW: Calcular hash instrucción
    
    alt Pre-script disponible
        GW->>R: Encolar en fast_execution
    else Sin pre-script
        GW->>R: Encolar en full_processing
    end
    
    GW->>C: Respuesta con task_id
    
    W->>R: Obtener tarea de cola
    W->>AI: Analizar instrucción
    AI->>W: Plan de acciones
    
    W->>B: Ejecutar acciones
    B->>W: Resultados
    
    W->>R: Guardar resultado
    W->>GW: Notificar completado
    
    C->>GW: GET /task/{id}
    GW->>R: Obtener resultado
    GW->>C: Respuesta con resultado
```

### 2. Flujo de Procesamiento IA

```mermaid
graph TD
    A[Instrucción Usuario] --> B[Analizador IA]
    B --> C{¿Pre-script disponible?}
    
    C -->|Sí| D[Ejecutar Pre-script]
    C -->|No| E[Planificador IA]
    
    E --> F[Selector de Elementos]
    F --> G[Ejecutor de Acciones]
    
    D --> H[Validador]
    G --> H
    
    H --> I{¿Éxito?}
    I -->|Sí| J[Guardar Resultado]
    I -->|No| K[Self-Healing]
    
    K --> L{¿Recuperable?}
    L -->|Sí| E
    L -->|No| M[Error Final]
    
    J --> N[Generar Pre-script]
    N --> O[Cache para futuro uso]
```

## Tecnologías Utilizadas

### Backend

| Componente | Tecnología | Versión | Propósito |
|------------|------------|---------|----------|
| Gateway | Deno + Fresh | 1.40+ | API y Frontend |
| Workers | Python | 3.11+ | Procesamiento |
| IA Framework | PocketFlow | Latest | Coordinación de agentes |
| Browser Engine | Playwright | Latest | Automatización web |
| API IA | OpenRouter | - | Modelos de IA |

### Infraestructura

| Componente | Tecnología | Versión | Propósito |
|------------|------------|---------|----------|
| Base de Datos | PostgreSQL | 15+ | Almacenamiento persistente |
| Cache/Colas | Redis | 7+ | Cache y colas |
| Proxy | Nginx | Latest | Load balancer |
| Contenedores | Docker | Latest | Containerización |
| Orquestación | Docker Compose | Latest | Orquestación local |

### Monitoreo

| Componente | Tecnología | Versión | Propósito |
|------------|------------|---------|----------|
| Métricas | Prometheus | Latest | Recolección de métricas |
| Dashboards | Grafana | Latest | Visualización |
| Alertas | AlertManager | Latest | Gestión de alertas |
| Logs | Structured Logging | - | Logging estructurado |

## Patrones de Diseño

### 1. Microservicios

- **Gateway**: Punto de entrada único
- **Workers**: Servicios de procesamiento especializados
- **Comunicación**: Asíncrona via colas Redis
- **Datos**: Base de datos compartida con esquemas separados

### 2. CQRS (Command Query Responsibility Segregation)

- **Commands**: Ejecución de tareas (Workers)
- **Queries**: Consulta de estado (Gateway)
- **Separación**: Diferentes modelos para lectura y escritura

### 3. Event Sourcing

- **Eventos**: Todas las acciones se registran como eventos
- **Reconstrucción**: Estado se puede reconstruir desde eventos
- **Auditoría**: Trazabilidad completa de acciones

### 4. Circuit Breaker

- **Protección**: Previene cascada de fallos
- **Recuperación**: Recuperación automática de servicios
- **Monitoreo**: Métricas de salud de servicios

### 5. Retry Pattern

- **Reintentos**: Reintentos automáticos con backoff exponencial
- **Dead Letter Queue**: Cola para tareas que fallan repetidamente
- **Límites**: Límites configurables de reintentos

## Escalabilidad

### Escalabilidad Horizontal

#### Workers
- **Stateless**: Workers sin estado para fácil escalado
- **Auto-scaling**: Escalado automático basado en carga de colas
- **Load Distribution**: Distribución automática de carga

#### Gateway
- **Multiple Instances**: Múltiples instancias detrás de load balancer
- **Session Affinity**: No requerida (stateless)
- **Health Checks**: Verificaciones de salud automáticas

#### Base de Datos
- **Read Replicas**: Réplicas de lectura para consultas
- **Connection Pooling**: Pool de conexiones optimizado
- **Partitioning**: Particionado por fecha para métricas

### Optimizaciones de Performance

#### Cache Strategy
```
L1: Redis (Hot data) - TTL: 1 hora
L2: PostgreSQL (Warm data) - TTL: 1 día
L3: File Storage (Cold data) - TTL: 30 días
```

#### Pre-scripts
- **Generación**: Automática basada en patrones exitosos
- **Cache**: Redis con TTL de 24 horas
- **Invalidación**: Automática en caso de fallos

#### Connection Pooling
- **Redis**: Pool de 10-50 conexiones por worker
- **PostgreSQL**: Pool de 5-20 conexiones por instancia
- **HTTP**: Keep-alive para APIs externas

## Seguridad

### Autenticación y Autorización

#### API Keys
- **Generación**: Cryptographically secure random
- **Almacenamiento**: Hash SHA-256 en base de datos
- **Rotación**: Automática cada 90 días
- **Scopes**: Permisos granulares por API key

#### Rate Limiting
```
Nivel 1: Nginx (IP-based)
Nivel 2: Gateway (API key-based)
Nivel 3: Redis (Sliding window)
```

### Seguridad de Datos

#### Encriptación
- **En Tránsito**: TLS 1.3 para todas las comunicaciones
- **En Reposo**: Encriptación de base de datos
- **API Keys**: Hash irreversible con salt

#### Aislamiento
- **Containers**: Cada worker en contenedor aislado
- **Networks**: Redes Docker separadas
- **Usuarios**: Usuarios no-root en contenedores

### Validación y Sanitización

#### Input Validation
- **Schema Validation**: Zod para validación de tipos
- **Sanitización**: Limpieza de inputs maliciosos
- **Límites**: Límites estrictos de tamaño y formato

#### Output Sanitization
- **XSS Prevention**: Sanitización de outputs HTML
- **Data Leakage**: Filtrado de información sensible
- **Error Messages**: Mensajes de error genéricos

## Monitoreo y Observabilidad

### Métricas (Prometheus)

#### Métricas de Sistema
```
# Métricas de tareas
aery_tasks_total{status="completed|failed|pending"}
aery_task_duration_seconds{percentile="50|95|99"}
aery_task_queue_size{queue="fast|full|failed"}

# Métricas de workers
aery_workers_active
aery_worker_cpu_usage{worker_id}
aery_worker_memory_usage{worker_id}

# Métricas de API
aery_api_requests_total{method,endpoint,status}
aery_api_request_duration_seconds{method,endpoint}
aery_api_rate_limit_hits{api_key}
```

#### Métricas de Negocio
```
# Métricas de usuarios
aery_users_active_daily
aery_api_keys_active
aery_usage_by_plan{plan="free|basic|pro|enterprise"}

# Métricas de IA
aery_ai_requests_total{model,provider}
aery_ai_cost_total{model,provider}
aery_prescript_hit_rate
```

### Logging

#### Structured Logging
```json
{
  "timestamp": "2024-01-15T10:25:00Z",
  "level": "INFO",
  "service": "worker-1",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "user_id": "user_123",
  "action": "browser_action",
  "details": {
    "type": "click",
    "selector": "button.submit",
    "success": true,
    "duration_ms": 150
  }
}
```

#### Log Levels
- **ERROR**: Errores que requieren atención inmediata
- **WARN**: Situaciones anómalas pero recuperables
- **INFO**: Eventos importantes del sistema
- **DEBUG**: Información detallada para debugging

### Alertas

#### Alertas Críticas
- **Sistema Down**: Servicios principales no responden
- **High Error Rate**: Tasa de error > 5%
- **Queue Overflow**: Colas con > 1000 tareas pendientes
- **Resource Exhaustion**: CPU/Memory > 90%

#### Alertas de Warning
- **Slow Response**: Tiempo de respuesta > 30s
- **Low Success Rate**: Tasa de éxito < 95%
- **Worker Offline**: Worker no envía heartbeat
- **Database Slow**: Consultas > 1s

### Dashboards (Grafana)

#### Dashboard Principal
- **Overview**: Estado general del sistema
- **Performance**: Métricas de rendimiento
- **Errors**: Análisis de errores
- **Usage**: Patrones de uso

#### Dashboard de Workers
- **Worker Status**: Estado de cada worker
- **Task Processing**: Procesamiento de tareas
- **Resource Usage**: Uso de recursos
- **AI Metrics**: Métricas de IA

#### Dashboard de Negocio
- **User Analytics**: Análisis de usuarios
- **Revenue Metrics**: Métricas de ingresos
- **Feature Usage**: Uso de características
- **Growth Metrics**: Métricas de crecimiento

## Deployment y DevOps

### Estrategia de Deployment

#### Desarrollo Local
```bash
# Desarrollo con hot reload
docker-compose -f docker-compose.dev.yml up

# Testing
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

#### Staging
```bash
# Deploy a staging
docker-compose -f docker-compose.staging.yml up -d

# Health checks
./scripts/health-check.sh staging
```

#### Producción
```bash
# Deploy con zero-downtime
./scripts/deploy.sh production

# Rollback si es necesario
./scripts/rollback.sh
```

### CI/CD Pipeline

```yaml
# .github/workflows/ci.yml
stages:
  - test
  - build
  - security-scan
  - deploy-staging
  - integration-tests
  - deploy-production
```

### Backup y Recovery

#### Backup Strategy
- **PostgreSQL**: Backup diario con retención de 30 días
- **Redis**: Snapshot cada 6 horas
- **Artifacts**: Backup semanal a S3
- **Configuración**: Versionado en Git

#### Disaster Recovery
- **RTO**: 4 horas (Recovery Time Objective)
- **RPO**: 1 hora (Recovery Point Objective)
- **Failover**: Automático para servicios críticos
- **Testing**: Simulacros mensuales

## Consideraciones Futuras

### Escalabilidad
- **Kubernetes**: Migración a Kubernetes para mejor orquestación
- **Multi-region**: Deployment en múltiples regiones
- **CDN**: Content Delivery Network para assets estáticos

### Funcionalidades
- **Mobile Automation**: Soporte para automatización móvil
- **API Webhooks**: Notificaciones en tiempo real
- **Custom Models**: Soporte para modelos IA personalizados
- **Workflow Builder**: Constructor visual de workflows

### Optimizaciones
- **Edge Computing**: Procesamiento en el edge
- **ML Optimization**: Optimización con machine learning
- **Caching Avanzado**: Cache distribuido inteligente
- **Compression**: Compresión avanzada de datos