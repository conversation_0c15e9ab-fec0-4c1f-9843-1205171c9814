# AERY Architecture Overview

This document provides a comprehensive overview of AERY's architecture, design decisions, and system components.

## 🏗️ High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        AERY Platform                           │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │   Client    │    │   Gateway   │    │   Workers   │        │
│  │  (Fresh)    │◄──►│   (Deno)    │◄──►│  (Python)   │        │
│  │             │    │             │    │             │        │
│  └─────────────┘    └─────────────┘    └─────────────┘        │
│                             │                                  │
│                             ▼                                  │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐        │
│  │ PostgreSQL  │    │    Redis    │    │ Monitoring  │        │
│  │ (Database)  │    │  (Cache)    │    │(Prometheus) │        │
│  └─────────────┘    └─────────────┘    └─────────────┘        │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🧩 Core Components

### 1. Client (Frontend)
- **Technology**: Fresh (Deno) + Preact + Twind
- **Purpose**: User interface and interaction
- **Location**: `client/`

**Key Features:**
- Server-side rendering (SSR)
- Real-time task monitoring
- Pre-script management
- User dashboard
- Responsive design

**Architecture:**
```
client/
├── routes/           # Fresh file-based routing
├── src/
│   ├── components/   # Reusable UI components
│   ├── hooks/        # Custom React hooks
│   ├── utils/        # Client-side utilities
│   └── styles/       # Twind styling
└── static/           # Static assets
```

### 2. Gateway (API Server)
- **Technology**: Deno + Fresh + TypeScript
- **Purpose**: API gateway and business logic
- **Location**: `server/gateway/`

**Key Features:**
- RESTful API endpoints
- JWT authentication
- Rate limiting
- Request validation
- Task orchestration
- WebSocket support

**Architecture:**
```
server/gateway/
├── routes/           # API endpoints
│   ├── auth/         # Authentication
│   ├── tasks/        # Task management
│   └── prescripts/   # Pre-script management
├── src/
│   ├── lib/          # Core libraries
│   ├── middleware/   # Request middleware
│   └── utils/        # Gateway utilities
└── main.ts           # Entry point
```

### 3. Workers (Automation Engine)
- **Technology**: Python + Playwright + FastAPI
- **Purpose**: Browser automation and AI processing
- **Location**: `server/workers/`

**Key Features:**
- Browser automation (Playwright)
- AI-powered task execution
- Multi-agent coordination
- Auto-healing mechanisms
- Concurrent task processing

**Architecture:**
```
server/workers/
├── src/
│   ├── automation/   # Browser automation
│   ├── ai/           # AI processing
│   ├── agents/       # Multi-agent system
│   └── utils/        # Worker utilities
├── requirements.txt  # Python dependencies
└── main.py           # Entry point
```

### 4. Shared Resources
- **Purpose**: Common types, utilities, and configurations
- **Location**: `shared/`

**Components:**
```
shared/
├── types/            # TypeScript definitions
├── config/           # Shared configuration
├── database/         # Database schemas
└── utils/            # Shared utilities
```

### 5. Infrastructure
- **Purpose**: Deployment and operational configurations
- **Location**: `infrastructure/`

**Components:**
```
infrastructure/
├── docker/           # Docker configurations
├── monitoring/       # Prometheus, Grafana
└── nginx/            # Reverse proxy configs
```

## 🔄 Data Flow

### 1. Task Execution Flow

```
User Request → Gateway → Queue → Workers → Database → Response
     ↓            ↓        ↓        ↓         ↓         ↓
  [Client]   [Validation] [Redis] [Playwright] [PostgreSQL] [Client]
```

**Detailed Steps:**
1. **Client** sends task request to Gateway
2. **Gateway** validates request and user permissions
3. **Gateway** queues task in Redis
4. **Workers** pick up task from queue
5. **Workers** execute browser automation
6. **Workers** store results in PostgreSQL
7. **Gateway** returns results to Client

### 2. Authentication Flow

```
Login Request → Gateway → Database → JWT Token → Client
      ↓           ↓          ↓          ↓         ↓
   [Credentials] [Validation] [User Check] [Token Gen] [Storage]
```

### 3. Real-time Updates

```
Task Status → Workers → Redis → Gateway → WebSocket → Client
     ↓          ↓        ↓        ↓          ↓         ↓
  [Progress]  [Update]  [Pub/Sub] [Broadcast] [Socket] [UI Update]
```

## 🗄️ Database Design

### PostgreSQL Schema

**Core Tables:**
- `users` - User accounts and profiles
- `tasks` - Task execution records
- `prescripts` - Pre-defined automation scripts
- `task_results` - Task execution results
- `user_sessions` - Active user sessions
- `api_keys` - API key management
- `metrics` - System and user metrics

**Key Relationships:**
```sql
users (1) ──── (N) tasks
users (1) ──── (N) prescripts
tasks (1) ──── (1) task_results
users (1) ──── (N) api_keys
```

### Redis Data Structures

**Queues:**
- `tasks:queue` - Task execution queue
- `tasks:priority` - High-priority tasks
- `tasks:retry` - Failed tasks for retry

**Cache:**
- `user:sessions:{id}` - User session data
- `prescripts:cache:{id}` - Cached pre-scripts
- `rate_limit:{user_id}` - Rate limiting counters

**Pub/Sub:**
- `task:updates` - Real-time task updates
- `system:events` - System-wide events

## 🔐 Security Architecture

### Authentication & Authorization
- **JWT Tokens**: Stateless authentication
- **API Keys**: Service-to-service authentication
- **Role-based Access**: User permissions
- **Rate Limiting**: Abuse prevention

### Data Protection
- **Encryption at Rest**: Database encryption
- **Encryption in Transit**: TLS/SSL
- **Input Validation**: Request sanitization
- **SQL Injection Prevention**: Parameterized queries

### Network Security
- **CORS Configuration**: Cross-origin protection
- **Reverse Proxy**: Nginx for SSL termination
- **Container Isolation**: Docker security
- **Secret Management**: Environment variables

## 🚀 Scalability Design

### Horizontal Scaling
- **Stateless Services**: Easy replication
- **Load Balancing**: Multiple gateway instances
- **Worker Scaling**: Dynamic worker pools
- **Database Sharding**: Future consideration

### Performance Optimization
- **Connection Pooling**: Database connections
- **Caching Strategy**: Redis for hot data
- **Async Processing**: Non-blocking operations
- **Resource Limits**: Container constraints

### Monitoring & Observability
- **Metrics Collection**: Prometheus
- **Log Aggregation**: Structured logging
- **Health Checks**: Service monitoring
- **Alerting**: Critical issue notifications

## 🔧 Technology Choices

### Frontend: Fresh + Preact
**Why:**
- Server-side rendering for SEO
- Lightweight React alternative
- Deno ecosystem integration
- TypeScript support

### Backend: Deno + Fresh
**Why:**
- Modern JavaScript runtime
- Built-in TypeScript support
- Secure by default
- Standard library quality

### Workers: Python + Playwright
**Why:**
- Mature browser automation
- Rich AI/ML ecosystem
- Excellent async support
- Cross-platform compatibility

### Database: PostgreSQL
**Why:**
- ACID compliance
- JSON support
- Excellent performance
- Rich ecosystem

### Cache: Redis
**Why:**
- High performance
- Pub/Sub capabilities
- Data structure variety
- Clustering support

## 🔄 Development Workflow

### Local Development
1. **Setup**: `make dev-setup`
2. **Development**: `make dev`
3. **Testing**: `make test`
4. **Deployment**: `make build`

### CI/CD Pipeline
1. **Code Push** → GitHub
2. **Tests Run** → GitHub Actions
3. **Build Images** → Docker Registry
4. **Deploy** → Production Environment

### Environment Management
- **Development**: Local Docker Compose
- **Staging**: Kubernetes cluster
- **Production**: Kubernetes cluster

## 📊 Monitoring Strategy

### Application Metrics
- Request/response times
- Error rates
- Task completion rates
- User activity

### Infrastructure Metrics
- CPU/Memory usage
- Database performance
- Queue lengths
- Network latency

### Business Metrics
- User registrations
- Task executions
- Revenue tracking
- Feature usage

## 🔮 Future Considerations

### Planned Enhancements
- **Microservices**: Service decomposition
- **Event Sourcing**: Audit trail
- **GraphQL**: Flexible API
- **Machine Learning**: Intelligent automation

### Scalability Roadmap
- **Multi-region**: Global deployment
- **CDN Integration**: Static asset delivery
- **Database Sharding**: Horizontal scaling
- **Caching Layers**: Performance optimization

## 📚 Additional Resources

- [Development Guide](DEVELOPMENT.md)
- [API Documentation](API.md)
- [Deployment Guide](DEPLOYMENT.md)
- [Contributing Guidelines](../CONTRIBUTING.md)
