# OrbStack Optimized Docker Compose
# Leverages OrbStack's fast file sharing and networking

services:

  # API Gateway (OrbStack Optimized)
  gateway:
    build:
      context: .
      dockerfile: ./server/gateway/Dockerfile
    container_name: aery_gateway_orbstack
    env_file:
      - .env
    ports:
      - "8000:8000"
    volumes:
      # OrbStack optimized volume mounts for hot reload
      - type: bind
        source: ./server/gateway
        target: /app
        consistency: cached
      - type: bind
        source: ./shared
        target: /app/shared
        consistency: cached
      - gateway_cache:/app/.deno
      # Exclude node_modules and cache for better performance
      - /app/node_modules
      - /app/.deno
    environment:
      - DATABASE_URL=***************************************************************/aery
      - REDIS_URL=redis://aery_redis_orbstack:6379
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - COHERE_API_KEY=${COHERE_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-placeholder_for_now}
      - API_SECRET_KEY=${API_SECRET_KEY:-dev_secret_key}
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret}
      - DENO_ENV=development
      - LOG_LEVEL=debug
      - ORBSTACK_OPTIMIZED=true
      - DENO_WATCH=true
    networks:
      aery_orbstack_network:
        aliases:
          - gateway.orb.local
          - api.orb.local
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    restart: unless-stopped
    command: ["deno", "task", "dev"]

  # Python Workers (OrbStack Optimized)
  workers:
    build:
      context: .
      dockerfile: ./server/workers/Dockerfile
    container_name: aery_workers_orbstack
    ports:
      - "5678:5678" # Debug port
    volumes:
      # OrbStack optimized volume mounts for hot reload
      - type: bind
        source: ./server/workers
        target: /app
        consistency: cached
      - type: bind
        source: ./shared
        target: /app/shared
        consistency: cached
      - type: bind
        source: ./artifacts
        target: /app/artifacts
        consistency: delegated
      - /dev/shm:/dev/shm
      - workers_cache:/app/.cache
      # Exclude cache directories for better performance
      - /app/__pycache__
      - /app/.pytest_cache
    environment:
      - REDIS_URL=redis://aery_redis_orbstack:6379
      - DATABASE_URL=***************************************************************/aery
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - PLAYWRIGHT_HEADLESS=false
      - PYTHON_ENV=development
      - PYTHONPATH=/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
      - ORBSTACK_OPTIMIZED=true
      - WATCHDOG_ENABLED=true
    networks:
      aery_orbstack_network:
        aliases:
          - workers.orb.local
    depends_on:
      redis:
        condition: service_started
      postgres:
        condition: service_healthy
    restart: unless-stopped
    command: ["python", "main.py"]

  # Alternative gateway service for debugging
  gateway-simple:
    build:
      context: .
      dockerfile: ./server/gateway/Dockerfile
    container_name: aery_gateway_simple_orbstack
    ports:
      - "8001:8000"
    volumes:
      - type: bind
        source: ./server/gateway
        target: /app
        consistency: cached
      - type: bind
        source: ./shared
        target: /app/shared
        consistency: cached
    environment:
      - DATABASE_URL=***************************************************************/aery
      - REDIS_URL=redis://aery_redis_orbstack:6379
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - API_SECRET_KEY=${API_SECRET_KEY:-dev_secret_key}
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret}
      - DENO_ENV=development
      - LOG_LEVEL=debug
    networks:
      aery_orbstack_network:
        aliases:
          - gateway-simple.orb.local
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    profiles:
      - debug
    command: ["deno", "task", "dev:simple"]

  # Alternative workers service for debugging  
  workers-simple:
    build:
      context: .
      dockerfile: ./server/workers/Dockerfile
    container_name: aery_workers_simple_orbstack
    ports:
      - "5679:5678"
    volumes:
      - type: bind
        source: ./server/workers
        target: /app
        consistency: cached
      - type: bind
        source: ./shared
        target: /app/shared
        consistency: cached
    environment:
      - REDIS_URL=redis://aery_redis_orbstack:6379
      - DATABASE_URL=***************************************************************/aery
      - PYTHON_ENV=development
      - PYTHONPATH=/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    networks:
      aery_orbstack_network:
        aliases:
          - workers-simple.orb.local
    depends_on:
      redis:
        condition: service_started
      postgres:
        condition: service_healthy
    profiles:
      - debug
    command: ["python", "main.py"]

  # PostgreSQL Database (OrbStack Optimized)
  postgres:
    image: postgres:15-alpine
    container_name: aery_postgres_orbstack
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=aery
      - POSTGRES_USER=aery
      - POSTGRES_PASSWORD=aery_dev_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_orbstack_data:/var/lib/postgresql/data
      - type: bind
        source: ./shared/database/init.sql
        target: /docker-entrypoint-initdb.d/01-init.sql
        consistency: cached
      - type: bind
        source: ./shared/database/dev-seed.sql
        target: /docker-entrypoint-initdb.d/02-dev-seed.sql
        consistency: cached
      - type: bind
        source: ./shared/database/models_schema.sql
        target: /docker-entrypoint-initdb.d/03-models-schema.sql
        consistency: cached
      - type: bind
        source: ./shared/database/models_seed.sql
        target: /docker-entrypoint-initdb.d/04-models-seed.sql
        consistency: cached
      - type: bind
        source: ./shared/database/agent-configs-seed.sql
        target: /docker-entrypoint-initdb.d/05-agent-configs-seed.sql
        consistency: cached

    networks:
      aery_orbstack_network:
        aliases:
          - postgres.orb.local
          - db.orb.local
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U aery -d aery"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: postgres -c log_statement=all -c log_destination=stderr

  # Redis (OrbStack Optimized)
  redis:
    image: redis:7-alpine
    container_name: aery_redis_orbstack
    ports:
      - "6379:6379"
    volumes:
      - redis_orbstack_data:/data
    networks:
      aery_orbstack_network:
        aliases:
          - redis.orb.local
          - cache.orb.local
    command: redis-server --appendonly yes --loglevel verbose
    restart: unless-stopped

  # Development Tools (OrbStack Optimized)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aery_redis_commander_orbstack
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis.orb.local:6379
    networks:
      aery_orbstack_network:
        aliases:
          - redis-ui.orb.local
    depends_on:
      - redis
    restart: unless-stopped





volumes:
  postgres_orbstack_data:
    driver: local
  redis_orbstack_data:
    driver: local

  gateway_cache:
    driver: local
  workers_cache:
    driver: local

networks:
  aery_orbstack_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: aery-orbstack
