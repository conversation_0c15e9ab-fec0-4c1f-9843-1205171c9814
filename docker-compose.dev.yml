# Development Docker Compose for OrbStack
# Optimized for fast development with hot reloading and debugging
services:

  # API Gateway (Development)
  gateway:
    build:
      context: .
      dockerfile: ./server/gateway/Dockerfile
    container_name: aery_gateway_dev
    ports:
      - "8000:8000"
      - "9229:9229" # Debug port
    environment:
      - DATABASE_URL=**************************************************/aery_db
      - REDIS_URL=redis://redis:6379
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - API_SECRET_KEY=${API_SECRET_KEY:-dev_secret_key}
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret}
      - DENO_ENV=development
      - LOG_LEVEL=debug
    volumes:
      - ./server/gateway:/app
      - ./shared:/app/shared
      - gateway_cache:/app/.deno
    networks:
      - aery_dev_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: ["deno", "task", "dev"]

  # Python Workers (Development)
  workers:
    build:
      context: .
      dockerfile: ./server/workers/Dockerfile
    container_name: aery_workers_dev
    ports:
      - "5678:5678" # Debug port
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=**************************************************/aery_db
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - PLAYWRIGHT_HEADLESS=false
      - PYTHON_ENV=development
      - PYTHONPATH=/app
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    volumes:
      - ./server/workers:/app
      - ./shared:/app/shared
      - ./artifacts:/app/artifacts
      - /dev/shm:/dev/shm
      - workers_cache:/app/.cache
    networks:
      - aery_dev_network
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    command: ["python", "-m", "debugpy", "--listen", "0.0.0.0:5678", "--wait-for-client", "main.py"]

  # PostgreSQL Database (Development)
  postgres:
    image: postgres:15-alpine
    container_name: aery_postgres_dev
    environment:
      POSTGRES_DB: aery_db
      POSTGRES_USER: aery_user
      POSTGRES_PASSWORD: aery_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./shared/database/init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./shared/database/dev-seed.sql:/docker-entrypoint-initdb.d/dev-seed.sql
    networks:
      - aery_dev_network
    restart: unless-stopped
    command: postgres -c log_statement=all -c log_destination=stderr

  # Redis (Development)
  redis:
    image: redis:7-alpine
    container_name: aery_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - aery_dev_network
    command: redis-server --appendonly yes --loglevel verbose
    restart: unless-stopped

  # Redis Commander (GUI for Redis)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aery_redis_commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    networks:
      - aery_dev_network
    depends_on:
      - redis
    restart: unless-stopped

  # pgAdmin (GUI for PostgreSQL)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aery_pgadmin
    ports:
      - "8080:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - aery_dev_network
    depends_on:
      - postgres
    restart: unless-stopped

  # Mailhog (Email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: aery_mailhog
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - aery_dev_network
    restart: unless-stopped

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_data:
    driver: local
  gateway_cache:
    driver: local
  workers_cache:
    driver: local

networks:
  aery_dev_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
